import js from '@eslint/js';
import tseslint from 'typescript-eslint';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
import globals from 'globals';
import simpleImportSort from 'eslint-plugin-simple-import-sort';
import unusedImports from 'eslint-plugin-unused-imports';

export default tseslint.config(
  js.configs.recommended,
  ...tseslint.configs.recommended,
  eslintPluginPrettierRecommended,

  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    ignores: ['packages/server/**/*'],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: 'module',
      parser: tseslint.parser,
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.commonjs,
        ...globals.es6,
        ...globals.jest,
        document: false,
        window: false,
      },
    },
    rules: {
      'no-plusplus': 'off', // 允许++ --
      'import/prefer-default-export': 'off', // 允许没有default export
      'no-param-reassign': 'off', // 允许对函数入参进行操作
      '@typescript-eslint/no-require-imports': 'off', // 可以使用require
      '@typescript-eslint/interface-name-prefix': 'off',
      '@typescript-eslint/explicit-member-accessibility': 'off',
      '@typescript-eslint/no-triple-slash-reference': 'off',
      '@typescript-eslint/ban-ts-ignore': 'off',
      '@typescript-eslint/no-this-alias': 'off',
      '@typescript-eslint/triple-slash-reference': [
        'error',
        {
          path: 'always',
          types: 'never',
          lib: 'never',
        },
      ],
      'no-useless-constructor': 'off',
      // 不考虑遍历定义顺序
      'no-use-before-define': 'off',
      // 允许console
      'no-console': 'off',
      // 因为sand项目中有node工程所以允许require，后续node项目改ts
      '@typescript-eslint/no-var-requires': 'off',
      // 允许使用@ts-nocheck 不校验ts文件
      '@typescript-eslint/ban-ts-comment': 'off',
      // 允许ts中方法实体为空
      '@typescript-eslint/no-empty-function': 'off',
      // 允许any
      '@typescript-eslint/no-explicit-any': 'off',
      // 允许！
      '@typescript-eslint/no-non-null-assertion': 'off',
    },
  },

  // React 专用配置 - 仅适用于 JSX/TSX 文件
  {
    files: ['**/*.{jsx,tsx}'],
    ignores: ['packages/server/**/*'],
    plugins: {
      react,
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    languageOptions: {
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react/static-property-placement': 'off', // 禁止propType放到class内部报错
      'react/state-in-constructor': 'off', // 允许state不在constructor中定义
      'react/forbid-prop-types': 'off', // prop-types允许使用object，any等
      'react/jsx-wrap-multilines': 'off', // 单个jsx元素外包裹括号
      'react/jsx-curly-newline': 'off', // 花括号在新行
      'react/jsx-props-no-spreading': 'off', // props允许解构
      // 允许JSX语法仅在jsx和tsx文件中
      'react/jsx-filename-extension': ['warn', { extensions: ['.jsx', '.tsx'] }],
      // JSX accessibility 规则
      'jsx-a11y/no-static-element-interactions': 'off',
      'jsx-a11y/img-has-alt': 'off',
      'jsx-a11y/anchor-is-valid': 'off',
      // 允许匿名组件
      'react/display-name': 'off',
      // 因为是 ts 忽略 react 属性类型
      'react/prop-types': 'off',
      // React 17+ 不需要在作用域中导入 React
      'react/react-in-jsx-scope': 'off',
    },
  },

  // 导入导出规则排序
  {
    files: ['apps/**/*.{ts,tsx,js,jsx}', 'packages/**/*.{ts,js}'],
    ignores: ['**/dist/**', '**/node_modules/**', '**/*.d.ts', 'packages/server/**/*'],
    plugins: {
      'simple-import-sort': simpleImportSort,
      'unused-imports': unusedImports,
    },
    rules: {
      'simple-import-sort/imports': 'warn',
      'simple-import-sort/exports': 'warn',
      'unused-imports/no-unused-imports': 'warn',
      'no-async-promise-executor': 'off',
    },
  },
);
