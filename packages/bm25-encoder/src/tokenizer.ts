import fs from 'fs';
import nodejieba from 'nodejieba';
import path from 'path';

import { Hash } from './hash';
import { getDirname } from './utils';

/**
 * 停用词管理类
 */
class StopWords {
  private _set: Set<string>;

  constructor(vocab: any = null) {
    this._set = new Set();

    if (vocab === true) {
      // 加载默认停用词
      const stopwordsPath = path.join(getDirname(), '../data/stopwords.txt');
      if (fs.existsSync(stopwordsPath)) {
        const content = fs.readFileSync(stopwordsPath, 'utf-8');
        content.split('\n').forEach((line) => {
          const word = line.trim();
          if (word) this._set.add(word);
        });
      }
    } else if (typeof vocab === 'string') {
      const filePath = fs.existsSync(vocab) ? vocab : path.join(getDirname(), vocab);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf-8');
        content.split('\n').forEach((line) => {
          const word = line.trim();
          if (word) this._set.add(word);
        });
      }
    } else if (Array.isArray(vocab) || vocab instanceof Set) {
      this._set = new Set(vocab);
    } else if (vocab && typeof vocab === 'object') {
      this._set = new Set(Object.keys(vocab));
    }
  }

  check(word: string): boolean {
    return this._set.has(word);
  }
}

interface TokenizerParameter {
  hashFunction?: (input: string) => number;
  stopWords?: any;
  lowerCase?: boolean;
  dictFile?: string | null;
  cutAll?: boolean;
  forSearch?: boolean;
  HMM?: boolean;
  usePaddle?: boolean;
}

/**
 * 基础分词器类
 */
export class BaseTokenizer {
  protected hashFunction: (input: string) => number;
  protected stopWords: any;
  protected lowerCase: boolean;
  protected dictFile: string | null;
  protected _stopWords: StopWords | null;
  protected kwargs: any;

  constructor({
    hashFunction = Hash.mmh3Hash,
    stopWords = null,
    lowerCase = false,
    dictFile = null,
    ...kwargs
  }: TokenizerParameter) {
    this.hashFunction = hashFunction;
    this.stopWords = stopWords;
    this.lowerCase = lowerCase;
    this.dictFile = dictFile;
    this._stopWords = stopWords ? new StopWords(stopWords) : null;
    this.kwargs = kwargs;
  }

  setStopwords(stopWords = true) {
    this.stopWords = stopWords;
    this._stopWords = stopWords ? new StopWords(stopWords) : null;
  }

  updateParameter({
    hashFunction = Hash.mmh3Hash,
    stopWords = null,
    lowerCase = false,
    dictFile = null,
    ...kwargs
  }: TokenizerParameter) {
    this.hashFunction = hashFunction;
    this.stopWords = stopWords;
    this.lowerCase = lowerCase;
    this.dictFile = dictFile;
    this._stopWords = stopWords ? new StopWords(stopWords) : null;
    this.kwargs = kwargs;
  }

  getParameter() {
    const data = {
      hash_function: this.hashFunction.name,
      stop_words: this.stopWords,
      lower_case: this.lowerCase,
      dict_file: this.dictFile,
    };
    return { ...data, ...this.kwargs };
  }

  _isStopWord(word: string): boolean {
    if (!this._stopWords) {
      return false;
    }
    return this._stopWords.check(word);
  }
}

/**
 * Jieba分词器实现类
 */
export class JiebaTokenizer extends BaseTokenizer {
  private cutAll: boolean;
  private forSearch: boolean;
  private HMM: boolean;
  private usePaddle: boolean;

  constructor({
    hashFunction = Hash.mmh3Hash,
    stopWords = true,
    lowerCase = false,
    dictFile = null,
    cutAll = true, // 修改默认值为true以正确处理英文
    forSearch = false,
    HMM = true,
    usePaddle = false,
    ...kwargs
  } = {}) {
    super({ hashFunction, stopWords, lowerCase, dictFile, ...kwargs });
    this.cutAll = cutAll;
    this.forSearch = forSearch;
    this.HMM = HMM;
    this.usePaddle = usePaddle;

    if (this.dictFile) {
      this.setDict(this.dictFile);
    }
  }

  updateParameter({
    hashFunction = Hash.mmh3Hash,
    stopWords = false,
    lowerCase = false,
    dictFile = null,
    cutAll = true, // 修改默认值为true以正确处理英文
    forSearch = false,
    HMM = true,
    usePaddle = false,
    ...kwargs
  }: TokenizerParameter) {
    super.updateParameter({ hashFunction, stopWords, lowerCase, dictFile, ...kwargs });
    this.cutAll = cutAll;
    this.forSearch = forSearch;
    this.HMM = HMM;
    this.usePaddle = usePaddle;

    if (this.dictFile) {
      this.setDict(this.dictFile);
    }
  }

  getParameter() {
    return {
      hash_function: this.hashFunction.name,
      stop_words: this.stopWords,
      lower_case: this.lowerCase,
      dict_file: this.dictFile,
      cut_all: this.cutAll,
      for_search: this.forSearch,
      HMM: this.HMM,
      use_paddle: this.usePaddle,
    };
  }

  tokenize(sentence: string): string[] {
    if (typeof sentence !== 'string') {
      throw new TypeError(`input sentence(${sentence}) must be string`);
    }
    if (sentence.length <= 0) {
      return [];
    }

    if (this.lowerCase) {
      sentence = sentence.toLowerCase();
    }

    const words: string[] = [];
    let segs: string[] = [];

    if (this.forSearch) {
      segs = this.cutForSearch(sentence);
    } else {
      segs = this.cut(sentence);
    }

    for (const word of segs) {
      if (word === ' ' || word === '') {
        continue;
      }
      if (this._isStopWord(word)) {
        continue;
      }
      words.push(word);
    }

    return words;
  }

  encode(sentence: string): number[] {
    const tokens: number[] = [];
    for (const word of this.tokenize(sentence)) {
      tokens.push(this.hashFunction(word));
    }
    return tokens;
  }

  cut(sentence: string): string[] {
    return nodejieba.cut(sentence, this.cutAll);
  }

  cutForSearch(sentence: string): string[] {
    return nodejieba.cutForSearch(sentence);
  }

  setDict(dictFile: string) {
    if (!fs.existsSync(dictFile)) {
      throw new Error('FileNotFoundError');
    }
    if (!fs.statSync(dictFile).isFile()) {
      throw new Error('not a file');
    }
    this.dictFile = dictFile;
    nodejieba.load({
      dict: dictFile,
    });
  }
}
