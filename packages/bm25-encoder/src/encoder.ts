import Decimal from 'decimal.js';
import fs from 'fs';
import path from 'path';

import { Hash } from './hash';
import { JiebaTokenizer } from './tokenizer';
import { getDirname } from './utils';

/**
 * BM25稀疏编码器实现
 */
export class BM25Encoder {
  public b: number;
  public k1: number;
  public tokenizer: JiebaTokenizer;
  public tokenFreq: Record<string, number> | null;
  public docCount: number | null;
  public averageDocLength: number | null;
  public totalDocLength: number;

  constructor({
    b = 0.75,
    k1 = 1.2,
    tokenizer = new JiebaTokenizer({ hashFunction: Hash.mmh3Hash, cutAll: true }),
  } = {}) {
    this.b = b;
    this.k1 = k1;
    this.tokenizer = tokenizer;

    // 学习参数
    this.tokenFreq = null;
    this.docCount = null;
    this.averageDocLength = null;
    this.totalDocLength = 0;
  }

  /**
   * 获取默认OKAPI BM25模型的BM25编码器
   * @returns {BM25Encoder} 带有默认模型的BM25编码器
   */
  static default(): BM25Encoder {
    const paramsPath = path.join(getDirname(), '../data/bm25_zh_default.json');
    const encoder = new BM25Encoder();
    try {
      encoder.setParams(paramsPath);
    } catch (e: any) {
      throw new Error(`load error: ${e.message}`);
    }
    return encoder;
  }

  /**
   * 计算词频
   * @param {string} text - 输入文本
   * @returns {[Array<number>, Array<number>]} [词元, 计数]
   */
  _tf(text: string): [number[], number[]] {
    const tokens = this.tokenizer.encode(text);
    const counter = new Map<number, number>();

    for (const token of tokens) {
      counter.set(token, (counter.get(token) || 0) + 1);
    }

    const resultTokens: number[] = [];
    const counts: number[] = [];
    for (const [token, count] of counter.entries()) {
      resultTokens.push(token);
      counts.push(count);
    }

    return [resultTokens, counts];
  }

  /**
   * 编码单个文档
   * @param {string} text - 文档文本
   * @returns {Array<[number, number]>} 稀疏向量
   */
  _encodeSingleDocument(text: string): [number, number][] {
    const [tokens, counts] = this._tf(text);
    const tf = counts;
    const tfSum = tf.reduce((sum, val) => sum + val, 0);

    const tfNormed = tf.map(
      (val) => val / (this.k1 * (1.0 - this.b + this.b * (tfSum / this.averageDocLength!)) + val),
    );

    const vectors: [number, number][] = [];
    for (let i = 0; i < tfNormed.length; i++) {
      vectors.push([tokens[i], tfNormed[i]]);
    }

    return vectors;
  }

  /**
   * 将文本编码为稀疏向量
   * @param {string|Array<string>} texts - 输入文本
   * @returns {Array<[number, number]>|Array<Array<[number, number]>>} 稀疏向量
   */
  encodeTexts(texts: string | string[]): [number, number][] | [number, number][][] {
    // if (this.tokenFreq === null || this.docCount === null || this.averageDocLength === null) {
    //   throw new Error('BM25 must be fit before encoding documents');
    // }

    if (typeof texts === 'string') {
      return this._encodeSingleDocument(texts);
    } else if (Array.isArray(texts)) {
      return texts.map((text) => this._encodeSingleDocument(text));
    } else {
      throw new Error('texts must be a string or array of strings');
    }
  }

  /**
   * 编码单个查询
   * @param {string} text - 查询文本
   * @returns {Array<[number, number]>} 稀疏向量
   */
  _encodeSingleQuery(text: string): [number, number][] {
    const [tokens] = this._tf(text);
    const df = tokens.map((idx) => this.tokenFreq![String(idx)] || 1);
    const idf = df.map((val) => Math.log((this.docCount! + 1) / (val + 0.5)));

    // 使用高精度计算求和以匹配Python numpy的精度
    let idfSum = new Decimal(0);
    for (const val of idf) {
      idfSum = idfSum.plus(val);
    }

    // 使用高精度计算归一化
    const idfNorm = idf.map((val) => {
      const result = new Decimal(val).div(idfSum);
      return result.toNumber();
    });

    const vectors: [number, number][] = [];
    for (let i = 0; i < idfNorm.length; i++) {
      vectors.push([tokens[i], idfNorm[i]]);
    }

    return vectors;
  }

  /**
   * 将查询编码为稀疏向量
   * @param {string|Array<string>} texts - 输入查询文本
   * @returns {Array<[number, number]>|Array<Array<[number, number]>>} 稀疏向量
   */
  encodeQueries(texts: string | string[]): [number, number][] | [number, number][][] {
    if (this.tokenFreq === null || this.docCount === null || this.averageDocLength === null) {
      throw new Error('BM25 must be fit before encoding queries');
    }

    if (typeof texts === 'string') {
      return this._encodeSingleQuery(texts);
    } else if (Array.isArray(texts)) {
      return texts.map((text) => this._encodeSingleQuery(text));
    } else {
      throw new Error('texts must be a string or array of strings');
    }
  }

  /**
   * 拟合语料库以学习参数
   * @param {string|Array<string>} corpus - 训练语料库
   */
  fitCorpus(corpus: string | string[]): void {
    let docNum = 0;
    let sumDocLen = 0;
    const tokenFreqCounter = new Map<string, number>();

    const corpusArray = typeof corpus === 'string' ? [corpus] : corpus;

    for (const doc of corpusArray) {
      if (typeof doc !== 'string') {
        throw new Error('corpus must be an array of strings');
      }

      const [indices, tf] = this._tf(doc);
      if (indices.length === 0) {
        continue;
      }

      docNum += 1;
      sumDocLen += tf.reduce((sum, val) => sum + val, 0);

      // 计算包含每个词元的文档数量
      const indicesStr = indices.map((x) => String(x));
      for (const idx of indicesStr) {
        tokenFreqCounter.set(idx, (tokenFreqCounter.get(idx) || 0) + 1);
      }
    }

    if (this.tokenFreq === null || this.docCount === null || this.averageDocLength === null) {
      this.tokenFreq = Object.fromEntries(tokenFreqCounter);
      this.docCount = docNum;
      this.averageDocLength = sumDocLen / docNum;
      this.totalDocLength = sumDocLen;
    } else {
      if (this.totalDocLength === 0) {
        this.totalDocLength = this.averageDocLength * this.docCount;
      }
      this.totalDocLength += sumDocLen;
      this.docCount += docNum;
      this.averageDocLength = this.totalDocLength / this.docCount;

      const tokenFreq = Object.fromEntries(tokenFreqCounter);
      for (const [k, v] of Object.entries(tokenFreq)) {
        const count = this.tokenFreq[k] || 0;
        this.tokenFreq[k] = count + v;
      }
    }
  }

  /**
   * 将BM25参数保存到文件
   * @param {string} paramsFile - 保存参数的文件路径
   */
  downloadParams(paramsFile = './bm25_params.json'): void {
    if (typeof paramsFile !== 'string') {
      throw new TypeError('input path must be string');
    }
    if (!paramsFile) {
      throw new Error('input path should not be empty');
    }
    if (this.tokenFreq === null || this.docCount === null || this.averageDocLength === null) {
      throw new Error('BM25 must be fit before storing params');
    }

    const tokenizerParam = this.tokenizer.getParameter();
    const docSorted = Object.entries(this.tokenFreq).sort();
    this.tokenFreq = Object.fromEntries(docSorted);

    const data = {
      b: this.b,
      k1: this.k1,
      token_freq: this.tokenFreq,
      doc_count: this.docCount,
      average_doc_length: this.averageDocLength,
      ...tokenizerParam,
    };

    try {
      const dir = path.dirname(paramsFile);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    } catch (error: any) {
      throw new Error(`create directory error: ${error.message}`);
    }

    try {
      fs.writeFileSync(paramsFile, JSON.stringify(data, null, 2), 'utf-8');
    } catch (e: any) {
      throw new Error(`download params error: ${e.message}`);
    }
  }

  /**
   * 从文件设置BM25参数
   * @param {string} paramsFile - 参数文件路径
   * @returns {BM25Encoder} 当前编码器实例
   */
  setParams(paramsFile: string): BM25Encoder {
    const content = fs.readFileSync(paramsFile, 'utf-8');
    const data = JSON.parse(content);

    this.b = data.b;
    this.k1 = data.k1;
    this.tokenFreq = data.token_freq;
    this.docCount = data.doc_count;
    this.averageDocLength = data.average_doc_length;

    this.tokenizer.updateParameter({
      hashFunction: Hash.mmh3Hash,
      stopWords: data.stop_words !== undefined ? data.stop_words : true,
      lowerCase: data.lower_case || false,
      dictFile: data.dict_file,
      // cutAll: data.cut_all || false,
      cutAll: true, // 强制设置为true以正确处理英文，忽略配置文件中的false
      forSearch: data.for_search || false,
      HMM: data.HMM !== undefined ? data.HMM : true,
      usePaddle: data.use_paddle || false,
    });

    return this;
  }

  /**
   * 加载个性化词典以提高检测率
   * @param {string} dictFile - 词典文件路径
   */
  setDict(dictFile: string): void {
    this.tokenizer.setDict(dictFile);
  }
}
