import murmurhash3js from 'murmurhash3js-revisited';

/**
 * 文本处理的哈希工具类
 */
export class Hash {
  /**
   * 使用 mmh3 算法将文本哈希为 32 位无符号整数
   * @param {string|number} text - 需要哈希的文本
   * @returns {number} 哈希值
   */
  static mmh3Hash(text: string) {
    if (typeof text === 'number') {
      return text;
    }
    if (typeof text !== 'string') {
      throw new TypeError(`input text(${text}) must be a string or number`);
    }
    return murmurhash3js.x86.hash32(Buffer.from(text));
  }
}
