{"name": "@tencent/bm25-encoder", "author": "", "version": "1.0.0", "description": "node版的稀疏向量工具包", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "files": ["dist", "data"], "scripts": {"build": "tsup", "build-test-data": "python3 tests/build-test-data.py", "test": "vitest", "example": "pnpm build && node tests/run_examples.js", "example:python": "python3 tests/example.py", "example:node": "node tests/example.js"}, "keywords": ["bm25", "sparse vector"], "license": "ISC", "devDependencies": {"@types/murmurhash3js-revisited": "^3.0.3", "tsup": "^8.4.0", "vitest": "^3.0.9"}, "dependencies": {"decimal.js": "^10.5.0", "murmurhash3js-revisited": "^3.0.0", "nodejieba": "^3.4.4"}}