import fs from 'fs';
import path from 'path';
import { describe, expect, it } from 'vitest';

import { BM25Encoder } from '../src';

describe('BM25编码器测试', () => {
  it('应该产生与Python实现完全一致的结果', () => {
    const encoder = BM25Encoder.default();

    const expectedPath = path.join(__dirname, 'test-data.json');
    const expectedData = JSON.parse(fs.readFileSync(expectedPath, 'utf-8'));

    const texts = expectedData.map((item: any) => item.text);

    const sparseVectors = encoder.encodeTexts(texts) as [number, number][][];
    const queryVectors = encoder.encodeQueries(texts) as [number, number][][];

    for (let i = 0; i < texts.length; i++) {
      const text = texts[i];
      const expectedItem = expectedData[i];
      const tokenizeTexts = encoder.tokenizer.tokenize(text);
      const tokenHashes = encoder.tokenizer.encode(text);

      const actualTokenResult = tokenizeTexts.map((token, index) => ({
        token: token,
        hash: tokenHashes[index],
      }));

      expect(actualTokenResult).toEqual(expectedItem.token_result);
      expect(sparseVectors[i]).toEqual(expectedItem.encode_texts_result);
      expect(queryVectors[i]).toEqual(expectedItem.encode_queries_result);
    }
  });
});
