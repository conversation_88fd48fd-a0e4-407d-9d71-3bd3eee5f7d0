[{"text": "腾讯云向量数据库（Tencent Cloud VectorDB）是一款全托管的自研企业级分布式数据库服务，专用于存储、索引、检索、管理由深度神经网络或其他机器学习模型生成的大量多维嵌入向量。", "token_result": [{"token": "腾讯", "hash": 2502995674}, {"token": "云", "hash": 1574737055}, {"token": "向量", "hash": 1169440797}, {"token": "数据库", "hash": 1075178782}, {"token": "Tencent", "hash": 1335487051}, {"token": "Cloud", "hash": 4109964284}, {"token": "VectorDB", "hash": 513507160}, {"token": "是", "hash": 4292253285}, {"token": "一款", "hash": 1006938503}, {"token": "全", "hash": 3180265193}, {"token": "托管", "hash": 3845702398}, {"token": "的", "hash": 2737245727}, {"token": "自研", "hash": 380823393}, {"token": "企业级", "hash": 3434527745}, {"token": "分布式", "hash": 1562082678}, {"token": "数据库", "hash": 1075178782}, {"token": "服务", "hash": 1549969637}, {"token": "专用", "hash": 1546594603}, {"token": "于", "hash": 1939544636}, {"token": "存储", "hash": 1172076521}, {"token": "索引", "hash": 4139467588}, {"token": "检索", "hash": 3020457966}, {"token": "管理", "hash": 4130523965}, {"token": "由", "hash": 3327852214}, {"token": "深度", "hash": 3434399993}, {"token": "神经网络", "hash": 1373702070}, {"token": "或", "hash": 663813817}, {"token": "其他", "hash": 2637553907}, {"token": "机器", "hash": 3507422554}, {"token": "学习", "hash": 2810460056}, {"token": "模型", "hash": 2619589300}, {"token": "生成", "hash": 2112632897}, {"token": "的", "hash": 2737245727}, {"token": "大量", "hash": 2927612885}, {"token": "多维", "hash": 3878051440}, {"token": "嵌入", "hash": 225043997}, {"token": "向量", "hash": 1169440797}], "encode_texts_result": [[2502995674, 0.6965838871822179], [1574737055, 0.6965838871822179], [1169440797, 0.8211605596928586], [1075178782, 0.8211605596928586], [1335487051, 0.6965838871822179], [4109964284, 0.6965838871822179], [513507160, 0.6965838871822179], [4292253285, 0.6965838871822179], [1006938503, 0.6965838871822179], [3180265193, 0.6965838871822179], [3845702398, 0.6965838871822179], [2737245727, 0.8211605596928586], [380823393, 0.6965838871822179], [3434527745, 0.6965838871822179], [1562082678, 0.6965838871822179], [1549969637, 0.6965838871822179], [1546594603, 0.6965838871822179], [1939544636, 0.6965838871822179], [1172076521, 0.6965838871822179], [4139467588, 0.6965838871822179], [3020457966, 0.6965838871822179], [4130523965, 0.6965838871822179], [3327852214, 0.6965838871822179], [3434399993, 0.6965838871822179], [1373702070, 0.6965838871822179], [663813817, 0.6965838871822179], [2637553907, 0.6965838871822179], [3507422554, 0.6965838871822179], [2810460056, 0.6965838871822179], [2619589300, 0.6965838871822179], [2112632897, 0.6965838871822179], [2927612885, 0.6965838871822179], [3878051440, 0.6965838871822179], [225043997, 0.6965838871822179]], "encode_queries_result": [[2502995674, 0.016605671984929748], [1574737055, 0.01391337404157145], [1169440797, 0.017272596878290174], [1075178782, 0.017474894481026407], [1335487051, 0.050879005775986245], [4109964284, 0.050879005775986245], [513507160, 0.050879005775986245], [4292253285, 0.050879005775986245], [1006938503, 0.01683949875517773], [3180265193, 0.01011409316473143], [3845702398, 0.023801201996476656], [2737245727, 0.050879005775986245], [380823393, 0.03590712479563089], [3434527745, 0.033382673893060585], [1562082678, 0.027778972686838172], [1549969637, 0.006281254022057385], [1546594603, 0.015068691633325113], [1939544636, 0.050879005775986245], [1172076521, 0.01975085025602738], [4139467588, 0.0198540244036402], [3020457966, 0.024001044302682958], [4130523965, 0.00760274437186535], [3327852214, 0.050879005775986245], [3434399993, 0.015936463709385847], [1373702070, 0.030129782852630318], [663813817, 0.050879005775986245], [2637553907, 0.050879005775986245], [3507422554, 0.015298408522666148], [2810460056, 0.009487034259171944], [2619589300, 0.012883793053140464], [2112632897, 0.050879005775986245], [2927612885, 0.050879005775986245], [3878051440, 0.027134690748348362], [225043997, 0.023812051651476627]]}, {"text": "作为专门为处理输入向量查询而设计的数据库，它支持多种索引类型和相似度计算方法，单索引支持10亿级向量规模，高达百万级 QPS 及毫秒级查询延迟。", "token_result": [{"token": "作为", "hash": 264800010}, {"token": "专门", "hash": 1070478849}, {"token": "为", "hash": 2320552345}, {"token": "处理", "hash": 3445754656}, {"token": "输入", "hash": 2000436274}, {"token": "向量", "hash": 1169440797}, {"token": "查询", "hash": 59256732}, {"token": "而", "hash": 3688234358}, {"token": "设计", "hash": 3648426870}, {"token": "的", "hash": 2737245727}, {"token": "数据库", "hash": 1075178782}, {"token": "它", "hash": 1963026435}, {"token": "支持", "hash": 1682626146}, {"token": "多种", "hash": 1316185243}, {"token": "索引", "hash": 4139467588}, {"token": "类型", "hash": 2411973761}, {"token": "和", "hash": 468177963}, {"token": "相似", "hash": 2888604556}, {"token": "度", "hash": 865827848}, {"token": "计算方法", "hash": 3693671420}, {"token": "单", "hash": 2712709217}, {"token": "索引", "hash": 4139467588}, {"token": "支持", "hash": 1682626146}, {"token": "10", "hash": 2263091519}, {"token": "亿级", "hash": 2846428945}, {"token": "向量", "hash": 1169440797}, {"token": "规模", "hash": 4199967281}, {"token": "高", "hash": 3355549849}, {"token": "达", "hash": 2611729710}, {"token": "百万", "hash": 1849209324}, {"token": "级", "hash": 1828837926}, {"token": "QPS", "hash": 1445750678}, {"token": "及", "hash": 3187326266}, {"token": "毫秒", "hash": 1704928839}, {"token": "级", "hash": 1828837926}, {"token": "查询", "hash": 59256732}, {"token": "延迟", "hash": 477332289}], "encode_texts_result": [[264800010, 0.6965838871822179], [1070478849, 0.6965838871822179], [2320552345, 0.6965838871822179], [3445754656, 0.6965838871822179], [2000436274, 0.6965838871822179], [1169440797, 0.8211605596928586], [59256732, 0.8211605596928586], [3688234358, 0.6965838871822179], [3648426870, 0.6965838871822179], [2737245727, 0.6965838871822179], [1075178782, 0.6965838871822179], [1963026435, 0.6965838871822179], [1682626146, 0.8211605596928586], [1316185243, 0.6965838871822179], [4139467588, 0.8211605596928586], [2411973761, 0.6965838871822179], [468177963, 0.6965838871822179], [2888604556, 0.6965838871822179], [865827848, 0.6965838871822179], [3693671420, 0.6965838871822179], [2712709217, 0.6965838871822179], [2263091519, 0.6965838871822179], [2846428945, 0.6965838871822179], [4199967281, 0.6965838871822179], [3355549849, 0.6965838871822179], [2611729710, 0.6965838871822179], [1849209324, 0.6965838871822179], [1828837926, 0.8211605596928586], [1445750678, 0.6965838871822179], [3187326266, 0.6965838871822179], [1704928839, 0.6965838871822179], [477332289, 0.6965838871822179]], "encode_queries_result": [[264800010, 0.05041731176751355], [1070478849, 0.05041731176751355], [2320552345, 0.05041731176751355], [3445754656, 0.05041731176751355], [2000436274, 0.013182497323397576], [1169440797, 0.01711585925404134], [59256732, 0.019315543316020926], [3688234358, 0.05041731176751355], [3648426870, 0.005493222838805543], [2737245727, 0.05041731176751355], [1075178782, 0.017316321137119], [1963026435, 0.05041731176751355], [1682626146, 0.05041731176751355], [1316185243, 0.014190910897950645], [4139467588, 0.019673861997330802], [2411973761, 0.010197066534102937], [468177963, 0.05041731176751355], [2888604556, 0.05041731176751355], [865827848, 0.012982911760441845], [3693671420, 0.02773087716471784], [2712709217, 0.05041731176751355], [2263091519, 0.05041731176751355], [2846428945, 0.043392842158205394], [4199967281, 0.012876760558910705], [3355549849, 0.005665163193992553], [2611729710, 0.0073467243289283505], [1849209324, 0.014933220506784856], [1828837926, 0.008493345624456645], [1445750678, 0.05041731176751355], [3187326266, 0.05041731176751355], [1704928839, 0.025777420605083153], [477332289, 0.018473086054520138]]}, {"text": "不仅能为大模型提供外部知识库，提高大模型回答的准确性，还可广泛应用于推荐系统、NLP 服务、计算机视觉、智能客服等 AI 领域。", "token_result": [{"token": "不仅", "hash": 1438774788}, {"token": "能", "hash": 516140144}, {"token": "为", "hash": 2320552345}, {"token": "大", "hash": 1721290619}, {"token": "模型", "hash": 2619589300}, {"token": "提供", "hash": 1313971048}, {"token": "外部", "hash": 2791049529}, {"token": "知识库", "hash": 3156250676}, {"token": "提高", "hash": 2925693446}, {"token": "大", "hash": 1721290619}, {"token": "模型", "hash": 2619589300}, {"token": "回答", "hash": 3418062048}, {"token": "的", "hash": 2737245727}, {"token": "准确性", "hash": 302156085}, {"token": "还", "hash": 3304049182}, {"token": "可", "hash": 71421380}, {"token": "广泛应用", "hash": 3500206691}, {"token": "于", "hash": 1939544636}, {"token": "推荐", "hash": 344804657}, {"token": "系统", "hash": 334201548}, {"token": "NLP", "hash": 3194014537}, {"token": "服务", "hash": 1549969637}, {"token": "计算机", "hash": 4286702936}, {"token": "视觉", "hash": 2775596440}, {"token": "智能", "hash": 3270491158}, {"token": "客服", "hash": 572684007}, {"token": "等", "hash": 2473124567}, {"token": "AI", "hash": 863271227}, {"token": "领域", "hash": 343383651}], "encode_texts_result": [[1438774788, 0.711104400205287], [516140144, 0.711104400205287], [2320552345, 0.711104400205287], [1721290619, 0.8311642470441586], [2619589300, 0.8311642470441586], [1313971048, 0.711104400205287], [2791049529, 0.711104400205287], [3156250676, 0.711104400205287], [2925693446, 0.711104400205287], [3418062048, 0.711104400205287], [2737245727, 0.711104400205287], [302156085, 0.711104400205287], [3304049182, 0.711104400205287], [71421380, 0.711104400205287], [3500206691, 0.711104400205287], [1939544636, 0.711104400205287], [344804657, 0.711104400205287], [334201548, 0.711104400205287], [3194014537, 0.711104400205287], [1549969637, 0.711104400205287], [4286702936, 0.711104400205287], [2775596440, 0.711104400205287], [3270491158, 0.711104400205287], [572684007, 0.711104400205287], [2473124567, 0.711104400205287], [863271227, 0.711104400205287], [343383651, 0.711104400205287]], "encode_queries_result": [[1438774788, 0.06311438847046813], [516140144, 0.06311438847046813], [2320552345, 0.06311438847046813], [1721290619, 0.06311438847046813], [2619589300, 0.01598208745094653], [1313971048, 0.008389587372611218], [2791049529, 0.0030731066673272247], [3156250676, 0.037048875279458604], [2925693446, 0.01598859790233702], [3418062048, 0.019534385043651337], [2737245727, 0.06311438847046813], [302156085, 0.03391980416848984], [3304049182, 0.06311438847046813], [71421380, 0.06311438847046813], [3500206691, 0.03183419313751215], [1939544636, 0.06311438847046813], [344804657, 0.018557265222550284], [334201548, 0.005893540798595382], [3194014537, 0.06311438847046813], [1549969637, 0.007791769913415443], [4286702936, 0.020237833759239775], [2775596440, 0.020395186098057794], [3270491158, 0.02259267722409604], [572684007, 0.029547365956751115], [2473124567, 0.06311438847046813], [863271227, 0.06311438847046813], [343383651, 0.014955450829810884]]}, {"text": "腾讯云向量数据库（Tencent Cloud VectorDB）作为一种专门存储和检索向量数据的服务提供给用户， 在高性能、高可用、大规模、低成本、简单易用、稳定可靠等方面体现出显著优势。 ", "token_result": [{"token": "腾讯", "hash": 2502995674}, {"token": "云", "hash": 1574737055}, {"token": "向量", "hash": 1169440797}, {"token": "数据库", "hash": 1075178782}, {"token": "Tencent", "hash": 1335487051}, {"token": "Cloud", "hash": 4109964284}, {"token": "VectorDB", "hash": 513507160}, {"token": "作为", "hash": 264800010}, {"token": "一种", "hash": 2724965718}, {"token": "专门", "hash": 1070478849}, {"token": "存储", "hash": 1172076521}, {"token": "和", "hash": 468177963}, {"token": "检索", "hash": 3020457966}, {"token": "向量", "hash": 1169440797}, {"token": "数据", "hash": 3656406568}, {"token": "的", "hash": 2737245727}, {"token": "服务", "hash": 1549969637}, {"token": "提供", "hash": 1313971048}, {"token": "给", "hash": 1572569924}, {"token": "用户", "hash": 3681788688}, {"token": "在", "hash": 3747799369}, {"token": "高性能", "hash": 161137419}, {"token": "高", "hash": 3355549849}, {"token": "可用", "hash": 256049420}, {"token": "大规模", "hash": 1304494748}, {"token": "低成本", "hash": 3357704521}, {"token": "简单", "hash": 1248038310}, {"token": "易用", "hash": 4162843804}, {"token": "稳定", "hash": 3926861960}, {"token": "可靠", "hash": 2135069689}, {"token": "等", "hash": 2473124567}, {"token": "方面", "hash": 44128954}, {"token": "体现", "hash": 2817015460}, {"token": "出", "hash": 234293441}, {"token": "显著", "hash": 3580522839}, {"token": "优势", "hash": 3486923126}], "encode_texts_result": [[2502995674, 0.698366438196841], [1574737055, 0.698366438196841], [1169440797, 0.8223978318110172], [1075178782, 0.698366438196841], [1335487051, 0.698366438196841], [4109964284, 0.698366438196841], [513507160, 0.698366438196841], [264800010, 0.698366438196841], [2724965718, 0.698366438196841], [1070478849, 0.698366438196841], [1172076521, 0.698366438196841], [468177963, 0.698366438196841], [3020457966, 0.698366438196841], [3656406568, 0.698366438196841], [2737245727, 0.698366438196841], [1549969637, 0.698366438196841], [1313971048, 0.698366438196841], [1572569924, 0.698366438196841], [3681788688, 0.698366438196841], [3747799369, 0.698366438196841], [161137419, 0.698366438196841], [3355549849, 0.698366438196841], [256049420, 0.698366438196841], [1304494748, 0.698366438196841], [3357704521, 0.698366438196841], [1248038310, 0.698366438196841], [4162843804, 0.698366438196841], [3926861960, 0.698366438196841], [2135069689, 0.698366438196841], [2473124567, 0.698366438196841], [44128954, 0.698366438196841], [2817015460, 0.698366438196841], [234293441, 0.698366438196841], [3580522839, 0.698366438196841], [3486923126, 0.698366438196841]], "encode_queries_result": [[2502995674, 0.016355123127678062], [1574737055, 0.01370344697750595], [1169440797, 0.01701198535871107], [1075178782, 0.017211230665024956], [1335487051, 0.050111335743310406], [4109964284, 0.050111335743310406], [513507160, 0.050111335743310406], [264800010, 0.050111335743310406], [2724965718, 0.006776318075974573], [1070478849, 0.050111335743310406], [1172076521, 0.019452846479613643], [468177963, 0.050111335743310406], [3020457966, 0.023638912964165506], [3656406568, 0.009961365313292147], [2737245727, 0.050111335743310406], [1549969637, 0.0061864815239156485], [1313971048, 0.006661134485577309], [1572569924, 0.050111335743310406], [3681788688, 0.013361419720674493], [3747799369, 0.050111335743310406], [161137419, 0.025230968318450418], [3355549849, 0.0056307820647773935], [256049420, 0.017030025189144572], [1304494748, 0.0147929208860737], [3357704521, 0.025465114679718445], [1248038310, 0.013101131818394148], [4162843804, 0.03140459539721167], [3926861960, 0.012455736109386899], [2135069689, 0.019334454408378964], [2473124567, 0.050111335743310406], [44128954, 0.050111335743310406], [2817015460, 0.018449519607026732], [234293441, 0.050111335743310406], [3580522839, 0.050111335743310406], [3486923126, 0.015337122166268605]]}, {"text": "腾讯云向量数据库可以和大语言模型 LLM 配合使用。企业的私域数据在经过文本分割、向量化后，可以存储在腾讯云向量数据库中，构建起企业专属的外部知识库，从而在后续的检索任务中，为大模型提供提示信息，辅助大模型生成更加准确的答案。", "token_result": [{"token": "腾讯", "hash": 2502995674}, {"token": "云", "hash": 1574737055}, {"token": "向量", "hash": 1169440797}, {"token": "数据库", "hash": 1075178782}, {"token": "可以", "hash": 4166014703}, {"token": "和", "hash": 468177963}, {"token": "大", "hash": 1721290619}, {"token": "语言", "hash": 1724264686}, {"token": "模型", "hash": 2619589300}, {"token": "LLM", "hash": 4060046290}, {"token": "配合", "hash": 4178294355}, {"token": "使用", "hash": 376887513}, {"token": "企业", "hash": 1170363907}, {"token": "的", "hash": 2737245727}, {"token": "私域", "hash": 2257961398}, {"token": "数据", "hash": 3656406568}, {"token": "在", "hash": 3747799369}, {"token": "经过", "hash": 1765692383}, {"token": "文本", "hash": 2178589616}, {"token": "分割", "hash": 995235875}, {"token": "向", "hash": 2117656664}, {"token": "量化", "hash": 2365772520}, {"token": "后", "hash": 3230084815}, {"token": "可以", "hash": 4166014703}, {"token": "存储", "hash": 1172076521}, {"token": "在", "hash": 3747799369}, {"token": "腾讯", "hash": 2502995674}, {"token": "云", "hash": 1574737055}, {"token": "向量", "hash": 1169440797}, {"token": "数据库", "hash": 1075178782}, {"token": "中", "hash": 3739180114}, {"token": "构建", "hash": 3276353601}, {"token": "起", "hash": 782805804}, {"token": "企业", "hash": 1170363907}, {"token": "专属", "hash": 771924135}, {"token": "的", "hash": 2737245727}, {"token": "外部", "hash": 2791049529}, {"token": "知识库", "hash": 3156250676}, {"token": "从而", "hash": 1050303739}, {"token": "在", "hash": 3747799369}, {"token": "后续", "hash": 4242576925}, {"token": "的", "hash": 2737245727}, {"token": "检索", "hash": 3020457966}, {"token": "任务", "hash": 1836401688}, {"token": "中", "hash": 3739180114}, {"token": "为", "hash": 2320552345}, {"token": "大", "hash": 1721290619}, {"token": "模型", "hash": 2619589300}, {"token": "提供", "hash": 1313971048}, {"token": "提示信息", "hash": 1447493947}, {"token": "辅助", "hash": 310383982}, {"token": "大", "hash": 1721290619}, {"token": "模型", "hash": 2619589300}, {"token": "生成", "hash": 2112632897}, {"token": "更加", "hash": 124161771}, {"token": "准确", "hash": 222403625}, {"token": "的", "hash": 2737245727}, {"token": "答案", "hash": 2808463469}], "encode_texts_result": [[2502995674, 0.796011491318915], [1574737055, 0.796011491318915], [1169440797, 0.796011491318915], [1075178782, 0.796011491318915], [4166014703, 0.796011491318915], [468177963, 0.6611454225513411], [1721290619, 0.8540860727400938], [1724264686, 0.6611454225513411], [2619589300, 0.8540860727400938], [4060046290, 0.6611454225513411], [4178294355, 0.6611454225513411], [376887513, 0.6611454225513411], [1170363907, 0.796011491318915], [2737245727, 0.8864213788903519], [2257961398, 0.6611454225513411], [3656406568, 0.6611454225513411], [3747799369, 0.8540860727400938], [1765692383, 0.6611454225513411], [2178589616, 0.6611454225513411], [995235875, 0.6611454225513411], [2117656664, 0.6611454225513411], [2365772520, 0.6611454225513411], [3230084815, 0.6611454225513411], [1172076521, 0.6611454225513411], [3739180114, 0.796011491318915], [3276353601, 0.6611454225513411], [782805804, 0.6611454225513411], [771924135, 0.6611454225513411], [2791049529, 0.6611454225513411], [3156250676, 0.6611454225513411], [1050303739, 0.6611454225513411], [4242576925, 0.6611454225513411], [3020457966, 0.6611454225513411], [1836401688, 0.6611454225513411], [2320552345, 0.6611454225513411], [1313971048, 0.6611454225513411], [1447493947, 0.6611454225513411], [310383982, 0.6611454225513411], [2112632897, 0.6611454225513411], [124161771, 0.6611454225513411], [222403625, 0.6611454225513411], [2808463469, 0.6611454225513411]], "encode_queries_result": [[2502995674, 0.013076745033475298], [1574737055, 0.010956596340221718], [1169440797, 0.013601939484796918], [1075178782, 0.013761246146657143], [4166014703, 0.04006653790904107], [468177963, 0.04006653790904107], [1721290619, 0.04006653790904107], [1724264686, 0.006479888884777798], [2619589300, 0.01014581505481248], [4060046290, 0.04006653790904107], [4178294355, 0.009740005530140776], [376887513, 0.04006653790904107], [1170363907, 0.007792250837805339], [2737245727, 0.04006653790904107], [2257961398, 0.04006653790904107], [3656406568, 0.007964613495741964], [3747799369, 0.04006653790904107], [1765692383, 0.04006653790904107], [2178589616, 0.014164089311326405], [995235875, 0.013781269088632765], [2117656664, 0.04006653790904107], [2365772520, 0.01939335918187575], [3230084815, 0.04006653790904107], [1172076521, 0.015553530939718426], [3739180114, -0.001992193930153652], [3276353601, 0.016935058086180687], [782805804, 0.04006653790904107], [771924135, 0.013627948967486866], [2791049529, 0.001950882322857431], [3156250676, 0.023519520696399336], [1050303739, 0.04006653790904107], [4242576925, 0.011746460210859205], [3020457966, 0.01890050201931998], [1836401688, 0.04006653790904107], [2320552345, 0.04006653790904107], [1313971048, 0.005325912658778653], [1447493947, 0.030948627050814304], [310383982, 0.01184865023485117], [2112632897, 0.04006653790904107], [124161771, 0.04006653790904107], [222403625, 0.014508918873484227], [2808463469, 0.015137219025440681]]}, {"text": "腾讯云数据库托管机房分布在全球多个位置，这些位置节点称为地域（Region），每个地域又由多个可用区（Zone）构成。每个地域（Region）都是一个独立的地理区域。每个地域内都有多个相互隔离的位置，称为可用区（Zone）。每个可用区都是独立的，但同一地域下的可用区通过低时延的内网链路相连。腾讯云支持用户在不同位置分配云资源，建议用户在设计系统时考虑将资源放置在不同可用区以屏蔽单点故障导致的服务不可用状态。", "token_result": [{"token": "腾讯", "hash": 2502995674}, {"token": "云", "hash": 1574737055}, {"token": "数据库", "hash": 1075178782}, {"token": "托管", "hash": 3845702398}, {"token": "机房", "hash": 3439682588}, {"token": "分布", "hash": 2552818870}, {"token": "在", "hash": 3747799369}, {"token": "全球", "hash": 3234728044}, {"token": "多个", "hash": 3791430851}, {"token": "位置", "hash": 3844300341}, {"token": "这些", "hash": 3373528199}, {"token": "位置", "hash": 3844300341}, {"token": "节点", "hash": 3257810603}, {"token": "称为", "hash": 566999144}, {"token": "地域", "hash": 2138654181}, {"token": "Region", "hash": 2059998584}, {"token": "每个", "hash": 3769291015}, {"token": "地域", "hash": 2138654181}, {"token": "又", "hash": 1105973258}, {"token": "由", "hash": 3327852214}, {"token": "多个", "hash": 3791430851}, {"token": "可用", "hash": 256049420}, {"token": "区", "hash": 1962642405}, {"token": "Zone", "hash": 140514590}, {"token": "构成", "hash": 2608906392}, {"token": "每个", "hash": 3769291015}, {"token": "地域", "hash": 2138654181}, {"token": "Region", "hash": 2059998584}, {"token": "都", "hash": 1162397220}, {"token": "是", "hash": 4292253285}, {"token": "一个", "hash": 1622524647}, {"token": "独立", "hash": 388214363}, {"token": "的", "hash": 2737245727}, {"token": "地理", "hash": 4263655969}, {"token": "区域", "hash": 3730869947}, {"token": "每个", "hash": 3769291015}, {"token": "地域", "hash": 2138654181}, {"token": "内", "hash": 3063661696}, {"token": "都", "hash": 1162397220}, {"token": "有", "hash": 3907780531}, {"token": "多个", "hash": 3791430851}, {"token": "相互", "hash": 57157376}, {"token": "隔离", "hash": 1979598294}, {"token": "的", "hash": 2737245727}, {"token": "位置", "hash": 3844300341}, {"token": "称为", "hash": 566999144}, {"token": "可用", "hash": 256049420}, {"token": "区", "hash": 1962642405}, {"token": "Zone", "hash": 140514590}, {"token": "每个", "hash": 3769291015}, {"token": "可用", "hash": 256049420}, {"token": "区", "hash": 1962642405}, {"token": "都", "hash": 1162397220}, {"token": "是", "hash": 4292253285}, {"token": "独立", "hash": 388214363}, {"token": "的", "hash": 2737245727}, {"token": "但", "hash": 2691226101}, {"token": "同一", "hash": 4814046}, {"token": "地域", "hash": 2138654181}, {"token": "下", "hash": 886500968}, {"token": "的", "hash": 2737245727}, {"token": "可用", "hash": 256049420}, {"token": "区", "hash": 1962642405}, {"token": "通过", "hash": 727838517}, {"token": "低", "hash": 1760146162}, {"token": "时延", "hash": 1431978508}, {"token": "的", "hash": 2737245727}, {"token": "内网", "hash": 2856016623}, {"token": "链路", "hash": 3199918929}, {"token": "相连", "hash": 3737061450}, {"token": "腾讯", "hash": 2502995674}, {"token": "云", "hash": 1574737055}, {"token": "支持", "hash": 1682626146}, {"token": "用户", "hash": 3681788688}, {"token": "在", "hash": 3747799369}, {"token": "不同", "hash": 2684284988}, {"token": "位置", "hash": 3844300341}, {"token": "分配", "hash": 1644999992}, {"token": "云", "hash": 1574737055}, {"token": "资源", "hash": 1055963367}, {"token": "建议", "hash": 3352697404}, {"token": "用户", "hash": 3681788688}, {"token": "在", "hash": 3747799369}, {"token": "设计", "hash": 3648426870}, {"token": "系统", "hash": 334201548}, {"token": "时", "hash": 1221945485}, {"token": "考虑", "hash": 3073255202}, {"token": "将", "hash": 1279791901}, {"token": "资源", "hash": 1055963367}, {"token": "放置", "hash": 169900310}, {"token": "在", "hash": 3747799369}, {"token": "不同", "hash": 2684284988}, {"token": "可用", "hash": 256049420}, {"token": "区以", "hash": 3286685284}, {"token": "屏蔽", "hash": 2496243161}, {"token": "单点故障", "hash": 741051414}, {"token": "导致", "hash": 2261509031}, {"token": "的", "hash": 2737245727}, {"token": "服务", "hash": 1549969637}, {"token": "不可", "hash": 1049908702}, {"token": "用", "hash": 2667047748}, {"token": "状态", "hash": 1339242251}], "encode_texts_result": [[2502995674, 0.7480120901999662], [1574737055, 0.8166034926129475], [1075178782, 0.5974595156589315], [3845702398, 0.5974595156589315], [3439682588, 0.5974595156589315], [2552818870, 0.5974595156589315], [3747799369, 0.8558431539388224], [3234728044, 0.5974595156589315], [3791430851, 0.8166034926129475], [3844300341, 0.8558431539388224], [3373528199, 0.5974595156589315], [3257810603, 0.5974595156589315], [566999144, 0.7480120901999662], [2138654181, 0.8812508217492613], [2059998584, 0.7480120901999662], [3769291015, 0.8558431539388224], [1105973258, 0.5974595156589315], [3327852214, 0.5974595156589315], [256049420, 0.8812508217492613], [1962642405, 0.8558431539388224], [140514590, 0.7480120901999662], [2608906392, 0.5974595156589315], [1162397220, 0.8166034926129475], [4292253285, 0.7480120901999662], [1622524647, 0.5974595156589315], [388214363, 0.7480120901999662], [2737245727, 0.8990442833932569], [4263655969, 0.5974595156589315], [3730869947, 0.5974595156589315], [3063661696, 0.5974595156589315], [3907780531, 0.5974595156589315], [57157376, 0.5974595156589315], [1979598294, 0.5974595156589315], [2691226101, 0.5974595156589315], [4814046, 0.5974595156589315], [886500968, 0.5974595156589315], [727838517, 0.5974595156589315], [1760146162, 0.5974595156589315], [1431978508, 0.5974595156589315], [2856016623, 0.5974595156589315], [3199918929, 0.5974595156589315], [3737061450, 0.5974595156589315], [1682626146, 0.5974595156589315], [3681788688, 0.7480120901999662], [2684284988, 0.7480120901999662], [1644999992, 0.5974595156589315], [1055963367, 0.7480120901999662], [3352697404, 0.5974595156589315], [3648426870, 0.5974595156589315], [334201548, 0.5974595156589315], [1221945485, 0.5974595156589315], [3073255202, 0.5974595156589315], [1279791901, 0.5974595156589315], [169900310, 0.5974595156589315], [3286685284, 0.5974595156589315], [2496243161, 0.5974595156589315], [741051414, 0.5974595156589315], [2261509031, 0.5974595156589315], [1549969637, 0.5974595156589315], [1049908702, 0.5974595156589315], [2667047748, 0.5974595156589315], [1339242251, 0.5974595156589315]], "encode_queries_result": [[2502995674, 0.008538054288901213], [1574737055, 0.0071537690866431075], [1075178782, 0.008984978018789577], [3845702398, 0.012237743523504952], [3439682588, 0.013643753132290482], [2552818870, 0.0048448822934655566], [3747799369, 0.026160200796145425], [3234728044, 0.005005292395602739], [3791430851, 0.006238994032455481], [3844300341, 0.004316890309007948], [3373528199, 0.026160200796145425], [3257810603, 0.009279115269355483], [566999144, 0.0025434953949862514], [2138654181, 0.008405368772606722], [2059998584, 0.026160200796145425], [3769291015, 0.026160200796145425], [1105973258, 0.026160200796145425], [3327852214, 0.026160200796145425], [256049420, 0.008890381226186125], [1962642405, 0.001953148432229181], [140514590, 0.026160200796145425], [2608906392, 0.026160200796145425], [1162397220, 0.026160200796145425], [4292253285, 0.026160200796145425], [1622524647, 0.026160200796145425], [388214363, 0.003815693802321516], [2737245727, 0.026160200796145425], [4263655969, 0.006087551965631982], [3730869947, 0.004714606909840416], [3063661696, 0.026160200796145425], [3907780531, 0.026160200796145425], [57157376, 0.026160200796145425], [1979598294, 0.008222899331836658], [2691226101, 0.026160200796145425], [4814046, 0.026160200796145425], [886500968, 0.026160200796145425], [727838517, 0.026160200796145425], [1760146162, 0.005582181085353498], [1431978508, 0.017592322732503506], [2856016623, 0.020048284889602773], [3199918929, 0.01451119904487253], [3737061450, 0.008654564395465577], [1682626146, 0.026160200796145425], [3681788688, 0.006975216637706239], [2684284988, 0.026160200796145425], [1644999992, 0.007623535072699008], [1055963367, 0.0066332742153006495], [3352697404, 0.005692392353401309], [3648426870, 0.002850287082813501], [334201548, 0.0024428060609930666], [1221945485, 0.026160200796145425], [3073255202, 0.026160200796145425], [1279791901, 0.026160200796145425], [169900310, 0.009558042353662099], [3286685284, 0.01784426946308608], [2496243161, 0.012465019944115129], [741051414, 0.02139315592791036], [2261509031, 0.026160200796145425], [1549969637, 0.003229600578126314], [1049908702, 0.026160200796145425], [2667047748, 0.026160200796145425], [1339242251, 0.005701808480806408]]}, {"text": "注册登录页面，包含社团、音乐节等区域，支持一键登录和手机号登录功能。", "token_result": [{"token": "注册", "hash": 2770219625}, {"token": "登录", "hash": 2472496076}, {"token": "页面", "hash": 94062968}, {"token": "包含", "hash": 1370441523}, {"token": "社团", "hash": 2995126448}, {"token": "音乐节", "hash": 4005895242}, {"token": "等", "hash": 2473124567}, {"token": "区域", "hash": 3730869947}, {"token": "支持", "hash": 1682626146}, {"token": "一键", "hash": 235549423}, {"token": "登录", "hash": 2472496076}, {"token": "和", "hash": 468177963}, {"token": "手机号", "hash": 4060472263}, {"token": "登录", "hash": 2472496076}, {"token": "功能", "hash": 2706796689}], "encode_texts_result": [[2770219625, 0.7380271183681154], [2472496076, 0.8941974381073333], [94062968, 0.7380271183681154], [1370441523, 0.7380271183681154], [2995126448, 0.7380271183681154], [4005895242, 0.7380271183681154], [2473124567, 0.7380271183681154], [3730869947, 0.7380271183681154], [1682626146, 0.7380271183681154], [235549423, 0.7380271183681154], [468177963, 0.7380271183681154], [4060472263, 0.7380271183681154], [2706796689, 0.7380271183681154]], "encode_queries_result": [[2770219625, 0.043939201009290225], [2472496076, 0.05754205910377286], [94062968, 0.05194831361049214], [1370441523, 0.026848401593304545], [2995126448, 0.04721737710286352], [4005895242, 0.05481353085146328], [2473124567, 0.15241001124043774], [3730869947, 0.02746742265942005], [1682626146, 0.15241001124043774], [235549423, 0.09861373870444154], [468177963, 0.15241001124043774], [4060472263, 0.10476685551165396], [2706796689, 0.029613066131984576]]}, {"text": "福利中心页面，包含头图加内容、时间线、签到和打卡、任务中心区域，提供用户签到、任务完成及奖励领取功能。", "token_result": [{"token": "福利", "hash": 1120089477}, {"token": "中心", "hash": 586003774}, {"token": "页面", "hash": 94062968}, {"token": "包含", "hash": 1370441523}, {"token": "头", "hash": 2587642001}, {"token": "图加", "hash": 290005938}, {"token": "内容", "hash": 949681716}, {"token": "时间", "hash": 1207638572}, {"token": "线", "hash": 910658778}, {"token": "签到", "hash": 845722770}, {"token": "和", "hash": 468177963}, {"token": "打卡", "hash": 2728564690}, {"token": "任务", "hash": 1836401688}, {"token": "中心", "hash": 586003774}, {"token": "区域", "hash": 3730869947}, {"token": "提供", "hash": 1313971048}, {"token": "用户", "hash": 3681788688}, {"token": "签到", "hash": 845722770}, {"token": "任务", "hash": 1836401688}, {"token": "完成", "hash": 3490874723}, {"token": "及", "hash": 3187326266}, {"token": "奖励", "hash": 2510910004}, {"token": "领取", "hash": 3926489789}, {"token": "功能", "hash": 2706796689}], "encode_texts_result": [[1120089477, 0.7204911929727084], [586003774, 0.8375412741611603], [94062968, 0.7204911929727084], [1370441523, 0.7204911929727084], [2587642001, 0.7204911929727084], [290005938, 0.7204911929727084], [949681716, 0.7204911929727084], [1207638572, 0.7204911929727084], [910658778, 0.7204911929727084], [845722770, 0.8375412741611603], [468177963, 0.7204911929727084], [2728564690, 0.7204911929727084], [1836401688, 0.8375412741611603], [3730869947, 0.7204911929727084], [1313971048, 0.7204911929727084], [3681788688, 0.7204911929727084], [3490874723, 0.7204911929727084], [3187326266, 0.7204911929727084], [2510910004, 0.7204911929727084], [3926489789, 0.7204911929727084], [2706796689, 0.7204911929727084]], "encode_queries_result": [[1120089477, 0.035249214051154894], [586003774, 0.009618299334465745], [94062968, 0.03756963006944585], [1370441523, 0.019417079125599288], [2587642001, 0.02703446992367221], [290005938, 0.08899503029094523], [949681716, 0.017978249609572967], [1207638572, 0.007202613355757339], [910658778, 0.010261117842009155], [845722770, 0.0715399612827908], [468177963, 0.11022470881570325], [2728564690, 0.06348006865873723], [1836401688, 0.11022470881570325], [3730869947, 0.01986476242545633], [1313971048, 0.014651806785114739], [3681788688, 0.02938972941411458], [3490874723, 0.11022470881570325], [3187326266, 0.11022470881570325], [2510910004, 0.03829608847347229], [3926489789, 0.047136527128753145], [2706796689, 0.021416516966125176]]}, {"text": "hello world", "token_result": [{"token": "hello", "hash": 613153351}, {"token": "world", "hash": 4220927227}], "encode_texts_result": [[613153351, 0.7649186863756456], [4220927227, 0.7649186863756456]], "encode_queries_result": [[613153351, 0.5], [4220927227, 0.5]]}]