from tcvdb_text.encoder.bm25 import BM25Encoder
import json
import os

# 构建标准的测试数据，用于测试 BM25Encoder 的正确性
def main():
    output_data = []
    
    try:
        encoder = BM25Encoder.default('zh')

        current_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(current_dir, 'text.json')
        texts = json.load(open(file_path, 'r', encoding='utf-8'))

        sparse_vectors = encoder.encode_texts(texts)
        query_vectors = encoder.encode_queries(texts)

        for i, text in enumerate(texts):
            tokenize_texts = encoder.tokenizer.tokenize(text)
            result = [{
                "token": token,
                "hash": encoder.tokenizer.hash_function(token),
            } for token in tokenize_texts]

            output_data.append({
                "text": text,
                "token_result": result,
                "encode_texts_result": sparse_vectors[i],
                "encode_queries_result": query_vectors[i]
            })

        output_file_path = os.path.join(current_dir, 'test-data.json')
        with open(output_file_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"数据已保存到 {output_file_path}")
        print(f"共处理了 {len(output_data)} 条文本")

    except Exception as error:
        print('错误：', str(error))
        exit(1)

if __name__ == '__main__':
    main()