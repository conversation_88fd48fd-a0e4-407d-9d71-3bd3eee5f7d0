import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __dirname = path.dirname(new URL(import.meta.url).pathname);

async function runCommand(command) {
  try {
    const { stdout, stderr } = await execAsync(command);
    return stdout || stderr;
  } catch (error) {
    return `执行错误: ${error.message}`;
  }
}

async function main() {
  fs.mkdirSync(path.join(__dirname, 'results'), { recursive: true });
  const outputFile = path.join(__dirname, 'results', `output_${Date.now()}.txt`);

  console.log('执行 Python 示例...');
  const pythonOutput = await runCommand(`pnpm run example:python`);

  console.log('执行 Node.js 示例...');
  const nodeOutput = await runCommand(`pnpm run example:node`);

  const output = `${pythonOutput}\n\n\n${nodeOutput}`;
  fs.writeFileSync(outputFile, output, 'utf8');
  console.log(`执行完成！输出已保存到: ${outputFile}`);
}

main().catch((error) => {
  console.error('错误：', error);
  process.exit(1);
});
