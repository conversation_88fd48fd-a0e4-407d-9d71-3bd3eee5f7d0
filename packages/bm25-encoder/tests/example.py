from tcvdb_text.encoder.bm25 import BM25Encoder
import json
import os

# BM25编码器使用示例
def main():
    try:
        print('BM25编码器 Python 示例')
        print('===========================\n')

        encoder = BM25Encoder.default('zh')

        # 获取当前文件所在目录路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 构建完整的文件路径
        file_path = os.path.join(current_dir, 'text.json')
        texts = json.load(open(file_path, 'r', encoding='utf-8'))
        
        sparse_vectors = encoder.encode_texts(texts)
        query_vectors = encoder.encode_queries(texts)

        for i, text in enumerate(texts):
            print(f'文本: {text}')
            print('分词结果:')
            print(encoder.tokenizer.tokenize(text))
            print('encode_texts结果:')
            print(sparse_vectors[i])
            print('encode_queries结果:')
            print(query_vectors[i])
            print()
        print()

    except Exception as error:
        print('错误：', str(error))
        exit(1)

if __name__ == '__main__':
    main()