import fs from 'fs';
import path from 'path';

import { BM25Encoder } from '../dist/index.js';

// BM25编码器使用示例
async function main() {
  console.log('BM25编码器 Node.js 示例');
  console.log('===========================\n');

  const encoder = BM25Encoder.default('zh');
  const dirname = path.dirname(new URL(import.meta.url).pathname);
  const texts = JSON.parse(fs.readFileSync(path.join(dirname, 'text.json'), 'utf8'));
  const textVectors = encoder.encodeTexts(texts);
  const queryVectors = encoder.encodeQueries(texts);

  texts.forEach((text, i) => {
    console.log(`文本: ${text}`);
    console.log('分词结果:');
    console.log(encoder.tokenizer.tokenize(text));
    console.log('encodeTexts结果:');
    console.log(textVectors[i]);
    console.log('encodeQueries结果:');
    console.log(queryVectors[i]);
    console.log();
  });
}

// 处理错误
main().catch((error) => {
  console.error('错误：', error.message);
  process.exit(1);
});
