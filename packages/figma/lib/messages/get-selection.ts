import * as Bridge from '@ai-assitant/ai-bridge';
import { BridgeSide } from '../bridge/sides';

type Payload = {};
type ResultType = {
  success: boolean;
  data: SceneNode[];
};

/**
 * 获取当前选中的节点
 */
export default class GetSelectionMessage extends Bridge.MessageType<Payload, Promise<ResultType>> {
  public receivingSide(): Bridge.Side {
    return BridgeSide.PLUGIN;
  }

  async handle(payload: Payload): Promise<ResultType> {
    try {
      await figma.currentPage.loadAsync();
      const selection =  (figma.currentPage.selection || []) as SceneNode[];

      return {
        success: true,
        data: selection,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
      };
    }
  }
}
