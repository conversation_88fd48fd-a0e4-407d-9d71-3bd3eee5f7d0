import * as Bridge from '@ai-assitant/ai-bridge';
import { BridgeSide } from '../bridge/sides';
import { IDslNode } from '@tencent/h2d-html-parser';

import { setMappingFamilyMap } from '../design/fonts';
import { updaterSameLevel, updaterGlobalLevel } from '../design-update';

interface Payload {
  model: IDslNode;
  id: string;
  data: {
    name?: string;
    mappingFamily?: { [key: string]: string };
  };
}

/**
 * 创建设计页面
 */
export default class UpdateDesignMessage extends Bridge.MessageType<Payload, Promise<Boolean>> {
  public receivingSide(): Bridge.Side {
    return BridgeSide.PLUGIN;
  }

  async handle(payload: Payload) {
    const { model, id, data } = payload;

    try {
      console.log('🚀 ~ updateNode ~ payload', payload);
      await figma.loadAllPagesAsync();
      // 设置缺失字体的映射
      if (data) {
        setMappingFamilyMap(data.mappingFamily || {});
      }

      const figmaNode = (await figma.getNodeByIdAsync(id)) as SceneNode;

      if (!figmaNode) {
        console.error('🚀 ~ updateNode ~ figmaNode is null');
        return false;
      }
      const t1 = Date.now();
      await updaterGlobalLevel.update(figmaNode, { ...model, x: figmaNode.x, y: figmaNode.y });
      const t2 = Date.now();
      console.log(`🚀 ~ updateNode ~ updateGlobalLevel: ${t2 - t1}ms`);
      figma.viewport.scrollAndZoomIntoView([figmaNode]);

      return true;
    } catch (error) {
      console.error(`🚀 ~ updateNode ~ ${model.type} error:`, error);
      return false;
    }
  }
}
