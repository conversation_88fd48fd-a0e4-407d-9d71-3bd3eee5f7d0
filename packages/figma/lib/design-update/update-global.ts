import { IDslNode } from '@tencent/h2d-html-parser';

import { Operation } from './types';
import { isSameNodeType, getSequence, getChildrenSafe, getOldNodeId, diffProps } from './utils';
import { uuidV4 } from '../utils/math';
import { Updater } from './update';

type MapNode<T> = {
  node: T;
  index: number;
  parentId: string;
} & (T extends SceneNode ? {} : { id?: string });

export default class UpdaterGlobalLevel extends Updater {
  private oldMap = new Map<string, MapNode<SceneNode>>();
  private newMap = new Map<string, MapNode<IDslNode>>();
  private operations: Operation[] = [];
  private newCreateTempMap = new Map<IDslNode, string>();

  async update(oldNode: SceneNode, newNode: IDslNode): Promise<void> {
    // 执行diff比较
    this.diffTrees(oldNode, newNode);

    // 应用更新
    await this.applyOperations();

    this.clean();
  }

  private clean() {
    this.oldMap.clear();
    this.newMap.clear();
    this.newCreateTempMap.clear();
    this.operations = [];
  }
  /**
   * 执行两个节点树的差异比较，newRoot 更新 oldRoot
   * @param oldRoot Figma节点树
   * @param newRoot DSL节点树
   */
  private diffTrees(oldRoot: SceneNode, newRoot: IDslNode) {
    const parentId = oldRoot.parent?.id || figma.currentPage.id;
    const index = oldRoot.parent?.children.indexOf(oldRoot) || 0;

    this.collectOldNodes(oldRoot, index, parentId);
    this.processNewTree(newRoot, index, parentId);

    for (const [k, v] of this.oldMap.entries()) {
      if (!this.newMap.has(k)) {
        this.operations.push({
          type: 'REMOVE',
          nodeId: v.node.id,
        });
      }
    }
  }

  private collectOldNodes(node: SceneNode, index: number, parentId: string) {
    if (!node) return;

    if (node.id) {
      this.oldMap.set(node.id, {
        node,
        index,
        parentId,
      });
    }

    const children = getChildrenSafe(node);
    children.forEach((child, index) => {
      this.collectOldNodes(child, index, node.id);
    });
  }

  private processNewTree(node: IDslNode & { id?: string }, index: number, parentId: string) {
    let curNodeId = node.id!;

    // 如果节点存在
    if (node.id && this.oldMap.has(node.id)) {
      const oldNodeCache = this.oldMap.get(node.id)!;
      const oldNode = oldNodeCache.node;

      // 节点类型相同
      if (isSameNodeType(oldNode, node)) {
        // 如果 parent 不同或者 index 不同需要移动
        if (oldNodeCache.parentId !== parentId || oldNodeCache.index !== index) {
          this.operations.push({
            type: 'MOVE',
            nodeId: node.id,
            parentId,
            index,
          });
        }

        // 缓存 node
        this.newMap.set(node.id, {
          node,
          parentId,
          index,
          id: node.id,
        });
      } else {
        const tempId = this.processCreate(node, parentId, index);
        curNodeId = tempId;
      }
    } else {
      const tempId = this.processCreate(node, parentId, index);
      curNodeId = tempId;
    }

    const children = getChildrenSafe(node);
    // 处理子级内容
    if (!['HORIZONTAL', 'VERTICAL'].includes((node as any).layoutMode)) {
      children?.sort((a, b) => {
        if ('$zIndex' in a && '$zIndex' in b) {
          return a.$zIndex - b.$zIndex;
        }
        return 0;
      });
    }

    children.forEach((child, index) => {
      this.processNewTree(child, index, curNodeId);
    });
  }

  private processCreate(node: IDslNode, parentId: string, index: number): string {
    const tempId = uuidV4();
    this.newCreateTempMap.set(node, tempId);

    this.newMap.set(tempId, {
      node,
      parentId,
      index,
    });
    this.operations.push({
      type: 'CREATE',
      node,
      parentId,
      index,
    });
    this.operations.push({
      type: 'MOVE',
      nodeId: tempId,
      parentId,
      index,
    });

    return tempId;
  }

  /**
   * 执行操作
   * @param operations 操作数组
   */
  private async applyOperations(): Promise<void> {
    // 优化：按操作类型分组执行
    const removes = this.operations.filter((op) => op.type === 'REMOVE');
    const creates = this.operations.filter((op) => op.type === 'CREATE');
    const moves = this.operations.filter((op) => op.type === 'MOVE');

    await this.executeCreates(creates, {
      before: (op) => {
        const parentId = this.newMap.get(op.parentId!)?.id ?? op.parentId;
        return {
          ...op,
          parentId,
        };
      },
      after: (op, node) => {
        const tempId = this.newCreateTempMap.get(op.node);
        if (tempId) {
          const nodeCache = this.newMap.get(tempId)!;
          nodeCache.id = node.id;
        }
      },
      excludeChildren: true,
    });

    await this.executeMoves(moves, {
      before: (op) => {
        const nodeId = this.newMap.get(op.nodeId)!.id!;
        const parentId = this.newMap.get(op.parentId)?.id ?? op.parentId;
        return {
          ...op,
          nodeId,
          parentId,
        };
      },
    });

    const updateOperations = await this.colletPropsDiff();

    await Promise.all([this.executeRemoves(removes), this.executeUpdates(updateOperations)]);
  }

  private async colletPropsDiff() {
    // 由于移动后父节点布局会影响节点属性，需要在移动之后处理属性变更
    const updateOperations: Extract<Operation, { type: 'UPDATE_PROPS' }>[] = [];

    await Promise.all(
      [...this.newMap.values()].map(async (nodeCache) => {
        const nodeId = nodeCache.id!;
        const node = nodeCache.node;
        const figmaNode = await figma.getNodeByIdAsync(nodeId);
        const propChanges = diffProps(figmaNode!, node);
        if (propChanges) {
          updateOperations.push({
            type: 'UPDATE_PROPS',
            nodeId,
            props: propChanges,
          });
        }
      }),
    );
    return updateOperations;
  }
}
