import { IDslNode } from '@tencent/h2d-html-parser';

import { Operation } from './types';
import { isSameNodeType, getSequence, getChildrenSafe, getOldNodeId, diffProps } from './utils';
import { Updater } from './update';

export default class UpdaterSameLevel extends Updater {
  async update(oldNode: SceneNode, newNode: IDslNode): Promise<void> {
    // 执行diff比较
    const operations = this.diffTrees(oldNode, newNode);

    console.log(operations);

    // 应用更新
    await this.applyOperations(operations);
  }

  /**
   * 执行两个节点树的差异比较
   * @param oldRoot Figma节点树
   * @param newRoot DSL节点树
   * @returns 更新操作数组
   */
  private diffTrees(oldRoot: SceneNode, newRoot: IDslNode): Operation[] {
    return this.diff(oldRoot, newRoot);
  }

  /**
   * 节点差异比较
   * @param oldNode 旧节点
   * @param newNode 新节点
   * @returns 更新操作数组
   */
  private diff(oldNode: SceneNode, newNode: IDslNode): Operation[] {
    const operations: Operation[] = [];

    if (!isSameNodeType(oldNode, newNode)) {
      operations.push({
        type: 'REMOVE',
        nodeId: oldNode.id,
      });

      operations.push({
        type: 'CREATE',
        node: newNode,
        parentId: oldNode.parent?.id!,
        index: oldNode.parent?.children.indexOf(oldNode) || 0,
      });

      return operations;
    }

    // 节点类型相同，更新属性
    const propChanges = diffProps(oldNode, newNode);
    if (propChanges) {
      operations.push({
        type: 'UPDATE_PROPS',
        nodeId: oldNode.id,
        props: propChanges,
      });
    }

    const oldChildren = getChildrenSafe<SceneNode>(oldNode);
    const newChildren = getChildrenSafe<IDslNode>(newNode);

    if (oldChildren.length || newChildren.length) {
      const childOperations = this.diffChildren(oldChildren, newChildren, oldNode.id);

      operations.push(...childOperations);
    }

    return operations;
  }

  /**
   * @param oldChildren 旧子节点数组
   * @param newChildren 新子节点数组
   * @param parentId 父容器节点ID
   * @returns 更新操作数组
   */
  private diffChildren(
    oldChildren: readonly SceneNode[] = [],
    newChildren: IDslNode[] = [],
    parentId: string,
  ): Operation[] {
    const operations: Operation[] = [];

    // 1. 预处理：处理开始和结束的相同节点
    let i = 0; // 开始索引
    let e1 = oldChildren.length - 1; // 旧节点结束索引
    let e2 = newChildren.length - 1; // 新节点结束索引

    // 1.1 从头开始处理相同节点
    while (i <= e1 && i <= e2) {
      const n1 = oldChildren[i];
      const n2 = newChildren[i];

      if (isSameNodeType(n1, n2)) {
        // 递归处理相同类型节点
        operations.push(...this.diff(n1, n2));
      } else {
        // 找到第一个不同的节点，退出循环
        break;
      }
      i++;
    }

    // 1.2 从尾开始处理相同节点
    while (i <= e1 && i <= e2) {
      const n1 = oldChildren[e1];
      const n2 = newChildren[e2];

      if (isSameNodeType(n1, n2)) {
        operations.push(...this.diff(n1, n2));
      } else {
        break;
      }
      e1--;
      e2--;
    }

    // 2. 处理简单情况

    // 2.1 如果旧节点处理完，但新节点还有剩余，需要添加这些节点
    if (i > e1) {
      if (i <= e2) {
        for (let j = i; j <= e2; j++) {
          operations.push({
            type: 'CREATE',
            node: newChildren[j],
            parentId: parentId,
            index: j,
          });
        }
      }
    }

    // 2.2 如果新节点处理完，但旧节点还有剩余，需要删除这些节点
    else if (i > e2) {
      for (let j = i; j <= e1; j++) {
        operations.push({
          type: 'REMOVE',
          nodeId: oldChildren[j].id,
        });
      }
    }

    // 3. 处理中间部分 - 最复杂的情况
    else {
      const s1 = i; // 旧节点起始索引
      const s2 = i; // 新节点起始索引

      // 3.1 构建 key 到索引的映射
      const keyToNewIndexMap = new Map();
      for (let i = s2; i <= e2; i++) {
        const nextChild = newChildren[i] as any;
        if (nextChild.id != null) {
          keyToNewIndexMap.set(nextChild.id, i);
        }
      }

      // 需要处理的新节点数量
      const toBePatched = e2 - s2 + 1;
      // 记录新节点在旧节点中的位置映射
      const newIndexToOldIndexMap = new Array(toBePatched).fill(0);

      // 3.2 遍历旧节点，更新或删除
      let moved = false;
      let maxNewIndexSoFar = 0;

      for (let i = s1; i <= e1; i++) {
        const prevChild = oldChildren[i];

        // 优化：如果所有新节点都已处理，直接删除剩余旧节点
        if (keyToNewIndexMap.size === 0) {
          operations.push({
            type: 'REMOVE',
            nodeId: prevChild.id,
          });
          continue;
        }

        // 查找当前旧节点在新节点中的位置
        let newIndex;
        if (prevChild.id != null) {
          newIndex = keyToNewIndexMap.get(prevChild.id);
        } else {
          // 如果没有key，查找类型相同的节点
          for (let j = s2; j <= e2; j++) {
            if (newIndexToOldIndexMap[j - s2] === 0 && isSameNodeType(prevChild, newChildren[j])) {
              newIndex = j;
              break;
            }
          }
        }

        // 节点在新树中不存在，删除它
        if (newIndex === undefined) {
          operations.push({
            type: 'REMOVE',
            nodeId: prevChild.id,
          });
        }
        // 节点在新树中存在，需要更新它
        else {
          // 记录这个位置已被处理，i+1避免0值（0表示未处理）
          newIndexToOldIndexMap[newIndex - s2] = i + 1;

          // 检测是否需要移动节点
          if (newIndex >= maxNewIndexSoFar) {
            maxNewIndexSoFar = newIndex;
          } else {
            moved = true;
          }

          // 递归更新节点
          operations.push(...this.diff(prevChild, newChildren[newIndex]));
        }
      }

      // 3.3 处理节点移动和添加
      if (moved) {
        // 计算最长递增子序列
        const seq = getSequence(newIndexToOldIndexMap);
        let j = seq.length - 1;

        // 从后往前遍历
        for (let i = toBePatched - 1; i >= 0; i--) {
          const newIndex = i + s2;
          const newChild = newChildren[newIndex];

          // 如果节点不存在于旧树中，创建它
          if (newIndexToOldIndexMap[i] === 0) {
            operations.push({
              type: 'CREATE',
              node: newChild,
              parentId: parentId,
              index: newIndex,
            });
          }
          // 需要移动节点
          else if (moved) {
            if (j < 0 || i !== seq[j]) {
              operations.push({
                type: 'MOVE',
                nodeId: getOldNodeId(oldChildren, newIndexToOldIndexMap[i] - 1),
                parentId: parentId,
                index: newIndex,
              });
            } else {
              j--;
            }
          }
        }
      }
      // 不需要移动，但可能需要创建节点
      else {
        for (let i = toBePatched - 1; i >= 0; i--) {
          if (newIndexToOldIndexMap[i] === 0) {
            const newIndex = i + s2;
            const newChild = newChildren[newIndex];

            operations.push({
              type: 'CREATE',
              node: newChild,
              parentId: parentId,
              index: newIndex,
            });
          }
        }
      }
    }

    return operations;
  }

  /**
   * 执行操作
   * @param operations 操作数组
   */
  private async applyOperations(operations: Operation[]): Promise<void> {
    // 优化：按操作类型分组执行
    const removes = operations.filter((op) => op.type === 'REMOVE');
    const updates = operations.filter((op) => op.type === 'UPDATE_PROPS');
    const creates = operations.filter((op) => op.type === 'CREATE');
    const moves = operations.filter((op) => op.type === 'MOVE');

    // 优先执行删除操作
    await this.executeRemoves(removes);

    // 然后执行属性更新
    await this.executeUpdates(updates);

    // 接着执行创建操作
    await this.executeCreates(creates);

    // 最后执行移动操作
    await this.executeMoves(moves);
  }
}
