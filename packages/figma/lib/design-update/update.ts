import { IDslNode, IFrameNode, ITextNode } from '@tencent/h2d-html-parser';
import { Operation } from './types';
import { createFigmaNode } from '../design/node';
import { rounding } from '../utils/math';
import { getFigFontName } from '../design/fonts';
import { toFrameNodeAfter } from '../design/frame';
import { allPaintsIsBlackSolid } from '../utils/node';

type ExecuteOptions<T> = {
  before?: (op: T) => T;
  after?: (op: T, node: SceneNode) => void;
} & (T extends Extract<Operation, { type: 'CREATE' }>
  ? {
      excludeChildren?: boolean;
    }
  : {});

export abstract class Updater {
  abstract update(oldNode: BaseNode, newNode: IDslNode): Promise<void>;

  protected async executeRemoves<T extends Extract<Operation, { type: 'REMOVE' }>>(operations: T[]): Promise<void> {
    // 并行执行删除节点逻辑
    const all = operations.map(async (op) => {
      console.log('删除节点', op);

      if (!op.nodeId) {
        console.warn('尝试删除节点但未提供nodeId');
        return;
      }

      try {
        // 寻找节点
        const node = await figma.getNodeByIdAsync(op.nodeId);
        if (!node) {
          console.warn(`无法找到要删除的节点: ${op.nodeId}`);
          return;
        }

        // 检查节点是否可以被删除
        if ('remove' in node) {
          node.remove();
        } else {
          console.warn(`节点 ${op.nodeId} 不能被删除`);
        }
      } catch (error) {
        console.error(`删除节点 ${op.nodeId} 时出错:`, error);
      }
    });

    // 等待所有删除操作并行完成
    await Promise.all(all);
  }

  protected async executeCreates<T extends Extract<Operation, { type: 'CREATE' }>>(
    operations: T[],
    options?: ExecuteOptions<T>,
  ): Promise<void> {
    // 实现创建节点逻辑

    for (let op of operations) {
      op = options?.before ? options.before(op) : op;
      console.log('创建节点', op);
      if (!op.node) {
        console.warn('创建操作缺少必要参数');
        continue;
      }

      try {
        // 创建新节点
        const parentNode = await figma.getNodeByIdAsync(op.parentId);
        const node = options?.excludeChildren ? { ...op.node, children: [], id: undefined } : op.node;
        const newNode = await createFigmaNode(
          node,
          {
            x: 0,
            y: 0,
          },
          parentNode as any,
        );

        if (!newNode) {
          console.warn(`无法创建类型为 ${op.node.type} 的节点`);
          continue;
        }

        if (options?.after) {
          options.after(op, newNode);
        }
      } catch (error) {
        console.error(`创建节点时出错:`, error);
      }
    }
  }

  protected async executeMoves<T extends Extract<Operation, { type: 'MOVE' }>>(
    operations: T[],
    options?: ExecuteOptions<T>,
  ): Promise<void> {
    const all = operations.map(async (op) => {
      op = options?.before ? options.before(op) : op;

      // 获取要移动的节点
      console.log('移动节点', op);
      const { nodeId, parentId } = op;
      const node = (await figma.getNodeByIdAsync(nodeId)) as SceneNode;
      if (!node) {
        console.warn(`无法找到要移动的节点: ${nodeId}`);
        return;
      }

      // 获取目标父节点
      const targetParent = await figma.getNodeByIdAsync(parentId);

      if (!targetParent || !('insertChild' in targetParent)) {
        console.warn(`无法找到目标父节点或目标不支持子节点: ${parentId}`);
        return;
      }

      try {
        const parentChildren = (targetParent as ChildrenMixin).children;

        // 确保索引在有效范围内
        let insertIndex = Math.min(Math.max(0, op.index), parentChildren.length);

        // 检查节点是否已经在目标父节点中
        const currentParent = node.parent as ChildrenMixin | null;
        const currentIndex = currentParent ? currentParent.children.indexOf(node) : -1;

        // 如果当前位置就是目标位置，不需要移动
        if (currentParent === targetParent && currentIndex === insertIndex) {
          console.log(`节点 ${nodeId} 已在目标位置，无需移动`);
          return;
        }

        // 使用 insertChild 插入到目标位置
        (targetParent as ChildrenMixin).insertChild(insertIndex, node);

        if (options?.after) {
          options.after(op, node);
        }
        console.log(`成功移动节点 ${nodeId} 到 ${parentId} 的位置 ${insertIndex}`);
      } catch (error) {
        console.error(`移动节点 ${nodeId} 到位置 ${op.index} 时出错:`, error);
      }
    });
    await Promise.all(all);
  }

  protected async executeUpdates<T extends Extract<Operation, { type: 'UPDATE_PROPS' }>>(
    operations: T[],
  ): Promise<void> {
    // 实现更新节点属性逻辑
    const all = operations.map(async (op) => {
      console.log('更新节点属性', op);
      if (!op.nodeId || !op.props) {
        console.warn('更新操作缺少必要参数');
        return;
      }

      // 获取要更新的节点
      const node = (await figma.getNodeByIdAsync(op.nodeId)) as any;
      if (!node) {
        console.warn(`无法找到要更新的节点: ${op.nodeId}`);
        return;
      }

      // 处理集合
      const {
        width,
        height,
        fontFamily,
        fontWeight,
        constraints,
        maxHeight,
        maxWidth,
        minHeight,
        minWidth,
        layoutPositioning,
        textAutoResize,
        relativeTransform,
        ...rest
      } = op.props;
      if ((width || height) && 'resize' in node) {
        node.resize(rounding(width ?? node.width), rounding(height ?? node.height));
      }

      if ((fontFamily || fontWeight) && 'fontName' in node) {
        const fontName = getFigFontName({
          fontFamily: fontFamily ?? node.fontName.family,
          fontWeight: fontWeight ?? node.fontName.style,
        } as ITextNode);
        await figma.loadFontAsync(fontName);
        node.fontName = fontName;
      }

      if (constraints || maxHeight || maxWidth || minHeight || minWidth || layoutPositioning) {
        toFrameNodeAfter(
          {
            constraints,
            maxHeight,
            maxWidth,
            minHeight,
            minWidth,
            layoutPositioning,
          } as IFrameNode,
          node,
          node.parent,
        );
      }

      if (node.type === 'TEXT') {
        node.textAutoResize = 'WIDTH_AND_HEIGHT';
      }

      // 应用所有更改的属性
      for (const [key, value] of Object.entries(rest)) {
        try {
          // 处理特殊属性
          if (['x', 'y'].includes(key) && key in node) {
            node[key] = rounding(value);
          } else if (key === 'fills' && 'fills' in node) {
            // 转换填充属性格式
            await this.applyFills(node, value);
          } else if (key === 'characters' && 'characters' in node) {
            // 更新文本内容
            node.characters = String(value);
            node.name = String(value);
          } else if (key in node) {
            // 应用常规属性
            (node as any)[key] = value;
          } else {
            console.warn(`节点 ${node.type} 不支持属性: ${key}`);
          }
        } catch (error) {
          console.error(`更新节点 ${op.nodeId} 属性时出错:`, error);
        }
      }
      if (relativeTransform) {
        node.relativeTransform = relativeTransform;
      }
    });
    await Promise.all(all);
  }

  // 辅助方法: 应用填充
  private async applyFills(node: FrameNode, fills: any[]): Promise<void> {
    // 处理 SVG
    const vectorShapes = node.findAll?.((node) => node.type === 'VECTOR');
    if (fills.length && vectorShapes?.length) {
      // 遍历节点，找到需要更改颜色的形状
      // 遍历所有 VECTOR 节点并更改它们的颜色
      vectorShapes.forEach((shape) => {
        const currShape = shape as VectorNode;
        /**
         * svg fill 属性有值时，优先级大于 css color
         * 当前形状填充为黑色 SolidFill 时，分两种情况：
         * 1. svg 默认填充
         * 2. svg fill 属性为此颜色
         * 目前两种情况统一使用 node.fills (css color) 覆盖
         **/
        if (currShape.fills !== figma.mixed && allPaintsIsBlackSolid(currShape.fills)) {
          currShape.fills = fills;
        }
        if (allPaintsIsBlackSolid(currShape.strokes)) {
          currShape.strokes = fills;
        }
      });
      return;
    }

    // 处理图片填充等需要加载的资源
    for (const fill of fills) {
      if (fill.type === 'IMAGE' && fill.imageHash) {
        // 确保图片已加载
        figma.getImageByHash(fill.imageHash);
      }
    }
    node.fills = fills;
  }
}
