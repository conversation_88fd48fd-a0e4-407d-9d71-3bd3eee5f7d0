import { IDslNode } from '@tencent/h2d-html-parser';

import { ChildrenType } from './types';

/**
 * 深度比较两个值是否相等
 */
export function isEqual(a: any, b: any): boolean {
  if (a === b) return true;

  if (a && b && typeof a === 'object' && typeof b === 'object') {
    if (Array.isArray(a) && Array.isArray(b)) {
      if (a.length !== b.length) return false;
      for (let i = 0; i < a.length; i++) {
        if (!isEqual(a[i], b[i])) return false;
      }
      return true;
    }

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);

    if (keysA.length !== keysB.length) return false;

    for (let key of keysA) {
      if (!keysB.includes(key) || !isEqual(a[key], b[key])) {
        return false;
      }
    }

    return true;
  }

  return false;
}

/**
 * 最长递增子序列算法
 * 使用贪心+二分查找实现，时间复杂度O(nlogn)
 * @returns 返回索引
 */
export function getSequence(arr: number[]): number[] {
  const len = arr.length;
  // 边界情况处理
  if (len <= 1) return len === 0 ? [] : [0];

  // result数组将保存当前最优解的索引集合
  const result = [0];
  // p数组记录每个位置的前驱节点索引
  const p = new Array(len).fill(0);

  for (let i = 1; i < len; i++) {
    // 当前值大于结果数组中的最后一个元素，直接追加
    if (arr[i] > arr[result[result.length - 1]]) {
      p[i] = result[result.length - 1];
      result.push(i);
      continue;
    }

    // 二分查找，找到result数组中第一个大于等于arr[i]的位置
    let left = 0;
    let right = result.length - 1;

    while (left < right) {
      const mid = Math.floor((left + right) / 2);
      if (arr[result[mid]] < arr[i]) {
        left = mid + 1;
      } else {
        right = mid;
      }
    }

    // 更新result数组
    if (arr[i] < arr[result[left]]) {
      if (left > 0) {
        p[i] = result[left - 1];
      }
      result[left] = i;
    }
  }

  // 回溯构建最终结果
  let len2 = result.length;
  let lastIndex = result[len2 - 1];

  // 从后向前回溯，确保得到正确的序列
  while (len2-- > 0) {
    result[len2] = lastIndex;
    lastIndex = p[lastIndex];
  }

  return result;
}

/**
 * 比较两个节点是否为相同类型（可复用）
 */
export function isSameNodeType(n1: BaseNode, n2: IDslNode): boolean {
  return n1.type === n2.type && n1.id === (n2 as any).id;
}

/**
 * 根据索引获取旧节点的ID
 */
export function getOldNodeId(oldChildren: readonly SceneNode[], index: number): string {
  return oldChildren[index].id;
}

/**
 * 比较节点属性差异
 */
export function diffProps(oldNode: BaseNode, newNode: IDslNode) {
  const changes: Record<string, any> = {};
  const excludeProps = ['id', 'parent', 'children', 'type', 'name'];

  if ((oldNode?.parent as FrameNode)?.layoutMode === 'NONE' && (oldNode as FrameNode).layoutMode === 'NONE') {
    excludeProps.push('maxHeight', 'maxWidth', 'minHeight', 'minWidth');
  }

  // 遍历新节点的所有属性
  for (const [key, value] of Object.entries(newNode)) {
    // 跳过排除的属性和内部属性
    if (excludeProps.includes(key) || key.startsWith('$')) {
      continue;
    }

    // 只有当属性值不同时才记录变更
    if (key in oldNode && value !== undefined && !isEqual((oldNode as any)[key], value)) {
      changes[key] = value;
    }
  }

  if (Object.keys(changes).length === 0) {
    return;
  }
  return changes;
}

// 安全获取子节点，保持类型安全
export function getChildrenSafe<T extends Object>(node: T): ChildrenType<T> extends never ? [] : ChildrenType<T> {
  return ('children' in node ? (node as any).children : []) as any;
}
