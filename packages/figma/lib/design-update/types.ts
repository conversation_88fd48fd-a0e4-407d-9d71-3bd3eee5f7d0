import { IDslNode } from '@tencent/h2d-html-parser';

// 操作类型
export type Operation =
  | { type: 'CREATE'; node: IDslNode; parentId: string; index: number }
  | { type: 'UPDATE_PROPS'; nodeId: string; props: Record<string, any> }
  | { type: 'REMOVE'; nodeId: string }
  | { type: 'MOVE'; nodeId: string; parentId: string; index: number };

export abstract class Updater {
  abstract update(oldNode: BaseNode, newNode: IDslNode): Promise<void>;
}

// 条件类型：如果T有children属性则返回children类型，否则返回never
export type ChildrenType<T> = T extends { children: infer C } ? C : never;
