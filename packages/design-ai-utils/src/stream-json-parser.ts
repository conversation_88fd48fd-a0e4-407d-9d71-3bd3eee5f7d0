import { parsePartialJSON } from './parse-partial-json';

export type ParseResult =
  | {
      success: boolean;
      result: any;
      isComplete: boolean;
      error?: any;
    }
  | undefined;

export class StreamJSONParser {
  buffer = '';
  isComplete = false;

  feed(chunk: string) {
    try {
      if (this.isComplete) {
        throw new Error('JSON 已解析完毕，不会再继续处理数据，请 reset 后重新开始');
      }
      if (chunk.length === 0) {
        return {
          success: false,
          isComplete: this.isComplete,
          result: undefined,
        };
      }
      this.buffer += chunk;
      this.buffer = this.buffer.trim();
      if (!this.buffer) {
        return {
          success: false,
          isComplete: this.isComplete,
          result: undefined,
        };
      }
      return this.tryParseJSON();
    } catch (err) {
      return {
        success: false,
        error: err,
        isComplete: this.isComplete,
        result: undefined,
      };
    }
  }

  reset() {
    this.buffer = '';
    this.isComplete = false;
  }

  private tryParseJSON(): ParseResult {
    const successRes = (result: any) => {
      return {
        success: true,
        result,
        isComplete: this.isComplete,
      };
    };
    // 找到第一个有效的 JSON 左括号
    const startIndex = this.buffer.search(/[{[]/);
    if (startIndex === -1) {
      return;
    }

    // 找到有效括号，删除前面的内容
    if (startIndex > 0) {
      this.buffer = this.buffer.slice(startIndex);
    }

    // 找到最后一个右括号的位置
    const lastCloseBrace = this.buffer.lastIndexOf('}');
    const lastCloseBracket = this.buffer.lastIndexOf(']');
    const lastClosePos = Math.max(lastCloseBrace, lastCloseBracket);

    if (lastClosePos === -1) {
      const value = parsePartialJSON(this.buffer);
      if (value.state === 'successful-parse' || value.state === 'repaired-parse') {
        return successRes(value.value);
      }
      return;
    }

    try {
      const value = JSON.parse(this.buffer.slice(0, lastClosePos + 1));
      this.isComplete = true;
      return successRes(value);
    } catch {
      // 失败说明JSON还不完整，解析部分数据
      const value = parsePartialJSON(this.buffer);
      if (value.state === 'successful-parse' || value.state === 'repaired-parse') {
        return successRes(value.value);
      }
    }
  }
}
