import { z } from 'zod';

import { parsePartialJSON } from './parse-partial-json';
import { DeepPartial, PageArchitectureWithComponentSchema } from './zod-schema';

type PageArchitecture = DeepPartial<z.infer<typeof PageArchitectureWithComponentSchema>>;

export const extractArchitecture = (originalText: string): { text: string; architecture: PageArchitecture } => {
  const unParsedRes = {
    text: originalText,
    architecture: {},
  };

  if (!originalText?.trim()) {
    return unParsedRes;
  }

  const firstOpenTagIndex = originalText.indexOf('<');
  if (firstOpenTagIndex === -1) return unParsedRes;

  const normalText = originalText.substring(0, firstOpenTagIndex);
  const architectureWholeText = originalText.substring(firstOpenTagIndex);

  // 提取第一个{和后面的<之前的片段
  const match = architectureWholeText.match(/\{.*?(?=<|$)/s);

  const architectureJsonText = match ? match[0] : '';
  if (architectureJsonText) {
    try {
      const archObj = JSON.parse(architectureJsonText);
      return {
        text: normalText,
        architecture: archObj,
      };
    } catch {
      const parsedResult = parsePartialJSON(architectureJsonText);
      if (parsedResult.state === 'successful-parse' || parsedResult.state === 'repaired-parse') {
        return {
          text: normalText,
          architecture: parsedResult.value as PageArchitecture,
        };
      }
    }
  }

  return {
    text: normalText,
    architecture: {},
  };
};
