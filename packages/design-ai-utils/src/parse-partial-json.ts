export type JSONValue = null | string | number | boolean | JSONObject | JSONArray;
export type JSONObject = {
  [key: string]: JSONValue;
};
export type JSONArray = JSONValue[];

/**
 * 解析JSON，如果解析失败，则尝试修复
 * @param str 部分JSON字符串
 * @returns 解析结果
 * - successful-parse: 解析成功
 * - repaired-parse: 解析失败，但尝试修复后成功
 * - failed-parse: 解析失败，无法修复
 * - empty-input: 输入为空(trim后为空也属于空)
 */
export function parsePartialJSON(str: string): {
  state: 'successful-parse' | 'repaired-parse' | 'failed-parse' | 'empty-input';
  value: JSONValue;
} {
  if (!str.trim()) return { state: 'empty-input', value: null };

  try {
    // 先尝试直接解析
    const value = JSON.parse(str);
    return {
      state: 'successful-parse',
      value,
    };
  } catch {
    // 如果解析失败，直接使用部分解析逻辑
    try {
      const { length } = str;
      let index = 0;

      const throwError = (msg: string) => {
        throw new Error(`${msg}（位置：${index}）`);
      };

      const parseAny = () => {
        skipBlank();
        if (index >= length) throwError('JSON 数据意外结束');

        if (str[index] === '"') return parseStr();
        if (str[index] === '{') return parseObj();
        if (str[index] === '[') return parseArr();
        return parseNum();
      };

      const parseStr: () => string = () => {
        let result = '"'; // 开始构建结果字符串
        index++; // 跳过左引号

        while (index < length) {
          const char = str[index];

          if (char === '\\' && index + 1 < length) {
            // 处理已转义的字符
            result += char + str[index + 1];
            index += 2;
            continue;
          }

          if (char === '"') {
            // 遇到双引号，需要判断是否应该转义
            let j = index + 1;

            // 跳过空白字符
            while (j < str.length && ' \t\n\r'.includes(str[j])) {
              j++;
            }

            if (j < str.length) {
              const nextChar = str[j];
              // 如果后面是JSON的分隔符，这是真正的字符串结束
              if ([',', '}', ']', ':'].includes(nextChar)) {
                result += char;
                index++;
                break;
              } else {
                // 否则转义这个双引号
                result += '\\"';
                index++;
                continue;
              }
            } else {
              // 字符串末尾，结束字符串
              result += char;
              index++;
              break;
            }
          } else {
            result += char;
            index++;
          }
        }

        try {
          return JSON.parse(result);
        } catch {
          // 如果解析失败，尝试补全结束引号
          if (!result.endsWith('"')) {
            result += '"';
          }
          try {
            return JSON.parse(result);
          } catch {
            throwError('字符串格式错误');
          }
        }
      };

      const parseObj = () => {
        index++; // 跳过左大括号
        skipBlank();
        const obj: Record<string, any> = {};
        try {
          while (str[index] !== '}') {
            skipBlank();
            if (index >= length) return obj;
            const key = parseStr();
            skipBlank();
            index++; // 跳过冒号
            try {
              const value = parseAny();
              obj[key] = value;
            } catch {
              return obj;
            }
            skipBlank();
            if (str[index] === ',') index++; // 跳过逗号
          }
        } catch {
          return obj;
        }
        index++; // 跳过右大括号
        return obj;
      };

      const parseArr = () => {
        index++; // 跳过左括号
        const arr: any[] = [];
        try {
          while (str[index] !== ']') {
            arr.push(parseAny());
            skipBlank();
            if (str[index] === ',') {
              index++; //  跳过逗号
            }
          }
        } catch {
          return arr;
        }
        index++; // 跳过右括号
        return arr;
      };

      const parseNum = () => {
        if (index === 0) {
          if (str === '-') throwError('无效的负号"-"');
          try {
            return JSON.parse(str);
          } catch {
            return JSON.parse(str.substring(0, str.lastIndexOf('e')));
          }
        }

        const start = index;

        if (str[index] === '-') index++;
        while (str[index] && ',]}'.indexOf(str[index]) === -1) index++;

        try {
          return JSON.parse(str.substring(start, index));
        } catch {
          if (str.substring(start, index) === '-') throwError('无效的负号"-"');
          try {
            return JSON.parse(str.substring(start, str.lastIndexOf('e')));
          } catch {
            throwError('数字格式错误');
          }
        }
      };

      const skipBlank = () => {
        while (index < length && ' \n\r\t'.includes(str[index])) {
          index++;
        }
      };

      const parsedValue = parseAny();
      return {
        state: 'repaired-parse',
        value: parsedValue,
      };
    } catch (error) {
      console.error(error);
      return {
        state: 'failed-parse',
        value: null,
      };
    }
  }
}
