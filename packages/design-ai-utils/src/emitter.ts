import { Subject, Subscription } from 'rxjs';

/**
 * 事件触发器
 */
export class Emitter<T> {
  private subject = new Subject<T>();
  private subscription: null | Subscription = null;

  public on(listener: (data: T) => void): Subscription {
    if (this.subscription === null) {
      this.subscription = this.subject.subscribe((data) => {
        listener(data);
      });
    }

    return this.subscription;
  }

  public emit(data: T) {
    this.subject.next(data);
  }

  public off() {
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }
  }
}
