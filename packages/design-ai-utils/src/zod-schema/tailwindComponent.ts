import { z } from 'zod';

export const TailwindComponentSchema = z.object({
  name: z.string().describe('组件名称'),
  description: z.string().describe('组件描述'),
  code: z.string().describe('组件代码，如果存在variants，则不存在该属性').optional(),
  variants: z
    .array(
      z.object({
        description: z.string().describe('组件变体描述'),
        code: z.string().describe('组件变体代码'),
      }),
    )
    .describe('组件的各个变体，如果存在variants，你需要继续从中根据 `description` 选择一个组件')
    .optional(),
});
