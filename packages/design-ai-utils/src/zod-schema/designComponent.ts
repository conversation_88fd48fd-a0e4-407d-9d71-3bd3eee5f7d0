import { z } from 'zod';

export const ComponentSetVariantSchema = z.object({
  n: z.string().describe('变体name'),
  opts: z.array(z.string()).describe('此变体的可选value'),
});

export const ComponentSetVariantCombinationSchema = z.object({
  id: z.string().describe('变体组件ID'),
  ps: z
    .record(z.string().describe('变体name'), z.string().describe('此变体的value'))
    .describe('变体组合属性,Properties的缩写'),
});

export const DesignComponentsSchema = z.array(
  z.object({
    id: z.string().describe('组件ID，COMPONENT_SET不存在id，需要使用变体组件variantCombinations'),
    n: z.string().describe('组件名称'),
    t: z
      .enum(['COMPONENT_SET', 'COMPONENT'])
      .describe('组件类型（type的缩写），COMPONENT_SET表示组件集，COMPONENT表示组件，组件集中包含多个变体组件'),
    d: z.string().optional().describe('组件描述, description的缩写'),
    v: z.array(ComponentSetVariantSchema).optional().describe('组件集的所有变体,variant的缩写'),
    vc: z
      .array(ComponentSetVariantCombinationSchema)
      .optional()
      .describe('所有可用的变体组件,variantCombinations的简写'),
  }),
);

export const ImageResourceSchema = z
  .object({
    id: z.string().describe('图片ID'),
    n: z.string().describe('图片名称，有时可以说明图片的用途'),
  })
  .describe('组件的图片资源');

export const TextResourceSchema = z
  .object({
    id: z.string().describe('文本ID'),
    n: z.string().describe('文本名称，有时可以说明文本的用途'),
    dv: z.string().describe('文本的默认内容，defaultValue的缩写'),
  })
  .describe('组件的文本资源');

export const DesignComponentsWithResourceSchema = z.array(
  z.object({
    id: z.string().describe('组件ID'),
    pid: z.string(),
    n: z.string().describe('组件名称'),
    d: z.string().optional().describe('组件描述, description的缩写'),
    w: z.number().describe('组件宽度'),
    h: z.number().describe('组件高度'),
    imgs: z.array(ImageResourceSchema).optional(),
    texts: z.array(TextResourceSchema).optional(),
  }),
);

export const DesignComponentInstanceConfigSchame = z.object({
  id: z.string().describe('组件ID'),
  pid: z.string(),
  imgs: z.array(
    z.object({
      id: z.string().describe('图片ID'),
      alt: z.string().describe('图片描述'),
      size: z.string().describe('图片尺寸，格式为宽*高，例如200*200'),
      src: z.string().describe('图片src，不需要设置这个属性'),
    }),
  ),
  texts: z.array(
    z.object({
      id: z.string().describe('文本ID'),
      value: z.string().describe('文本内容'),
    }),
  ),
});
