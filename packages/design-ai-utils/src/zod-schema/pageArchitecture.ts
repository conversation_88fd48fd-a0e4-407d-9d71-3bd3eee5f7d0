import { z } from 'zod';

export const SectionSchema = z.object({
  id: z.string().length(6).describe('区块ID，需要和页面ID有联系，同一应用内不允许存在相同ID的区块'),
  n: z.string().describe('区块名称，同一页面内不允许存在相同名称的区块'),
  d: z.string().describe('区块UI描述'),
});

export const PageSchema = z.object({
  id: z.string().length(3).describe('页面ID，同一应用内不允许存在相同ID的页面'),
  n: z.string().describe('页面名称，同一应用内不允许存在相同名称的页面'),
  sections: z.array(SectionSchema),
});

export const SectionWithComponentSchema = SectionSchema.extend({
  dc: z.array(z.string()).describe('选中组件id数组，dc是designComponent的缩写'),
});

export const PageWithComponentSchema = PageSchema.extend({
  sections: z.array(SectionWithComponentSchema),
});

export const PageArchitectureSchema = z.object({
  an: z.string().describe('应用名称, appName的缩写'),
  // sty: z.string().describe('样式风格描述, styleGuide的缩写'),
  pages: z.array(PageSchema),
});

export const PageArchitectureWithComponentSchema = PageArchitectureSchema.extend({
  pages: z.array(PageWithComponentSchema),
});
