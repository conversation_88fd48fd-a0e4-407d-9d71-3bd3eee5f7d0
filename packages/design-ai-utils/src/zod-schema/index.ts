import { zodToJsonSchema } from 'zod-to-json-schema';
import { z } from 'zod';

export function zodToJsonSchemaString(zodObj: z.ZodType) {
  return JSON.stringify(zodToJsonSchema(zodObj));
}

export * from './pageArchitecture';
export * from './tailwindComponent';
export * from './designComponent';
export * from './templateHtml';
/**
 * 将T的所有属性（包括嵌套属性）变为可选
 * - 基本类型：直接返回
 * - 数组类型：递归处理数组元素类型
 * - 函数类型：直接返回
 * - 对象类型：递归处理每个属性
 */
export type DeepPartial<T> =
  T extends Array<infer U>
    ? Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends Function
        ? T
        : T extends object
          ? { [K in keyof T]?: DeepPartial<T[K]> }
          : T;

/**
 * 将T的所有属性（包括嵌套属性）变为必选
 * - 基本类型：直接返回
 * - 数组类型：递归处理数组元素类型
 * - 函数类型：直接返回
 * - 对象类型：递归处理每个属性
 */
export type DeepRequired<T> =
  T extends Array<infer U>
    ? Array<DeepRequired<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepRequired<U>>
      : T extends Function
        ? T
        : T extends object
          ? { [K in keyof T]-?: DeepRequired<T[K]> }
          : T;
