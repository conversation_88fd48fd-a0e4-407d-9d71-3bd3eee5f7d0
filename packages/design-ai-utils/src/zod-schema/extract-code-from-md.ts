/**
 * 从 markdown 中提取代码块
 * @param mdContent markdown 内容
 * @param codeTag 代码块的标签
 * @returns 代码块数组
 */
export function extractCodeFromMd(mdContent: string, codeTag: string): string[] {
  // 早期返回：简化输入验证
  if (!mdContent || !codeTag || typeof mdContent !== 'string' || typeof codeTag !== 'string') {
    return [];
  }

  try {
    // 转义特殊字符，避免正则表达式注入
    const escapedCodeTag = codeTag.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // 使用更精确的正则表达式，匹配代码块并捕获内容
    const regex = new RegExp(`^\`\`\`${escapedCodeTag}\\s*\\n([\\s\\S]*?)\\n\`\`\``, 'gm');

    // 使用现代 matchAll API 替代 while 循环
    const matches = Array.from(mdContent.matchAll(regex));

    // 提取匹配的代码内容，去除首尾空白但保留内部格式
    return matches.map((match) => match[1]?.trim() || '').filter((code) => code.length > 0); // 过滤空代码块
  } catch {
    return [];
  }
}
