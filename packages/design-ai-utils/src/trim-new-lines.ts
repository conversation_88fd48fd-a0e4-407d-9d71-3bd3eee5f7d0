/**
 * 删除开头和结尾的换行符，将中间连续的换行符替换为单个换行符
 * @param value 字符串
 * @returns 删除开头和结尾的换行符，将中间连续的换行符替换为单个换行符后的字符串
 * @example
 * ```ts
 * trimNewlines`
 *   <div>
 *     <h1>Hello</h1>
 *   </div>`;
 *
 * trimNewlines('  <div>\n    <h1>Hello</h1>\n  </div>');
 */
export function trimNewlines(value: string): string;
export function trimNewlines(strings: TemplateStringsArray, ...values: any[]): string;
export function trimNewlines(arg0: string | TemplateStringsArray, ...values: any[]) {
  if (typeof arg0 !== 'string') {
    const processedString = arg0.reduce((acc, curr, i) => {
      acc += curr + (values[i] ?? '');
      return acc;
    }, '');

    return _trimNewlines(processedString);
  }

  return _trimNewlines(arg0);
}

function _trimNewlines(value: string) {
  return value
    .replace(/[\r\n]{2,}/g, '\n') // 先将连续的换行符替换为单个换行符
    .replace(/^[\r\n]/, '') // 删除开头的所有换行符
    .replace(/[\r\n]$/, ''); // 删除结尾的所有换行符
}
