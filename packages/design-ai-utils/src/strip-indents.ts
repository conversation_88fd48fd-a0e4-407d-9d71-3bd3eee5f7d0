/**
 * 移除字符串中的缩进，支持模板字符串
 * @param value 字符串
 * @returns 移除缩进后的字符串
 * @example
 * ```ts
 * stripIndents`
 *   <div>
 *     <h1>Hello</h1>
 *   </div>`;
 *
 * stripIndents('  <div>\n    <h1>Hello</h1>\n  </div>');
 */
export function stripIndents(value: string): string;
export function stripIndents(strings: TemplateStringsArray, ...values: any[]): string;
export function stripIndents(arg0: string | TemplateStringsArray, ...values: any[]) {
  if (typeof arg0 !== 'string') {
    const processedString = arg0.reduce((acc, curr, i) => {
      acc += curr + (values[i] ?? '');
      return acc;
    }, '');

    return _stripIndents(processedString);
  }

  return _stripIndents(arg0);
}

function _stripIndents(value: string) {
  return value
    .split('\n')
    .map((line) => line.trim())
    .join('\n')
    .trimStart()
    .replace(/[\r\n]$/, '');
}
