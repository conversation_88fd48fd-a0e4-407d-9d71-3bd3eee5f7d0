/**
 * 从 markdown 中提取代码块
 * @param mdContent markdown 内容
 * @param codeTag 代码块的标签
 * @returns 代码块数组
 */
export function extractCodeFromMd(mdContent: string, codeTag: string): string[] {
  if (!mdContent || !codeTag || typeof mdContent !== 'string' || typeof codeTag !== 'string') {
    return [];
  }

  try {
    // 转义特殊字符，避免正则表达式注入
    const escapedCodeTag = codeTag.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    const regex = new RegExp(`^\`\`\`${escapedCodeTag}\\s*\\n([\\s\\S]*?)\\n\`\`\``, 'gm');
    const matches = Array.from(mdContent.matchAll(regex));
    return matches.map((match) => match[1]?.trim() || '').filter((code) => code.length > 0);
  } catch {
    return [];
  }
}
