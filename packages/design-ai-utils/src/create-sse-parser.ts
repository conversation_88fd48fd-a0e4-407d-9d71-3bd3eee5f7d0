/**
 * Represents a message sent in an event stream
 * https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events#Event_stream_format
 */
export interface EventSourceMessage {
  /** The event ID to set the EventSource object's last event ID value. */
  id: string;
  /** A string identifying the type of event described. */
  event: string;
  /** The event data */
  data: string;
  /** The reconnection interval (in milliseconds) to wait before retrying the connection */
  retry?: number;
}

export type SSEFields = 'data' | 'event' | 'id' | 'retry';

/**
 * 创建一个解析 SSE 的 TransformStream
 * @returns TransformStream
 */
export const createSSEParser = () => {
  let buffer = '';

  function newMessage(): EventSourceMessage {
    return {
      data: '',
      event: '',
      id: '',
      retry: undefined,
    };
  }

  return new TransformStream<string, EventSourceMessage>({
    transform(streamChunk, controller) {
      buffer += streamChunk;
      const parts = buffer.split('\n\n');

      parts.slice(0, -1).forEach((part) => {
        let message = newMessage();

        // 处理每一行
        const lines = part.split('\n');
        
        for (const line of lines) {
          // 空行表示消息结束（但在我们的split逻辑中，这种情况由\n\n处理）
          if (line.length === 0) {
            continue;
          }

          const colonIndex = line.indexOf(':');
          
          // 注释行（以冒号开头）或无字段名的行
          if (colonIndex === 0) {
            continue; // 跳过注释行
          }
          
          let field: string;
          let value: string;

          if (colonIndex === -1) {
            // 没有冒号的行，整行作为字段名，值为空字符串
            field = line;
            value = '';
          } else {
            // 提取字段名
            field = line.slice(0, colonIndex);
            
            // 提取值，如果冒号后面紧跟一个空格则忽略这个空格
            const valueStart = colonIndex + 1;
            const rawValue = line.slice(valueStart);
            value = rawValue.startsWith(' ') ? rawValue.slice(1) : rawValue;
          }

          // 处理各个字段
          switch (field) {
            case 'data':
              // 如果消息已经有data，用换行符连接新值
              message.data = message.data
                ? message.data + '\n' + value
                : value;
              break;
            case 'event':
              message.event = value;
              break;
            case 'id':
              message.id = value;
              break;
            case 'retry':
              const retry = parseInt(value, 10);
              if (!isNaN(retry)) { // 按规范，忽略非整数
                message.retry = retry;
              }
              break;
          }
        }

        controller.enqueue(message);
      });

      buffer = parts[parts.length - 1] || '';
    },
  });
};
