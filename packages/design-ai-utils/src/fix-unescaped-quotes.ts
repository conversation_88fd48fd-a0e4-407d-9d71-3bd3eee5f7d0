/**
 * 修复JSON字符串中的未转义双引号
 * @param str 原始JSON字符串
 * @returns 修复后的JSON字符串
 */
export function fixUnescapedQuotes(str: string): string {
  let result = '';
  let inString = false;
  let i = 0;

  while (i < str.length) {
    const char = str[i];

    if (char === '\\' && i + 1 < str.length) {
      // 处理转义字符
      result += char + str[i + 1];
      i += 2;
      continue;
    }

    if (char === '"') {
      if (!inString) {
        // 字符串开始
        inString = true;
        result += char;
      } else {
        // 可能的字符串结束，检查后面的字符
        let j = i + 1;

        // 跳过空白字符
        while (j < str.length && ' \t\n\r'.includes(str[j])) {
          j++;
        }

        if (j < str.length) {
          const nextChar = str[j];
          // 如果后面是JSON的分隔符，这是真正的字符串结束
          if ([',', '}', ']', ':'].includes(nextChar)) {
            inString = false;
            result += char;
          } else {
            // 否则转义这个双引号
            result += '\\"';
          }
        } else {
          // 字符串末尾，结束字符串
          inString = false;
          result += char;
        }
      }
    } else {
      result += char;
    }

    i++;
  }

  return result;
}
