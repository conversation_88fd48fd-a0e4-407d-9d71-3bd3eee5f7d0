import { describe, it, expect } from 'vitest';
import { trimNewlines } from '../trim-new-lines';

describe('trimNewlines - 删除换行符处理', () => {
  describe('字符串用法', () => {
    it('应该删除开头和结尾的换行符', () => {
      const input = '\n\n<div>Hello</div>\n\n';
      const expected = '<div>Hello</div>';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该将多个连续换行符替换为单个换行符', () => {
      const input = '<div>\n\n\nHello\n\n\nWorld\n\n\n</div>';
      const expected = '<div>\nHello\nWorld\n</div>';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该处理只包含换行符的字符串', () => {
      const input = '\n\n\n\n';
      const expected = '';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该处理空字符串', () => {
      expect(trimNewlines('')).toBe('');
    });

    it('应该处理不包含换行符的字符串', () => {
      const input = 'Hello World';
      const expected = 'Hello World';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该处理只有单个换行符的字符串', () => {
      const input = 'Hello\nWorld';
      const expected = 'Hello\nWorld';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该处理回车符', () => {
      const input = '\r\n\r\n<div>Hello</div>\r\n\r\n';
      const expected = '<div>Hello</div>';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该处理混合行结束符', () => {
      const input = '\n\r\n<div>\r\n\r\nHello\n\n</div>\r\n';
      const expected = '<div>\nHello\n</div>';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该处理复杂HTML结构但保留缩进', () => {
      const input = '\n\n\n<div>\n\n\n  <h1>Title</h1>\n\n\n  <p>Content</p>\n\n\n</div>\n\n\n';
      const expected = '<div>\n  <h1>Title</h1>\n  <p>Content</p>\n</div>';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该保留空格和其他空白字符但处理换行符', () => {
      const input = '\n\n  <div>  \n\n\n    Hello World    \n\n\n  </div>  \n\n';
      const expected = '  <div>  \n    Hello World    \n  </div>  ';
      expect(trimNewlines(input)).toBe(expected);
    });

    it('应该按正确顺序处理：先合并连续换行符再删除首尾换行符', () => {
      const input = '\n\n\nstart\n\n\n\nmiddle\n\n\n\nend\n\n\n';
      const expected = 'start\nmiddle\nend';
      expect(trimNewlines(input)).toBe(expected);
    });
  });

  describe('模板字符串用法', () => {
    it('应该删除模板字符串中的换行符但保留缩进', () => {
      const result = trimNewlines`

        <div>
          Hello World
        </div>

`;
      const expected = '        <div>\n          Hello World\n        </div>';
      expect(result).toBe(expected);
    });

    it('应该处理带有插值的模板字符串并保留缩进', () => {
      const title = 'World';
      const content = 'Hello there!';
      const result = trimNewlines`


        <div>


          <h1>${title}</h1>


          <p>${content}</p>


        </div>


`;
      const expected = '        <div>\n          <h1>World</h1>\n          <p>Hello there!</p>\n        </div>';
      expect(result).toBe(expected);
    });

    it('应该处理带有数字插值的模板字符串', () => {
      const count = 42;
      const price = 19.99;
      const result = trimNewlines`


        <div>


          <span>Count: ${count}</span>


          <span>Price: $${price}</span>


        </div>


`;
      const expected =
        '        <div>\n          <span>Count: 42</span>\n          <span>Price: $19.99</span>\n        </div>';
      expect(result).toBe(expected);
    });

    it('应该处理空模板字符串', () => {
      const result = trimNewlines``;
      expect(result).toBe('');
    });

    it('应该处理只包含换行符的模板字符串', () => {
      const result = trimNewlines`



`;
      expect(result).toBe('');
    });

    it('应该处理带有布尔值插值的模板字符串', () => {
      const isActive = true;
      const isVisible = false;
      const result = trimNewlines`


        <div>


          <span>Active: ${isActive}</span>


          <span>Visible: ${isVisible}</span>


        </div>


`;
      const expected =
        '        <div>\n          <span>Active: true</span>\n          <span>Visible: false</span>\n        </div>';
      expect(result).toBe(expected);
    });

    it('应该处理带有对象插值的模板字符串', () => {
      const user = { name: 'John', age: 30 };
      const result = trimNewlines`


        <div>


          <span>User: ${JSON.stringify(user)}</span>


        </div>


`;
      const expected = '        <div>\n          <span>User: {"name":"John","age":30}</span>\n        </div>';
      expect(result).toBe(expected);
    });

    it('undefined/null插值应该为空字符串', () => {
      const undefinedVar = undefined;
      const nullVar = null;
      const result = trimNewlines`


        <div>


          <span>Undefined: ${undefinedVar}</span>


          <span>Null: ${nullVar}</span>


        </div>


`;
      const expected =
        '        <div>\n          <span>Undefined: </span>\n          <span>Null: </span>\n        </div>';
      expect(result).toBe(expected);
    });

    it('应该正确处理实际的缩进空格', () => {
      const result = trimNewlines`

  <div class="container">

    <h1>Title</h1>

    <p>Content</p>

  </div>

`;
      const expected = '  <div class="container">\n    <h1>Title</h1>\n    <p>Content</p>\n  </div>';
      expect(result).toBe(expected);
    });
  });
});
