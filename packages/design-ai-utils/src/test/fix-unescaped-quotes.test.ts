import { describe, expect, it } from 'vitest';

import { fixUnescapedQuotes } from '../fix-unescaped-quotes';

describe('fixUnescapedQuotes 修复未转义双引号', () => {
  describe('基础功能测试', () => {
    it('应该保持有效的JSON字符串不变', () => {
      const input = '{"name": "张三", "age": 25}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(input);
    });

    it('应该处理空字符串', () => {
      const result = fixUnescapedQuotes('');

      expect(result).toBe('');
    });

    it('应该处理没有引号的字符串', () => {
      const input = 'hello world';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(input);
    });

    it('应该保持已转义的双引号不变', () => {
      const input = '{"message": "他说\\"你好\\"世界"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(input);
    });
  });

  describe('未转义双引号修复测试', () => {
    it('应该修复字符串中的单个未转义双引号', () => {
      const input = '{"message": "他说"你好""}';
      const expected = '{"message": "他说\\"你好\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该修复字符串中的多个未转义双引号', () => {
      const input = '{"title": "AI说"你好"世界"真棒""}';
      const expected = '{"title": "AI说\\"你好\\"世界\\"真棒\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该修复多个字段中的未转义双引号', () => {
      const input = '{"title": "这是"标题"", "content": "这是"内容""}';
      const expected = '{"title": "这是\\"标题\\"", "content": "这是\\"内容\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理字符串末尾的未转义双引号', () => {
      const input = '{"message": "结尾有引号""}';
      const expected = '{"message": "结尾有引号\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理字符串开头的未转义双引号', () => {
      const input = '{"message": ""开头有引号"}';
      const expected = '{"message": "\\"开头有引号"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });
  });

  describe('JSON结构识别测试', () => {
    it('应该正确识别对象结束符', () => {
      const input = '{"message": "他说"你好"", "status": "ok"}';
      const expected = '{"message": "他说\\"你好\\"", "status": "ok"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该正确识别数组结束符', () => {
      const input = '["这是"第一个"", "这是"第二个""]';
      const expected = '["这是\\"第一个\\"", "这是\\"第二个\\""]';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该正确识别键值分隔符', () => {
      const input = '{"key": "这是"值""}';
      const expected = '{"key": "这是\\"值\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该正确识别数组元素分隔符', () => {
      const input = '{"items": ["项目"一"", "项目"二""]}';
      const expected = '{"items": ["项目\\"一\\"", "项目\\"二\\""]}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });
  });

  describe('空白字符处理测试', () => {
    it('应该处理引号后的空格', () => {
      const input = '{"message": "他说"你好" "}';
      const expected = '{"message": "他说\\"你好\\" "}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理引号后的制表符', () => {
      const input = '{"message": "他说"你好"\t"}';
      const expected = '{"message": "他说\\"你好\\"\t"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理引号后的换行符', () => {
      const input = '{"message": "他说"你好"\n"}';
      const expected = '{"message": "他说\\"你好\\"\n"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理引号后的回车符', () => {
      const input = '{"message": "他说"你好"\r"}';
      const expected = '{"message": "他说\\"你好\\"\r"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理引号后的多种空白字符组合', () => {
      const input = '{"message": "他说"你好" \t\n\r"}';
      const expected = '{"message": "他说\\"你好\\" \t\n\r"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });
  });

  describe('复杂JSON结构测试', () => {
    it('应该处理嵌套对象中的未转义双引号', () => {
      const input = '{"user": {"name": "张三", "comment": "他说"很好"啊"}, "score": 95}';
      const expected = '{"user": {"name": "张三", "comment": "他说\\"很好\\"啊"}, "score": 95}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理数组中的未转义双引号', () => {
      const input = '{"items": ["这是"第一个"项目", "这是"第二个"项目"]}';
      const expected = '{"items": ["这是\\"第一个\\"项目", "这是\\"第二个\\"项目"]}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理混合数据类型的JSON', () => {
      const input = '{"title": "标题"测试"", "count": 42, "active": true, "items": ["项目"一""]}';
      const expected = '{"title": "标题\\"测试\\"", "count": 42, "active": true, "items": ["项目\\"一\\""]}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理深层嵌套结构', () => {
      const input = '{"level1": {"level2": {"message": "深层"嵌套"测试"}}}';
      const expected = '{"level1": {"level2": {"message": "深层\\"嵌套\\"测试"}}}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });
  });

  describe('转义字符处理测试', () => {
    it('应该保持已转义的反斜杠不变', () => {
      const input = '{"path": "C:\\\\Users\\\\<USER>\\\\反斜杠"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(input);
    });

    it('应该处理转义字符和未转义双引号的混合情况', () => {
      const input = '{"message": "文件位于 C:\\\\Users\\\\<USER>\\\\Users\\\\name，他说\\"很好\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理其他转义字符', () => {
      const input = '{"text": "换行\\n制表\\t回车\\r，还有"引号""}';
      const expected = '{"text": "换行\\n制表\\t回车\\r，还有\\"引号\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理连续的转义字符', () => {
      const input = '{"message": "这里有\\\\\\\\双反斜杠和"引号""}';
      const expected = '{"message": "这里有\\\\\\\\双反斜杠和\\"引号\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });
  });

  describe('边界情况测试', () => {
    it('应该处理只有一个双引号的字符串', () => {
      const input = '"';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe('"');
    });

    it('应该处理字符串末尾的情况', () => {
      const input = '{"message": "测试"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(input);
    });

    it('应该处理不完整的JSON字符串', () => {
      const input = '{"message": "他说"你好"';
      const expected = '{"message": "他说\\"你好"';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理空的字符串值', () => {
      const input = '{"empty": "", "message": "包含"引号""}';
      const expected = '{"empty": "", "message": "包含\\"引号\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理连续的双引号', () => {
      const input = '{"message": "连续""双引号""测试"}';
      const expected = '{"message": "连续\\"\\"双引号\\"\\"测试"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该处理字符串中只有双引号的情况', () => {
      const input = '{"quotes": """"}';
      const expected = '{"quotes": "\\"\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });
  });

  describe('实际使用场景测试', () => {
    it('应该修复AI生成的包含引用文本的JSON', () => {
      const input = '{"response": "用户问："如何使用这个功能？"，我回答："很简单""}';
      const expected = '{"response": "用户问：\\"如何使用这个功能？\\"，我回答：\\"很简单\\""}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该修复包含对话内容的JSON', () => {
      const input = '{"conversation": [{"user": "你好"世界""}, {"bot": "你好"用户""}]}';
      const expected = '{"conversation": [{"user": "你好\\"世界\\""}, {"bot": "你好\\"用户\\""}]}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该修复包含HTML标签属性的JSON', () => {
      const input = '{"html": "<div class="container">内容包含"引号"</div>"}';
      const expected = '{"html": "<div class=\\"container\\">内容包含\\"引号\\"</div>"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });

    it('应该修复包含代码片段的JSON', () => {
      const input = '{"code": "console.log("Hello World");"}';
      const expected = '{"code": "console.log(\\"Hello World\\");"}';
      const result = fixUnescapedQuotes(input);

      expect(result).toBe(expected);
    });
  });
});
