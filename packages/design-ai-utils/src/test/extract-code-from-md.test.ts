import { describe, expect, it } from 'vitest';

import { extractCodeFromMd } from '../extract-code-from-md';

describe('从 Markdown 提取代码块 (extractCodeFromMd)', () => {
  it('应从 markdown 中提取指定标签的代码块', () => {
    const mdContent = '一些文本\n```javascript\nconsole.log("Hello");\n```\n更多文本';
    const codeTag = 'javascript';
    const expected = ['console.log("Hello");'];
    expect(extractCodeFromMd(mdContent, codeTag)).toEqual(expected);
  });

  it('当存在多个相同标签的代码块时，应提取所有匹配的代码块', () => {
    const mdContent = '```html\n<p>Block 1</p>\n```\n文本\n```html\n<div>Block 2</div>\n```';
    const codeTag = 'html';
    const expected = ['<p>Block 1</p>', '<div>Block 2</div>'];
    expect(extractCodeFromMd(mdContent, codeTag)).toEqual(expected);
  });

  it('当代码块内容为空时，返回空数组', () => {
    const mdContent = '```python\n```';
    const codeTag = 'python';
    expect(extractCodeFromMd(mdContent, codeTag)).toEqual([]);
  });

  it('当 markdown 中没有匹配的代码块时，应返回空数组', () => {
    const mdContent = '没有代码块的普通文本。';
    const codeTag = 'javascript';
    expect(extractCodeFromMd(mdContent, codeTag)).toEqual([]);
  });

  it('当 markdown 中有其他语言的代码块时，应返回空数组', () => {
    const mdContent = '```python\nprint("Hello")\n```';
    const codeTag = 'javascript';
    expect(extractCodeFromMd(mdContent, codeTag)).toEqual([]);
  });

  it('当 codeTag 包含需要转义的 regex 特殊字符时，应正确提取', () => {
    const mdContent = '```c++\nint main() { return 0; }\n```';
    const codeTag = 'c++'; // '+' 是特殊字符
    const expected = ['int main() { return 0; }'];
    expect(extractCodeFromMd(mdContent, codeTag)).toEqual(expected);

    const mdContent2 = '```js?\nconsole.log(1);\n```';
    const codeTag2 = 'js?'; // '?' 是特殊字符
    const expected2 = ['console.log(1);'];
    expect(extractCodeFromMd(mdContent2, codeTag2)).toEqual(expected2);
  });

  it('markdown内容不合法直接返回空数组', () => {
    expect(extractCodeFromMd({} as any, 'js')).toEqual([]);
    expect(extractCodeFromMd(null as any, 'js')).toEqual([]);
    expect(extractCodeFromMd('文本```jsconsole.log(1);```文本', 1 as any)).toEqual([]);
  });
});
