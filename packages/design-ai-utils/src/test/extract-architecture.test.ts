import { describe, expect, it } from 'vitest';

import { extractArchitecture } from '../extract-architecture';

const pageArchirectrureString =
  '{"an":"简影","pages":[{"id":"001","n":"首页","sections":[{"id":"001001","n":"顶部搜索","d":"极简搜索条，灰色背景，无边框，只有放大镜图标和\'搜索\'提示文字"},{"id":"001002","n":"今日推荐","d":"单个全宽电影卡片，只显示高质量海报和片名，点击即可播放"},{"id":"001003","n":"影片列表","d":"双列瀑布流布局，纯海报展示，无额外文字说明，海报本身传达影片信息"},{"id":"001004","n":"底部导航","d":"仅两个选项：\'影片\'和\'我的\'，只用图标表示，无文字，当前页面图标填充显示"}]}]}';
const pageArchirectrure = {
  an: '简影',
  pages: [
    {
      id: '001',
      n: '首页',
      sections: [
        {
          id: '001001',
          n: '顶部搜索',
          d: "极简搜索条，灰色背景，无边框，只有放大镜图标和'搜索'提示文字",
        },
        {
          id: '001002',
          n: '今日推荐',
          d: '单个全宽电影卡片，只显示高质量海报和片名，点击即可播放',
        },
        {
          id: '001003',
          n: '影片列表',
          d: '双列瀑布流布局，纯海报展示，无额外文字说明，海报本身传达影片信息',
        },
        {
          id: '001004',
          n: '底部导航',
          d: "仅两个选项：'影片'和'我的'，只用图标表示，无文字，当前页面图标填充显示",
        },
      ],
    },
  ],
};
describe('extractArchitecture 提取架构信息', () => {
  describe('基础功能测试', () => {
    it('应该处理不包含架构信息的普通字符串', () => {
      const input = '一串字符串';
      const result = extractArchitecture(input);

      expect(result.text).toBe('一串字符串');
      expect(result.architecture).toEqual({});
    });

    it('应该从包含architecture标签的字符串中提取架构信息', () => {
      const input = `一串字符串<architecture>${pageArchirectrureString}`;
      const result = extractArchitecture(input);

      expect(result.text).toBe('一串字符串');
      expect(result.architecture).toEqual(pageArchirectrure);
    });

    it('应该从包含完整architecture标签的字符串中提取架构信息', () => {
      const input = `一串字符串<architecture>${pageArchirectrureString}</architecture>`;
      const result = extractArchitecture(input);

      expect(result.text).toBe('一串字符串');
      expect(result.architecture).toEqual(pageArchirectrure);
    });
  });

  describe('边界情况测试', () => {
    it('应该处理空字符串', () => {
      const result = extractArchitecture('');

      expect(result.text).toBe('');
      expect(result.architecture).toEqual({});
    });

    it('应该处理只有空白字符的字符串', () => {
      const result = extractArchitecture('   \n\t  ');

      expect(result.text).toBe('   \n\t  ');
      expect(result.architecture).toEqual({});
    });

    it('应该处理没有左括号的字符串', () => {
      const input = '这里没有任何标签或括号';
      const result = extractArchitecture(input);

      expect(result.text).toBe(input);
      expect(result.architecture).toEqual({});
    });

    it('应该处理包含无效JSON的字符串', () => {
      const input = '前缀文本<architecture>{invalid json}';
      const result = extractArchitecture(input);

      expect(result.text).toBe('前缀文本');
      expect(result.architecture).toEqual({});
    });

    it('应该处理不完整的JSON', () => {
      const input = '前缀文本<architecture>{"an":"简影","pages":[{"id":"001"';
      const result = extractArchitecture(input);

      expect(result.text).toBe('前缀文本');
      expect(result.architecture).toEqual({
        an: '简影',
        pages: [{ id: '001' }],
      });
    });
  });

  describe('复杂情况测试', () => {
    it('应该处理包含多个标签的字符串', () => {
      const input = '前缀<tag1>内容1</tag1><architecture>{"test": "value"}<other>其他内容';
      const result = extractArchitecture(input);

      expect(result.text).toBe('前缀');
      expect(result.architecture).toEqual({ test: 'value' });
    });

    it('应该处理JSON中包含特殊字符的情况', () => {
      const input = '文本<architecture>{"message": "包含\\"引号\\"和\\n换行符"}';
      const result = extractArchitecture(input);

      expect(result.text).toBe('文本');
      expect(result.architecture).toEqual({
        message: '包含"引号"和\n换行符',
      });
    });

    it('应该处理复杂嵌套结构', () => {
      const input =
        '描述文本<architecture>{"app":{"name":"测试应用","config":{"theme":"dark","features":["search","recommend"]}}}';
      const result = extractArchitecture(input);

      expect(result.text).toBe('描述文本');
      expect(result.architecture).toEqual({
        app: {
          name: '测试应用',
          config: {
            theme: 'dark',
            features: ['search', 'recommend'],
          },
        },
      });
    });
  });
});
