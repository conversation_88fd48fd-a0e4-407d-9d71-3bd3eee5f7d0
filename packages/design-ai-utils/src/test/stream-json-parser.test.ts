import { beforeEach, describe, expect, it } from 'vitest';

import { StreamJSONParser } from '../stream-json-parser';

describe('StreamJSONParser 流式JSON解析器', () => {
  let parser: StreamJSONParser;

  beforeEach(() => {
    parser = new StreamJSONParser();
  });

  describe('基础功能测试', () => {
    it('应该正确解析完整的JSON对象', () => {
      const result = parser.feed('{"name": "测试", "value": 123}');

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual({ name: '测试', value: 123 });
    });

    it('应该正确解析完整的JSON数组', () => {
      const result = parser.feed('[{"id": 1}, {"id": 2}]');

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual([{ id: 1 }, { id: 2 }]);
    });

    it('应该处理空字符串输入', () => {
      const result = parser.feed('');

      expect(result?.success).toBe(false);
      expect(result?.isComplete).toBe(false);
      expect(result?.result).toBeUndefined();
    });

    it('应该处理只有空白字符的输入', () => {
      const result = parser.feed('   \n\t  ');

      expect(result?.success).toBe(false);
      expect(result?.isComplete).toBe(false);
      expect(result?.result).toBeUndefined();
    });
  });

  describe('流式解析功能测试', () => {
    it('应该处理分块传输的JSON数据', () => {
      const chunk1 = '{"users": [{"name": "';
      const chunk2 = '张三"}, {"name": "李四"}]}';

      const result1 = parser.feed(chunk1);
      expect(result1?.success).toBe(true);
      expect(result1?.isComplete).toBe(false);

      const result2 = parser.feed(chunk2);
      expect(result2?.success).toBe(true);
      expect(result2?.isComplete).toBe(true);
      expect(result2?.result).toEqual({
        users: [{ name: '张三' }, { name: '李四' }],
      });
    });

    it('应该处理多次分块输入', () => {
      const chunks = ['{"data":', ' {"items":', ' [1, 2,', ' 3]}}'];

      let result;
      for (const chunk of chunks) {
        result = parser.feed(chunk);
      }

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual({
        data: { items: [1, 2, 3] },
      });
    });

    it('应该处理不完整的JSON对象', () => {
      const result = parser.feed('{"name": "测试", "value":');

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(false);
      expect(result?.result).toEqual({ name: '测试' });
    });

    it('应该处理不完整的JSON数组', () => {
      const result = parser.feed('[{"id": 1}, {"id":');

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(false);
      expect(result?.result).toEqual([{ id: 1 }, {}]);
    });
  });

  describe('前缀处理测试', () => {
    it('应该忽略JSON前的无效字符', () => {
      const result = parser.feed('一些无效文本{"name": "测试"}');

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual({ name: '测试' });
    });

    it('应该处理没有有效JSON括号的输入', () => {
      const result = parser.feed('只是一些普通文本');

      expect(result).toBeUndefined();
    });

    it('应该处理混合内容中的JSON', () => {
      const result = parser.feed('前缀文本[1, 2, 3]后缀文本');

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual([1, 2, 3]);
    });
  });

  describe('错误处理测试', () => {
    it('应该在解析完成后拒绝新的输入', () => {
      parser.feed('{"complete": true}');
      const result = parser.feed('{"new": "data"}');

      expect(result?.success).toBe(false);
      expect(result?.error).toBeDefined();
      expect(result?.error.message).toContain('JSON 已解析完毕');
    });
  });

  describe('重置功能测试', () => {
    it('应该正确重置解析器状态', () => {
      parser.feed('{"test": "data"}');
      expect(parser.isComplete).toBe(true);

      parser.reset();
      expect(parser.buffer).toBe('');
      expect(parser.isComplete).toBe(false);

      const result = parser.feed('{"new": "data"}');
      expect(result?.success).toBe(true);
      expect(result?.result).toEqual({ new: 'data' });
    });

    it('应该在重置后能够处理新的流式数据', () => {
      parser.feed('{"first": "complete"}');
      parser.reset();

      const chunk1 = '{"second":';
      const chunk2 = ' "streaming"}';

      parser.feed(chunk1);
      const result = parser.feed(chunk2);

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual({ second: 'streaming' });
    });
  });

  describe('复杂JSON结构测试', () => {
    it('应该处理嵌套对象', () => {
      const json = {
        user: {
          profile: {
            name: '张三',
            settings: {
              theme: 'dark',
              notifications: true,
            },
          },
        },
      };

      const result = parser.feed(JSON.stringify(json));

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual(json);
    });

    it('应该处理包含数组的复杂结构', () => {
      const json = {
        data: [
          { id: 1, tags: ['前端', '开发'] },
          { id: 2, tags: ['后端', 'API'] },
        ],
      };

      const result = parser.feed(JSON.stringify(json));

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual(json);
    });

    it('应该处理包含特殊字符的字符串', () => {
      const json = {
        message: '这是一个包含"引号"和\n换行符的字符串',
        path: 'C:\\Users\\<USER>\n\t');
      const result = parser.feed('{"data": "test"}');

      expect(result?.success).toBe(true);
      expect(result?.isComplete).toBe(true);
      expect(result?.result).toEqual({ data: 'test' });
    });
  });
});
