import { beforeEach, describe, expect, it, vi } from 'vitest';

import { Emitter } from '../emitter';

describe('Emitter 事件发射器', () => {
  let emitter: Emitter<any>;

  beforeEach(() => {
    emitter = new Emitter();
  });

  describe('基础功能测试', () => {
    it('应该能够发射和监听事件', () => {
      const mockListener = vi.fn();

      emitter.on(mockListener);
      emitter.emit('测试数据');

      expect(mockListener).toHaveBeenCalledWith('测试数据');
      expect(mockListener).toHaveBeenCalledTimes(1);
    });

    it('应该能够发射多个事件', () => {
      const mockListener = vi.fn();

      emitter.on(mockListener);
      emitter.emit('第一个事件');
      emitter.emit('第二个事件');
      emitter.emit('第三个事件');

      expect(mockListener).toHaveBeenCalledTimes(3);
      expect(mockListener).toHaveBeenNthCalledWith(1, '第一个事件');
      expect(mockListener).toHaveBeenNthCalledWith(2, '第二个事件');
      expect(mockListener).toHaveBeenNthCalledWith(3, '第三个事件');
    });

    it('应该能够取消事件监听', () => {
      const mockListener = vi.fn();

      emitter.on(mockListener);
      emitter.emit('第一个事件');

      emitter.off();
      emitter.emit('第二个事件');

      expect(mockListener).toHaveBeenCalledTimes(1);
      expect(mockListener).toHaveBeenCalledWith('第一个事件');
    });
  });

  describe('数据类型测试', () => {
    it('应该能够处理字符串类型数据', () => {
      const emitter = new Emitter<string>();
      const mockListener = vi.fn();

      emitter.on(mockListener);
      emitter.emit('字符串数据');

      expect(mockListener).toHaveBeenCalledWith('字符串数据');
    });

    it('应该能够处理数字类型数据', () => {
      const emitter = new Emitter<number>();
      const mockListener = vi.fn();

      emitter.on(mockListener);
      emitter.emit(42);

      expect(mockListener).toHaveBeenCalledWith(42);
    });

    it('应该能够处理对象类型数据', () => {
      const emitter = new Emitter<{ name: string; age: number }>();
      const mockListener = vi.fn();
      const testData = { name: '张三', age: 25 };

      emitter.on(mockListener);
      emitter.emit(testData);

      expect(mockListener).toHaveBeenCalledWith(testData);
    });

    it('应该能够处理数组类型数据', () => {
      const emitter = new Emitter<string[]>();
      const mockListener = vi.fn();
      const testData = ['项目1', '项目2', '项目3'];

      emitter.on(mockListener);
      emitter.emit(testData);

      expect(mockListener).toHaveBeenCalledWith(testData);
    });

    it('应该能够处理null和undefined', () => {
      const emitter = new Emitter<null | undefined>();
      const mockListener = vi.fn();

      emitter.on(mockListener);
      emitter.emit(null);
      emitter.emit(undefined);

      expect(mockListener).toHaveBeenCalledTimes(2);
      expect(mockListener).toHaveBeenNthCalledWith(1, null);
      expect(mockListener).toHaveBeenNthCalledWith(2, undefined);
    });
  });

  describe('订阅管理测试', () => {
    it('应该返回订阅对象', () => {
      const mockListener = vi.fn();
      const subscription = emitter.on(mockListener);

      expect(subscription).toBeDefined();
      expect(typeof subscription.unsubscribe).toBe('function');
    });

    it('应该只创建一个订阅', () => {
      const mockListener1 = vi.fn();
      const mockListener2 = vi.fn();

      const subscription1 = emitter.on(mockListener1);
      const subscription2 = emitter.on(mockListener2);

      expect(subscription1).toBe(subscription2);
    });

    it('应该在多次调用on时只有第一个监听器生效', () => {
      const mockListener1 = vi.fn();
      const mockListener2 = vi.fn();

      emitter.on(mockListener1);
      emitter.on(mockListener2);
      emitter.emit('测试数据');

      // 由于实现只为第一个listener创建订阅，只有第一个会被调用
      expect(mockListener1).toHaveBeenCalledWith('测试数据');
      expect(mockListener2).not.toHaveBeenCalled();
    });

    it('应该能够重新订阅', () => {
      const mockListener1 = vi.fn();
      const mockListener2 = vi.fn();

      emitter.on(mockListener1);
      emitter.emit('第一次数据');

      emitter.off();
      emitter.on(mockListener2);
      emitter.emit('第二次数据');

      expect(mockListener1).toHaveBeenCalledWith('第一次数据');
      expect(mockListener2).toHaveBeenCalledWith('第二次数据');
    });
  });

  describe('边界情况测试', () => {
    it('应该能够在没有监听器的情况下发射事件', () => {
      expect(() => {
        emitter.emit('无监听器的事件');
      }).not.toThrow();
    });

    it('应该能够多次调用off方法', () => {
      const mockListener = vi.fn();

      emitter.on(mockListener);
      emitter.off();

      expect(() => {
        emitter.off();
        emitter.off();
      }).not.toThrow();
    });

    it('应该能够在off后继续发射事件', () => {
      const mockListener = vi.fn();

      emitter.on(mockListener);
      emitter.off();

      expect(() => {
        emitter.emit('off后的事件');
      }).not.toThrow();

      expect(mockListener).not.toHaveBeenCalled();
    });

    it('应该能够处理复杂的数据结构', () => {
      interface ComplexData {
        id: string;
        user: {
          name: string;
          profile: {
            age: number;
            hobbies: string[];
          };
        };
        metadata: Record<string, any>;
      }

      const complexEmitter = new Emitter<ComplexData>();
      const mockListener = vi.fn();
      const complexData: ComplexData = {
        id: 'test-001',
        user: {
          name: '李四',
          profile: {
            age: 30,
            hobbies: ['阅读', '编程', '旅行'],
          },
        },
        metadata: {
          timestamp: Date.now(),
          source: 'test',
          tags: ['重要', '紧急'],
        },
      };

      complexEmitter.on(mockListener);
      complexEmitter.emit(complexData);

      expect(mockListener).toHaveBeenCalledWith(complexData);
    });
  });

  describe('实际使用场景测试', () => {
    it('应该能够用于数据更新通知', () => {
      interface DataUpdate {
        type: 'create' | 'update' | 'delete';
        id: string;
        data?: any;
      }

      const dataEmitter = new Emitter<DataUpdate>();
      const mockHandler = vi.fn();

      dataEmitter.on(mockHandler);

      dataEmitter.emit({ type: 'create', id: '001', data: { name: '新项目' } });
      dataEmitter.emit({ type: 'update', id: '001', data: { name: '更新项目' } });
      dataEmitter.emit({ type: 'delete', id: '001' });

      expect(mockHandler).toHaveBeenCalledTimes(3);
      expect(mockHandler).toHaveBeenNthCalledWith(1, { type: 'create', id: '001', data: { name: '新项目' } });
      expect(mockHandler).toHaveBeenNthCalledWith(2, { type: 'update', id: '001', data: { name: '更新项目' } });
      expect(mockHandler).toHaveBeenNthCalledWith(3, { type: 'delete', id: '001' });
    });

    it('应该能够用于状态变化通知', () => {
      type AppState = 'loading' | 'ready' | 'error';

      const stateEmitter = new Emitter<AppState>();
      const mockStateHandler = vi.fn();

      stateEmitter.on(mockStateHandler);

      stateEmitter.emit('loading');
      stateEmitter.emit('ready');
      stateEmitter.emit('error');

      expect(mockStateHandler).toHaveBeenCalledTimes(3);
      expect(mockStateHandler).toHaveBeenNthCalledWith(1, 'loading');
      expect(mockStateHandler).toHaveBeenNthCalledWith(2, 'ready');
      expect(mockStateHandler).toHaveBeenNthCalledWith(3, 'error');
    });

    it('应该能够用于错误处理', () => {
      interface ErrorEvent {
        code: string;
        message: string;
        timestamp: number;
        stack?: string;
      }

      const errorEmitter = new Emitter<ErrorEvent>();
      const mockErrorHandler = vi.fn();

      errorEmitter.on(mockErrorHandler);

      const errorEvent: ErrorEvent = {
        code: 'NETWORK_ERROR',
        message: '网络连接失败',
        timestamp: Date.now(),
        stack: 'Error stack trace...',
      };

      errorEmitter.emit(errorEvent);

      expect(mockErrorHandler).toHaveBeenCalledWith(errorEvent);
    });
  });
});
