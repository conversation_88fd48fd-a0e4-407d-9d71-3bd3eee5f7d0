import { describe, expect, it } from 'vitest';

import { parsePartialJSON } from '../parse-partial-json';

describe('parsePartialJSON 解析器', () => {
  describe('基础功能测试', () => {
    it('应该正确解析有效的JSON', () => {
      const input = '{"name": "测试", "value": 123}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('successful-parse');
      expect(result.value).toEqual({ name: '测试', value: 123 });
    });

    it('应该处理空输入', () => {
      const result = parsePartialJSON('');

      expect(result.state).toBe('empty-input');
      expect(result.value).toBe(null);
    });

    it('应该处理只有空白字符的输入', () => {
      const result = parsePartialJSON('   \n\t  ');

      expect(result.state).toBe('empty-input');
      expect(result.value).toBe(null);
    });
  });

  describe('未转义双引号修复功能', () => {
    it('应该修复字符串中的未转义双引号', () => {
      const input = '{"description": "这是一个"测试"案例", "status": "active"}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        description: '这是一个"测试"案例',
        status: 'active',
      });
    });

    it('应该修复复杂的未转义双引号情况', () => {
      const input = '{"title": "AI说"你好"世界", "content": "这里有"多个"未转义的双引号"}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        title: 'AI说"你好"世界',
        content: '这里有"多个"未转义的双引号',
      });
    });

    it('应该处理嵌套对象中的未转义双引号', () => {
      const input = '{"user": {"name": "张三", "comment": "他说"很好"啊"}, "score": 95}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        user: {
          name: '张三',
          comment: '他说"很好"啊',
        },
        score: 95,
      });
    });

    it('应该处理数组中的未转义双引号', () => {
      const input = '{"items": ["这是"第一个"项目", "这是"第二个"项目"]}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        items: ['这是"第一个"项目', '这是"第二个"项目'],
      });
    });

    it('应该处理字符串中有空白字符后的双引号', () => {
      const input = '{"message": "他说"好的"}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        message: '他说"好的',
      });
    });
  });

  describe('流式输出场景测试', () => {
    it('应该处理不完整的JSON对象', () => {
      const input = '{"name": "测试", "description": "这是一个包含"引号"的描述", "value":';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      // 修复后的字符串现在能正确解析完整的description字段
      expect(result.value).toEqual({
        name: '测试',
        description: '这是一个包含"引号"的描述',
      });
    });

    it('应该处理不完整的数组', () => {
      const input = '{"items": ["item1", "这是"带引号"的项目"';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      // 修复后的字符串现在能正确解析完整的字符串
      expect(result.value).toEqual({
        items: ['item1', '这是"带引号"的项目'],
      });
    });

    it('应该处理只有键没有值的情况', () => {
      const input = '{"name": "测试", "description":';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        name: '测试',
      });
    });

    it('应该处理不完整的字符串（没有结束引号）', () => {
      const input = '{"name": "测试中';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        name: '测试中',
      });
    });

    it('应该处理解析值时出错的情况', () => {
      const input = '{"name": "测试", "invalid": xyz}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        name: '测试',
      });
    });

    it('应该处理对象解析时出错的情况', () => {
      const input = '{"name": "测试", invalid syntax';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        name: '测试',
      });
    });
  });

  describe('数字解析测试', () => {
    it('应该处理负数', () => {
      const input = '{"value": -123}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('successful-parse');
      expect(result.value).toEqual({ value: -123 });
    });

    it('应该处理不完整的负数', () => {
      const input = '{"value": -';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({});
    });

    it('应该处理科学计数法', () => {
      const input = '{"value": 1.23e5}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('successful-parse');
      expect(result.value).toEqual({ value: 123000 });
    });

    it('应该处理不完整的科学计数法', () => {
      const input = '{"value": 1.23e';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({ value: 1.23 });
    });

    it('应该处理单独的负号', () => {
      const input = '-';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('failed-parse');
      expect(result.value).toBe(null);
    });

    it('应该处理数字解析错误', () => {
      const input = '{"value": 123abc}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({});
    });
  });

  describe('边界情况测试', () => {
    it('应该正确处理已转义的双引号', () => {
      const input = '{"message": "他说\\"你好\\"世界"}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('successful-parse');
      expect(result.value).toEqual({
        message: '他说"你好"世界',
      });
    });

    it('应该处理混合转义和未转义双引号', () => {
      const input = '{"message": "他说\\"你好\\"世界，还有"其他"内容"}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        message: '他说"你好"世界，还有"其他"内容',
      });
    });

    it('应该处理数字和布尔值', () => {
      const input = '{"count": 42, "active": true, "comment": "这是"测试""}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        count: 42,
        active: true,
        comment: '这是"测试"',
      });
    });

    it('应该处理完全无效的JSON', () => {
      const input = 'this is not json at all';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('failed-parse');
      expect(result.value).toBe(null);
    });

    it('应该处理转义字符', () => {
      const input = '{"path": "C:\\\\Program Files\\\\test", "newline": "line1\\nline2"}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('successful-parse');
      expect(result.value).toEqual({
        path: 'C:\\Program Files\\test',
        newline: 'line1\nline2',
      });
    });

    it('应该处理复杂的转义字符和未转义双引号混合', () => {
      const input = '{"message": "文件位于 C:\\\\Users\\\\<USER>\\Users\\name，他说"很好"',
      });
    });

    it('应该处理空数组', () => {
      const input = '{"list": []}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('successful-parse');
      expect(result.value).toEqual({
        list: [],
      });
    });

    it('应该处理空对象', () => {
      const input = '{"nested": {}}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('successful-parse');
      expect(result.value).toEqual({
        nested: {},
      });
    });

    it('应该处理null值', () => {
      const input = '{"value": null}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('successful-parse');
      expect(result.value).toEqual({
        value: null,
      });
    });

    it('应该处理字符串末尾的双引号（边界情况）', () => {
      const input = '{"message": "测试"';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        message: '测试',
      });
    });

    it('应该处理字符串解析失败需要补全结束引号的情况', () => {
      const input = '{"message": "测试';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        message: '测试',
      });
    });

    it('应该处理数组解析时发生异常的情况', () => {
      const input = '{"list": [1, 2, invalid]}';
      const result = parsePartialJSON(input);

      expect(result.state).toBe('repaired-parse');
      expect(result.value).toEqual({
        list: [1, 2],
      });
    });
  });
});
