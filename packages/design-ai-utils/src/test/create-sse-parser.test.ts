import { describe, expect, it } from 'vitest';

import { createSSEParser, EventSourceMessage } from '../create-sse-parser';

describe('SSE 解析器 (createSSEParser)', () => {
  const createMockReadableStream = (chunks: string[]) => {
    return new ReadableStream<string>({
      start(controller) {
        chunks.forEach(chunk => controller.enqueue(chunk));
        controller.close();
      }
    });
  };

  const collectResults = async (stream: ReadableStream<EventSourceMessage>): Promise<EventSourceMessage[]> => {
    const results: EventSourceMessage[] = [];
    const reader = stream.getReader();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        results.push(value);
      }
    } finally {
      reader.releaseLock();
    }
    
    return results;
  };

  describe('基础字段解析', () => {
    it('应解析单个 data 字段', async () => {
      // arrange
      const input = createMockReadableStream(['data: hello world\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        data: 'hello world',
        event: '',
        id: '',
        retry: undefined
      });
    });

    it('应解析所有字段', async () => {
      // arrange
      const input = createMockReadableStream([
        'event: test\n',
        'data: hello\n',
        'id: 123\n',
        'retry: 5000\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        data: 'hello',
        event: 'test',
        id: '123',
        retry: 5000
      });
    });

    it('应解析没有值的字段', async () => {
      // arrange
      const input = createMockReadableStream(['data\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        data: '',
        event: '',
        id: '',
        retry: undefined
      });
    });

    it('应处理冒号后跟空格的情况', async () => {
      // arrange
      const input = createMockReadableStream(['data: hello world\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results[0].data).toBe('hello world');
    });

    it('应处理冒号后不跟空格的情况', async () => {
      // arrange
      const input = createMockReadableStream(['data:hello world\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results[0].data).toBe('hello world');
    });
  });

  describe('多个 data 字段', () => {
    it('应用换行符连接多个 data 字段', async () => {
      // arrange
      const input = createMockReadableStream([
        'data: first line\n',
        'data: second line\n',
        'data: third line\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0].data).toBe('first line\nsecond line\nthird line');
    });

    it('应处理空的 data 字段', async () => {
      // arrange
      const input = createMockReadableStream([
        'data: YHOO\n',
        'data: +2\n',
        'data\n',
        'data: 10\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0].data).toBe('YHOO\n+2\n\n10');
    });
  });

  describe('注释处理', () => {
    it('应跳过注释行', async () => {
      // arrange
      const input = createMockReadableStream([
        ': this is a comment\n',
        'data: actual data\n',
        ': another comment\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        data: 'actual data',
        event: '',
        id: '',
        retry: undefined
      });
    });

    it('应处理带内容的注释', async () => {
      // arrange
      const input = createMockReadableStream([
        ':comment content here\n',
        'data: test\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0].data).toBe('test');
    });
  });

  describe('retry 字段处理', () => {
    it('应解析有效的 retry 值', async () => {
      // arrange
      const input = createMockReadableStream(['retry: 3000\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results[0].retry).toBe(3000);
    });

    it('应忽略无效的 retry 值', async () => {
      // arrange
      const input = createMockReadableStream(['retry: invalid\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results[0].retry).toBeUndefined();
    });

    it('应处理负数 retry 值', async () => {
      // arrange
      const input = createMockReadableStream(['retry: -1000\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results[0].retry).toBe(-1000); // 负数也是有效整数
    });
  });

  describe('多个消息', () => {
    it('应解析由空行分隔的多个消息', async () => {
      // arrange
      const input = createMockReadableStream([
        'data: first message\n\n',
        'data: second message\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(2);
      expect(results[0].data).toBe('first message');
      expect(results[1].data).toBe('second message');
    });

    it('应处理包含不同字段的消息', async () => {
      // arrange
      const input = createMockReadableStream([
        'event: update\n',
        'data: {"count": 1}\n\n',
        'event: delete\n',
        'data: {"id": 123}\n',
        'id: abc\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(2);
      expect(results[0]).toEqual({
        data: '{"count": 1}',
        event: 'update',
        id: '',
        retry: undefined
      });
      expect(results[1]).toEqual({
        data: '{"id": 123}',
        event: 'delete',
        id: 'abc',
        retry: undefined
      });
    });
  });

  describe('分块数据处理', () => {
    it('应处理跨多个块分割的数据', async () => {
      // arrange
      const input = createMockReadableStream([
        'data: hel',
        'lo wor',
        'ld\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0].data).toBe('hello world');
    });

    it('应处理跨块分割的消息边界', async () => {
      // arrange
      const input = createMockReadableStream([
        'data: first\n',
        '\ndata: sec',
        'ond\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(2);
      expect(results[0].data).toBe('first');
      expect(results[1].data).toBe('second');
    });

    it('应处理跨块分割的字段', async () => {
      // arrange
      const input = createMockReadableStream([
        'ev',
        'ent: test\n',
        'da',
        'ta: hello\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        data: 'hello',
        event: 'test',
        id: '',
        retry: undefined
      });
    });
  });

  describe('边界情况', () => {
    it('应忽略未知字段', async () => {
      // arrange
      const input = createMockReadableStream([
        'data: test\n',
        'unknown: field\n',
        'custom: value\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        data: 'test',
        event: '',
        id: '',
        retry: undefined
      });
    });

    it('应处理包含多个冒号的字段', async () => {
      // arrange
      const input = createMockReadableStream(['data: key:value:another\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results[0].data).toBe('key:value:another');
    });

    it('应处理空输入', async () => {
      // arrange
      const input = createMockReadableStream(['']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(0);
    });

    it('应处理只有换行符的输入', async () => {
      // arrange
      const input = createMockReadableStream(['\n\n\n\n']);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(2);
      results.forEach(result => {
        expect(result).toEqual({
          data: '',
          event: '',
          id: '',
          retry: undefined
        });
      });
    });

    it('应在多次指定字段时覆盖字段值', async () => {
      // arrange
      const input = createMockReadableStream([
        'event: first\n',
        'event: second\n',
        'id: initial\n',
        'id: final\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        data: '',
        event: 'second',
        id: 'final',
        retry: undefined
      });
    });
  });

  describe('真实场景', () => {
    it('应处理典型的聊天流式响应', async () => {
      // arrange
      const input = createMockReadableStream([
        'event: message\n',
        'data: {"type":"start","content":""}\n\n',
        'event: message\n',
        'data: {"type":"chunk","content":"Hello"}\n\n',
        'event: message\n',
        'data: {"type":"chunk","content":" world"}\n\n',
        'event: message\n',
        'data: {"type":"end","content":""}\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(4);
      expect(results[0].event).toBe('message');
      expect(results[0].data).toBe('{"type":"start","content":""}');
      expect(results[1].data).toBe('{"type":"chunk","content":"Hello"}');
      expect(results[2].data).toBe('{"type":"chunk","content":" world"}');
      expect(results[3].data).toBe('{"type":"end","content":""}');
    });

    it('应处理服务器心跳和空事件', async () => {
      // arrange
      const input = createMockReadableStream([
        'data: actual data\n\n',
        ': heartbeat\n\n',
        'data: more data\n\n'
      ]);
      const parser = createSSEParser();
      
      // act
      const results = await collectResults(input.pipeThrough(parser));
      
      // assert
      expect(results).toHaveLength(3);
      expect(results[0].data).toBe('actual data');
      expect(results[1]).toEqual({
        data: '',
        event: '',
        id: '',
        retry: undefined
      });
      expect(results[2].data).toBe('more data');
    });
  });
}); 