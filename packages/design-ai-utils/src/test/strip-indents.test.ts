import { describe, it, expect } from 'vitest';
import { stripIndents } from '../strip-indents';

describe('stripIndents - 移除字符串缩进', () => {
  describe('字符串用法', () => {
    it('应该移除简单缩进字符串的缩进', () => {
      const input = '  <div>\n    <h1>Hello</h1>\n  </div>';
      const expected = '<div>\n<h1>Hello</h1>\n</div>';
      expect(stripIndents(input)).toBe(expected);
    });

    it('应该处理没有缩进的字符串', () => {
      const input = '<div>\n<h1>Hello</h1>\n</div>';
      const expected = '<div>\n<h1>Hello</h1>\n</div>';
      expect(stripIndents(input)).toBe(expected);
    });

    it('应该处理空字符串', () => {
      expect(stripIndents('')).toBe('');
    });

    it('应该处理单行字符串', () => {
      const input = '  Hello World  ';
      const expected = 'Hello World';
      expect(stripIndents(input)).toBe(expected);
    });

    it('应该处理混合缩进的字符串', () => {
      const input = '    <div>\n      <h1>Title</h1>\n        <p>Content</p>\n    </div>';
      const expected = '<div>\n<h1>Title</h1>\n<p>Content</p>\n</div>';
      expect(stripIndents(input)).toBe(expected);
    });

    it('应该处理包含制表符的字符串', () => {
      const input = '\t\t<div>\n\t\t\t<h1>Hello</h1>\n\t\t</div>';
      const expected = '<div>\n<h1>Hello</h1>\n</div>';
      expect(stripIndents(input)).toBe(expected);
    });

    it('应该处理带有尾随换行符的字符串', () => {
      const input = '  <div>\n    <h1>Hello</h1>\n  </div>\n';
      const expected = '<div>\n<h1>Hello</h1>\n</div>';
      expect(stripIndents(input)).toBe(expected);
    });
  });

  describe('模板字符串用法', () => {
    it('应该移除模板字符串的缩进', () => {
      const result = stripIndents`
        <div>
          <h1>Hello</h1>
        </div>`;
      const expected = '<div>\n<h1>Hello</h1>\n</div>';
      expect(result).toBe(expected);
    });

    it('应该处理带有插值的模板字符串', () => {
      const title = 'World';
      const content = 'Hello there!';
      const result = stripIndents`
        <div>
          <h1>${title}</h1>
          <p>${content}</p>
        </div>`;
      const expected = '<div>\n<h1>World</h1>\n<p>Hello there!</p>\n</div>';
      expect(result).toBe(expected);
    });

    it('应该处理带有数字插值的模板字符串', () => {
      const count = 42;
      const result = stripIndents`
        <div>
          <span>Count: ${count}</span>
        </div>`;
      const expected = '<div>\n<span>Count: 42</span>\n</div>';
      expect(result).toBe(expected);
    });

    it('应该处理空模板字符串', () => {
      const result = stripIndents``;
      expect(result).toBe('');
    });

    it('应该处理只包含空白的模板字符串', () => {
      const result = stripIndents`
        
        `;
      expect(result).toBe('');
    });

    it('应该处理复杂HTML结构的模板字符串', () => {
      const className = 'container';
      const id = 'main';
      const result = stripIndents`
        <div class="${className}" id="${id}">
          <header>
            <h1>Title</h1>
            <nav>
              <ul>
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
              </ul>
            </nav>
          </header>
          <main>
            <p>Content goes here</p>
          </main>
        </div>`;

      const expected =
        '<div class="container" id="main">\n<header>\n<h1>Title</h1>\n<nav>\n<ul>\n<li><a href="#home">Home</a></li>\n<li><a href="#about">About</a></li>\n</ul>\n</nav>\n</header>\n<main>\n<p>Content goes here</p>\n</main>\n</div>';
      expect(result).toBe(expected);
    });
  });
});
