# @tencent/design-ai-utils

腾讯设计AI功能公共工具库，为AI设计相关应用提供底层的数据处理和解析功能。

## 📦 安装

```bash
npm install @tencent/design-ai-utils
pnpm add @tencent/design-ai-utils
yarn add @tencent/design-ai-utils
```

## parsePartialJSON

解析可能不完整或损坏的JSON字符串，支持自动修复。

```typescript
import { parsePartialJSON } from '@tencent/design-ai-utils';

const result = parsePartialJSON('{"name": "张三", "age": 25');
console.log(result);
// { state: 'repaired-parse', value: { name: '张三', age: 25 } }
```

## StreamJSONParser

处理流式传输的JSON数据解析。

```typescript
import { StreamJSONParser } from '@tencent/design-ai-utils';

const parser = new StreamJSONParser();
const result = parser.feed('{"users": [{"name": "张三"}]}');
console.log(result.success); // true
```

## extractCodeFromMd

从Markdown文档中提取指定语言的代码块。

````typescript
import { extractCodeFromMd } from '@tencent/design-ai-utils';

const markdown = '```javascript\nconsole.log("hello");\n```';
const codes = extractCodeFromMd(markdown, 'javascript');
console.log(codes); // ['console.log("hello");']
````

## createSSEParser

创建Server-Sent Events流解析器。

```typescript
import { createSSEParser } from '@tencent/design-ai-utils';

const parser = createSSEParser();
// 用于处理SSE数据流
stream.pipeThrough(parser);
```

## streamXmlParser

解析流式XML数据。

```typescript
import { streamXmlParser } from '@tencent/design-ai-utils';

const result = streamXmlParser('<root><item>value</item></root>');
```

## extractArchitecture

提取pageArchitecture信息。

```typescript
import { extractArchitecture } from '@tencent/design-ai-utils';

const architecture = extractArchitecture(htmlContent);
```

## stripIndents

移除字符串的缩进。

```typescript
import { stripIndents } from '@tencent/design-ai-utils';

const text = stripIndents(`
  function hello() {
    console.log('world');
  }
`);
```

## trimNewLines

修剪字符串首尾的换行符。

```typescript
import { trimNewLines } from '@tencent/design-ai-utils';

const cleaned = trimNewLines('\n\nhello world\n\n');
console.log(cleaned); // 'hello world'
```

## fixUnescapedQuotes

修复未转义的引号。

```typescript
import { fixUnescapedQuotes } from '@tencent/design-ai-utils';

const fixed = fixUnescapedQuotes('He said "Hello" to me');
```

## Emitter

事件发射器，提供事件监听和发布功能。

```typescript
import { Emitter } from '@tencent/design-ai-utils';

const emitter = new Emitter();
emitter.on('event', (data) => console.log(data));
emitter.emit('event', 'hello');
```

## 代码规范

- 使用 TypeScript 编写代码
- 遵循现有的代码风格
- 为新功能添加测试用例
- 更新相关文档

## 🔗 相关资源

- [Zod 文档](https://zod.dev/) - Schema 验证库
- [RxJS 文档](https://rxjs.dev/) - 响应式编程库
- [Server-Sent Events MDN](https://developer.mozilla.org/zh-CN/docs/Web/API/Server-sent_events) - SSE 规范
- [JSON 规范](https://www.json.org/json-zh.html) - JSON 格式说明
