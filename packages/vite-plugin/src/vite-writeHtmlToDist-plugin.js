// vite 将内存文件写入磁盘插件
import fs from 'fs/promises';
import path from 'path';

export const writeHtmlToDistPlugin = () => ({
  name: 'vite-plugin-write-to-disk',
  apply: 'serve', // 仅在开发服务器模式下应用
  configureServer(server) {
    // 在服务器启动后执行
    server.httpServer?.once('listening', async () => {
      try {
        // 获取服务器信息
        const serverUrl = server.config.server.origin || 
          `http://${server.config.server.host || 'localhost'}:${server.config.server.port}`;
        console.log(`🌐 服务器URL: base href=${serverUrl}`);
        
        // 获取index.html的内容
        const indexHtml = await fetch(serverUrl).then(res => res.text());
        
        // 构造 HTML 模板
        const template = indexHtml.replace('<head>', `<head><base href="${serverUrl}/">`);
        
        // 确定输出目录和文件路径
        const outDir = server.config.build.outDir || 'dist';
        const filePath = path.join(process.cwd(), outDir, 'index.html');
        
        // 确保目录存在
        await fs.mkdir(path.dirname(filePath), { recursive: true });
        
        // 写入文件
        await fs.writeFile(filePath, template);
        console.log(`📝 已将 HTML 写入: ${filePath}`);
      } catch (error) {
        const err = error;
        console.error(`❌ 生成或写入文件失败: ${err.message}`);
      }
    });
  },
});