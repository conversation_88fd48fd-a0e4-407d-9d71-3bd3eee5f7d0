import type { Options } from 'tsup';

const getConfig = (options?: any) => {
  const isProd = options?.env?.NODE_ENV === 'production';

  return {
    entry: ['src/index.ts'],
    format: ['esm', 'cjs'],
    dts: {
      resolve: true,
      compilerOptions: {
        composite: false,
        declaration: true,
        skipLibCheck: true,
      },
      banner: '/// <reference types="@figma/plugin-typings" />',
    },
    splitting: false,
    sourcemap: false,
    clean: isProd ? true : false,
    treeshake: true,
    minify: isProd ? true : false,
    outDir: 'dist',
    watch: !isProd,
  } as Options;
};

export default getConfig;
