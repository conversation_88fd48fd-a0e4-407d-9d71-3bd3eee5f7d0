{"name": "tsup-config", "version": "1.0.0", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "description": "本项目并非一个独立的包，而是一个公用的 tsup 配置文件，设置 package.json 是为了本地开发识别相关的依赖包", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "scripts": {"dev": "tsup --env.NODE_ENV development", "build": "tsup"}, "files": ["dist", "README.md"], "keywords": [], "author": "", "license": "ISC", "devDependencies": {"tsup": "^8.4.0"}}