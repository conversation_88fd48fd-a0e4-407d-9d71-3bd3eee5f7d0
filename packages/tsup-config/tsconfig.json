{
  "compilerOptions": {
    "target": "ES2021",
    "module": "ES6",
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "useDefineForClassFields": true,
    "moduleResolution": "Node",
    "strict": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "skipLibCheck": true,
    "declaration": true,
    "emitDeclarationOnly": true,
    "noEmit": false,
    "typeRoots": [
      "./node_modules/@types",
      "./node_modules/@figma",
    ],
    "outDir": "./dist",
    "baseUrl": ".",
  }
}