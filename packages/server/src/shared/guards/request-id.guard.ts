import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { HEADER_X_REQUEST_ID } from '@shared/constants/system.constants';
import { genRequestId } from '@utils/request';

@Injectable()
export class RequestIdGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // 关于 request-id，链路中各个环节的处理模式：
    // clb：添加 stgw-request-id
    // nginxingress: 透传 x-request-id，如果没有请求头部中没有则新建
    // pluginproxy：主要参考 nginxingress 的处理方式，透传 x-request-id，但也兼容 clb，优先读取 x-request-id，没有的话读取 stgw-request-id，否则创建随机值
    // 因此，plugin 优先读取 x-request-id，再尝试 stgw-request-id，最后用随机值
    // 没有 x-request-id 的场景：境外用户访问，这时请求直接从 香港-clb 进入 plugin 服务，中间没有 nginxingress 或 pluginproxy，所以只有 stgw-request-id
    const requestId = genRequestId(request);

    request.requestId = requestId;
    response.setHeader(HEADER_X_REQUEST_ID, requestId);

    return true;
  }
}
