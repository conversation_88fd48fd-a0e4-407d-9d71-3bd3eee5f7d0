import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { isString } from 'lodash';

/** 自定义查询的 order条件 */
export interface IOrderParam {
  defaultOrder?: [[key: string, orderType: 'asc' | 'desc']];
}

/**
 * 返回结构
 * pageParams页码参数；where条件；order条件；
 */
export interface IPager {
  pageSize: number;
  pageNum: number;
  order: [[key: string, orderType: 'asc' | 'desc']];
}

/**
 * 分页列表参数解析装饰器 必须是 get请求
 * 前端传参示例{pageNum:2,pageSize:10,orderBy:'createTime',order:'asc'}
 */
export const PagerParams = createParamDecorator((orderParam: IOrderParam, ctx: ExecutionContext): IPager => {
  const request = ctx.switchToHttp().getRequest();
  const { query } = request;

  const pageSize = Number(query.pageSize || 10);
  const pageNum = Number(query.pageNum || 1);

  let order = orderParam.defaultOrder;

  // 处理前端传来的order条件
  if (isString(query.orderBy)) {
    const verifyOrderValue = ['desc', 'asc'].includes(query.order);
    if (verifyOrderValue) {
      order = [[query.orderBy, query.order]];
    } else {
      order = [[query.orderBy, 'desc']];
    }
  }
  return { pageSize, pageNum, order };
});
