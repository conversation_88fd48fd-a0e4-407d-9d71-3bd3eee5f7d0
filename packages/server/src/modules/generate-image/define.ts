export interface GenerateImagePayload {
  prompt: string;
  width?: number;
  height?: number;
}
export interface GenerateImageResult {
  success: boolean;
  signUrl?: string;
  fileKey?: string;
  error?: string;
}

export const COS_IMAGE_FILEKEY = 'generate-image';

export const DEFAULT_IMAGE_SIZE = {
  width: 1024,
  height: 1024,
};

export type ImageModel = 'hunyuan' | 'flux';
export const IMAGE_MODELS = ['hunyuan', 'flux'] as const;
