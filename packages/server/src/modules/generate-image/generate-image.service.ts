import { AIConfig } from '@config/config.interface';
import {
  ConcurrentTaskExecutorService,
  TaskResult,
} from '@modules/concurrent-task-executor/concurrent-task-executor.service';
import { COS_COMMON_FILEKEY_PREFIX } from '@modules/cos/constant';
import { CosService } from '@modules/cos/cos.service';
import { TRACK_ACTION, TrackService } from '@modules/track/track.service';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { convertBase64ToBuffer, convertUrlToBuffer, getFormattedDateComponents } from '@utils/index';
import { nanoid } from 'nanoid';
import { firstValueFrom } from 'rxjs';

import { COS_IMAGE_FILEKEY, DEFAULT_IMAGE_SIZE, GenerateImagePayload, GenerateImageResult, ImageModel } from './define';
import { normalizeHunyuanImageSize } from './utils';
// import { createApi } from 'unsplash-js';
// import type { Random } from 'unsplash-js/dist/methods/photos/types';

@Injectable()
export class GenerateImageService {
  // private unsplashApi: ReturnType<typeof createApi>;
  public readonly hunyuanConcurrent: number = 2;

  private readonly logger = new Logger('GenerateImageService');

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly cosService: CosService,
    private readonly taskExecutor: ConcurrentTaskExecutorService,
    private readonly track: TrackService,
  ) {
    // const { ACCESS_KEY } = this.configService.get<UnsplashConfig>('unsplash');
    // this.unsplashApi = createApi({
    //   accessKey: ACCESS_KEY,
    // });
  }

  /**
   * Search a image by keyword
   * @param keyword
   * @returns
   *   * full: jpg 图片，宽度为最大尺寸
   *   * regular: jpg 图片，宽度为 1080
   *   * small: jpg 图片，宽度为 400
   *   * thumb: jpg 图片，宽度为 200
   *   * raw: 带有 ixid 参数的图片路径
   */
  // public async searchImageByUnsplash(keyword: string) {
  //   const res = await this.unsplashApi.photos.getRandom({ query: keyword });

  //   if (res.type === 'error') {
  //     const errorMessage = res.errors.join(',');
  //     this.logger.warn(`searchImageByUnsplash 失败: ${errorMessage}`, '', { keyword });
  //     throw new Error(errorMessage);
  //   }

  //   return (res.response as Random).urls;
  // }

  public async generateImages(model: ImageModel, payloads: GenerateImagePayload[]): Promise<GenerateImageResult[]> {
    let results: TaskResult<GenerateImageResult>[] = [];
    const startTime = Date.now();
    if (model === 'flux') {
      results = await this.taskExecutor.executeTasks(payloads, (arg) => this.generateImageByFlux(arg));
    } else if (model === 'hunyuan') {
      const concurrentLimit = Number(this.configService.get<AIConfig>('ai').HUNYUAN_CONCURRENT_LIMIT) ?? 2;
      results = await this.taskExecutor.executeTasks(
        payloads,
        (arg) => this.generateImageByHunyuan(arg),
        concurrentLimit,
        {
          keyPrefix: 'ai-gen-design-hunyuan-image-gen',
          taskTimeout: 15 * 1000,
        },
      );
      this.logger.log(`混元生图(${payloads.length}张图)总耗时：s`);
    } else {
      throw new Error('不支持的模型');
    }

    const duration = (Date.now() - startTime) / 1000; // 秒
    this.track.send(TRACK_ACTION.GENERATE_IMAGE, {
      eventValue: model,
      extraInfo: {
        duration,
      },
    });

    return results.map((o) => ({
      success: o.result?.success ?? o.success,
      fileKey: o.result?.fileKey,
      signUrl: o.result?.signUrl,
      error: o.error,
    }));
  }

  /**
   * 最好使用英文提示词
   */
  private async generateImageByFlux(payload: GenerateImagePayload): Promise<GenerateImageResult> {
    const { GEN_IMAGE_URL } = this.configService.get<AIConfig>('ai');
    const requestBody = {
      // 图片尺寸最小16，否则会报错
      prompt: payload.prompt,
      width: Math.max(payload.width ?? DEFAULT_IMAGE_SIZE.width, 16),
      height: Math.max(payload.height ?? DEFAULT_IMAGE_SIZE.height, 16),
    };

    try {
      const res = await firstValueFrom(
        this.httpService.request({
          url: GEN_IMAGE_URL,
          method: 'post',
          headers: { 'Content-Type': 'application/json' },
          data: requestBody,
        }),
      );
      const responseData = res?.data ?? {};
      if (responseData.nsfw) {
        throw new Error('图片违规');
      }
      if (responseData.image?.length === 0) {
        throw new Error('图片为空');
      }
      const fileKey = this.combineCosFileKey();
      const cosRes = await this.cosService.putObject({
        Key: fileKey,
        Body: convertBase64ToBuffer(responseData.image),
      });
      if (cosRes.statusCode !== 200) {
        throw new Error('上传图片到cos失败');
      }
      return {
        success: true,
        fileKey: fileKey,
        signUrl: this.cosService.generateCdnSignUrl(fileKey),
      };
    } catch (error) {
      this.logger.error(`generateImageByInternal失败：${error.message}`, '', JSON.stringify({ requestBody }));
      return {
        success: false,
        error: error.message,
      };
    }
  }

  private async generateImageByHunyuan(payload: GenerateImagePayload): Promise<GenerateImageResult> {
    const { HUNYUAN_API_KEY } = this.configService.get<AIConfig>('ai');
    const size = {
      width: normalizeHunyuanImageSize(payload.width ?? DEFAULT_IMAGE_SIZE.width),
      height: normalizeHunyuanImageSize(payload.height ?? DEFAULT_IMAGE_SIZE.height),
    };
    // https://iwiki.woa.com/p/4013905692
    const requestBody: Record<string, any> = {
      prompt: payload.prompt,
      revise: false,
      model: 'hunyuan-image',
      size: `${size.width}x${size.height}`,
    };
    try {
      const startTime = Date.now();
      const res = await firstValueFrom(
        this.httpService.request({
          url: 'http://hunyuanapi.woa.com/openapi/v1/images/generations',
          method: 'post',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${HUNYUAN_API_KEY}`,
          },
          data: requestBody,
        }),
      );
      const { error, data } = res.data ?? {};
      if (error) {
        throw new Error(error.message);
      }
      if (!data?.[0]?.url) {
        throw new Error('图片为空');
      }
      this.logger.log(`混元生图成功，耗时：${(Date.now() - startTime) / 1000}s`, JSON.stringify(res.data));
      const fileKey = this.combineCosFileKey();
      const cosRes = await this.cosService.putObject({
        Key: fileKey,
        Body: await convertUrlToBuffer(data[0].url),
      });
      if (cosRes.statusCode !== 200) {
        throw new Error('上传图片到cos失败');
      }
      return {
        success: true,
        fileKey: fileKey,
        signUrl: this.cosService.generateCdnSignUrl(fileKey),
      };
    } catch (error) {
      this.logger.error(`混元生图失败：${error.message}`, '', JSON.stringify({ requestBody }));
      return {
        success: false,
        error: error.message,
      };
    }
  }

  private combineCosFileKey() {
    const { year, month, day } = getFormattedDateComponents(new Date());
    return `${COS_COMMON_FILEKEY_PREFIX}/${COS_IMAGE_FILEKEY}/${year}/${month}/${day}/${nanoid(21)}.jpg`;
  }
}
