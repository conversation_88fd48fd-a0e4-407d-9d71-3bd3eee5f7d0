import { ConcurrentTaskExecutorModule } from '@modules/concurrent-task-executor/concurrent-task-executor.module';
import { CosModule } from '@modules/cos/cos.module';
import { TrackModule } from '@modules/track/track.module';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { GenerateImageController } from './generate-image.controller';
import { GenerateImageService } from './generate-image.service';

@Module({
  imports: [ConcurrentTaskExecutorModule, HttpModule, CosModule, TrackModule],
  providers: [GenerateImageService],
  exports: [GenerateImageService],
  controllers: [GenerateImageController],
})
export class GenerateImageModule {}
