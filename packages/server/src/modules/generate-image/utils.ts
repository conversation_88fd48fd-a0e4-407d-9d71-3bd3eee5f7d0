/**
 * 针对hunyuan的格式要求处理图片尺寸 https://iwiki.woa.com/p/4013905692
 * 规范化图片尺寸，确保尺寸在有效范围内且为64的倍数
 * @param size 输入的尺寸
 * @returns 处理后的尺寸，保证在768-1280范围内且为64的倍数
 */
export function normalizeHunyuanImageSize(size: number): number {
  const MIN_SIZE = 768;
  const MAX_SIZE = 1280;
  const MULTIPLE = 64;
  const SPECIAL_MIN = 720; // 特殊处理范围的最小值

  // 如果尺寸小于特殊最小值720，设置为最小有效值768
  if (size < SPECIAL_MIN) {
    return MIN_SIZE;
  }

  // 如果尺寸大于最大值1280，设置为最大值
  if (size > MAX_SIZE) {
    return MAX_SIZE;
  }

  // 计算向上取整到64的倍数
  const remainder = size % MULTIPLE;
  if (remainder === 0) {
    // 已经是64的倍数，但要确保至少是768
    return Math.max(size, MIN_SIZE);
  } else {
    // 向上取最接近的64的倍数
    const nextMultiple = size + (MULTIPLE - remainder);
    // 确保在有效范围内
    return Math.min(Math.max(nextMultiple, MIN_SIZE), MAX_SIZE);
  }
}
