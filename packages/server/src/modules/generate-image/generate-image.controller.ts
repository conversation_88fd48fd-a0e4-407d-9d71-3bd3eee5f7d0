import { Body, Controller, Post } from '@nestjs/common';

import { GenerateImageDto } from './dto/generate-image.dto';
import { GenerateImageService } from './generate-image.service';

export type AssetType = 'avatar' | 'logo';

@Controller('generate-image')
export class GenerateImageController {
  constructor(private readonly generateImageService: GenerateImageService) {}

  @Post()
  public generateImage(@Body() body: GenerateImageDto) {
    return this.generateImageService.generateImages(body.model ?? 'flux', body.querys);
  }
}
