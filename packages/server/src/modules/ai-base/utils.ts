import { InvalidToolArgumentsError, NoSuchToolError, ToolCallRepairError, ToolExecutionError } from 'ai';

/**
 * 判断是否是ai-sdk的tool call相关错误
 * https://ai-sdk.dev/docs/ai-sdk-core/tools-and-tool-calling#handling-errors
 * @param error - 错误对象
 * @returns {boolean}
 */
export const isToolError = (error: Error) => {
  return (
    error instanceof NoSuchToolError ||
    error instanceof InvalidToolArgumentsError ||
    error instanceof ToolExecutionError ||
    error instanceof ToolCallRepairError
  );
};
