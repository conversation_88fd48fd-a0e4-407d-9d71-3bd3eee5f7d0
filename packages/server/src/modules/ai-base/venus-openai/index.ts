import { VenusAIModelId } from '../define';
import { OpenAIChatLanguageModel } from './openai-chat-language-model';

export function createVenusOpenAILanguageModel(modelId: VenusAIModelId, apiKey: string) {
  const baseURL = 'http://v2.open.venus.oa.com/llmproxy';
  const providerName = 'venus';

  const getHeaders = () => ({
    Authorization: `Bearer ${apiKey}`,
  });

  return new OpenAIChatLanguageModel(
    modelId,
    {},
    {
      provider: `${providerName}.chat`,
      url: ({ path }) => `${baseURL}${path}`,
      headers: getHeaders,
      compatibility: 'compatible',
    },
  );
}
