import { LanguageModelV1FinishReason } from '@ai-sdk/provider';

export function mapOpenAIFinishReason(finishReason: string | null | undefined): LanguageModelV1FinishReason {
  switch (finishReason) {
    // Anthropic
    case 'end_turn':
    // Anthropic
    case 'stop_sequence':
    case 'stop':
      return 'stop';
    case 'max_tokens':
    // Anthropic
    case 'length':
      return 'length';
    case 'content_filter':
      return 'content-filter';
    case 'function_call':
    case 'tool_calls':
    // Anthropic
    case 'tool_use':
      return 'tool-calls';
    default:
      return 'unknown';
  }
}
