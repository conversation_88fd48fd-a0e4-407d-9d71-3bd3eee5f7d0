export interface OpenAIChatSettings {
  /**
Modify the likelihood of specified tokens appearing in the completion.

Accepts a JSON object that maps tokens (specified by their token ID in
the GPT tokenizer) to an associated bias value from -100 to 100. You
can use this tokenizer tool to convert text to token IDs. Mathematically,
the bias is added to the logits generated by the model prior to sampling.
The exact effect will vary per model, but values between -1 and 1 should
decrease or increase likelihood of selection; values like -100 or 100
should result in a ban or exclusive selection of the relevant token.

As an example, you can pass {"50256": -100} to prevent the <|endoftext|>
token from being generated.
*/
  logitBias?: Record<number, number>;

  /**
Return the log probabilities of the tokens. Including logprobs will increase
the response size and can slow down response times. However, it can
be useful to better understand how the model is behaving.

Setting to true will return the log probabilities of the tokens that
were generated.

Setting to a number will return the log probabilities of the top n
tokens that were generated.
*/
  logprobs?: boolean | number;

  /**
Whether to enable parallel function calling during tool use. Default to true.
   */
  parallelToolCalls?: boolean;

  /**
Whether to use structured outputs. Defaults to false.

When enabled, tool calls and object generation will be strict and follow the provided schema.
 */
  structuredOutputs?: boolean;

  /**
Whether to use legacy function calling. Defaults to false.

Required by some open source inference engines which do not support the `tools` API. May also
provide a workaround for `parallelToolCalls` resulting in the provider buffering tool calls,
which causes `streamObject` to be non-streaming.

Prefer setting `parallelToolCalls: false` over this option.

@deprecated this API is supported but deprecated by OpenAI.
   */
  useLegacyFunctionCalling?: boolean;

  /**
A unique identifier representing your end-user, which can help OpenAI to
monitor and detect abuse. Learn more.
*/
  user?: string;

  /**
Automatically download images and pass the image as data to the model.
OpenAI supports image URLs for public models, so this is only needed for
private models or when the images are not publicly accessible.

Defaults to `false`.
   */
  downloadImages?: boolean;

  /**
Simulates streaming by using a normal generate call and returning it as a stream.
Enable this if the model that you are using does not support streaming.

Defaults to `false`.

@deprecated Use `simulateStreamingMiddleware` instead.
   */
  simulateStreaming?: boolean;

  /**
Reasoning effort for reasoning models. Defaults to `medium`.
   */
  reasoningEffort?: 'low' | 'medium' | 'high';
}
