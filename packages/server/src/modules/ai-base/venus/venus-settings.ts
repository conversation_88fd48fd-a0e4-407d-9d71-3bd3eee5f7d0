import crypto from 'crypto';

/**
 * 维纳斯平台设置
 */
export class VenusSettings {
  constructor(
    private url: string,
    private appGroupId: number,
    private secretId: string,
    private secretKey: string,
  ) {}

  public get Url() {
    return this.url;
  }

  public get AppGroupId() {
    return this.appGroupId;
  }

  public getCustomHeader(body: any) {
    const req = new URL(this.url);
    const orderQueryStr = this.orderQuery(req.search);
    req.search = orderQueryStr;
    const timeStamp = Math.round(new Date().getTime() / 1000);
    const venusSign = this.geneVenusSign(req, body, timeStamp);
    const headers = {
      'SecretId': this.secretId,
      'Venusopenapi-Authorization': venusSign,
    };

    return headers;
  }

  geneVenusSign(req: URL, body, timeStamp) {
    const hmc = crypto.createHmac('sha256', encodeURIComponent(this.secretKey));
    const bodyStr = typeof body === 'object' ? JSON.stringify(body) : '';
    const algorithm = 'HMAC-SHA256';
    const pathSign = crypto.createHash('sha256').update(req.pathname).digest('hex').toLocaleUpperCase();
    const querySign = crypto.createHash('sha256').update(req.search).digest('hex').toLocaleUpperCase();
    const bodySign = crypto.createHash('sha256').update(bodyStr).digest('hex').toLocaleUpperCase();
    const signStr = [algorithm, this.secretId, timeStamp, pathSign, querySign, bodySign].join('\n');
    const hmcRet = hmc.update(signStr).digest('hex').toLocaleUpperCase();
    return `${algorithm} Credential=${this.secretId}/${timeStamp} Signature=${hmcRet}`;
  }

  orderQuery(query = '') {
    const params = {};
    query.split('&').forEach((param) => {
      if (!param) {
        return;
      }
      const [key, val] = param.split('=');
      params[key] = val;
    });
    const sortedList = Object.entries(params).sort((a, b) => a[0].localeCompare(b[0]));
    return sortedList.map(([key, val]) => `${key}=${val}`).join('&');
  }
}
