import {
  LanguageModelV1,
  LanguageModelV1CallOptions,
  LanguageModelV1FinishReason,
  LanguageModelV1Prompt,
  LanguageModelV1StreamPart,
  LanguageModelV1TextPart,
} from '@ai-sdk/provider';
import { createJsonResponseHandler, postJsonToApi } from '@ai-sdk/provider-utils';

import { AIModelId } from '../define';
import {
  venusAIChatResponseSchema,
  venusAIChatSSEChunkSchema,
  venusAIChatSSEResponseSchema,
  venusAIFailedResponseHandler,
} from './ai-schema';
import { eventSourceResponseHandler, ResponseChunk } from './successful-response-handler';
import { VenusSettings } from './venus-settings';

function convertToVenusMessages(messages: LanguageModelV1Prompt, modelId: AIModelId) {
  // 处理多模态消息
  const messagesWithMultiModal = messages.map((it) => {
    if (it.role === 'user') {
      const content =
        typeof it.content === 'string'
          ? it.content
          : it.content.map((item) => {
              if (item.providerMetadata?.metadata?.type === 'image') {
                return JSON.parse((item as LanguageModelV1TextPart).text);
              }

              return item;
            });
      return {
        role: it.role,
        content,
      };
    } else {
      return it;
    }
  });

  // 不同 LLM 的 messages 格式可能不同，这里调整
  switch (modelId) {
    case 'hunyuan-vision':
      return messagesWithMultiModal.map((it) => {
        // TODO 这里指考虑了text类型的提示词，可能需要扩展
        if (it.role === 'user' && Array.isArray(it.content) && it.content[0].type === 'text') {
          return {
            ...it,
            content: it.content[0].text,
          };
        } else if (it.role === 'assistant' && Array.isArray(it.content) && it.content[0].type === 'text') {
          return {
            ...it,
            content: it.content[0].text,
          };
        }
        return it;
      });

    case 'claude-3-5-sonnet-20241022':
    case 'claude-3-7-sonnet-20250219':
      return messagesWithMultiModal.map((it) => {
        if (Array.isArray(it.content) && it.content.length === 1 && it.content[0].type === 'text') {
          return {
            ...it,
            content: it.content[0].text,
          };
        }
        return it;
      });
    default:
      return messagesWithMultiModal;
  }
}

/**
 * Venus平台设计语言模型
 */
export class VenusAILanguageModel implements LanguageModelV1 {
  // 规格版本
  readonly specificationVersion = 'v1';
  // 模型编号
  readonly modelId: string = '';
  // 提供者
  readonly provider = 'tencent-venus';
  // 默认对象生成模式
  readonly defaultObjectGenerationMode = 'json';
  readonly supportsImageUrls = false;
  readonly supportsStructuredOutputs = false;

  // Venus平台设置
  private venusSettings: VenusSettings;

  // AI大模型编号
  private aiModelId: AIModelId;

  constructor(settings: VenusSettings) {
    this.venusSettings = settings;
  }

  public setModelId(modelId: AIModelId) {
    this.aiModelId = modelId;
  }

  async doGenerate(options: LanguageModelV1CallOptions): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {
    const body = this.combineApiBody(options);
    const customerHeaders = this.venusSettings.getCustomHeader(body);

    const { value: response } = await postJsonToApi({
      url: this.venusSettings.Url,
      headers: customerHeaders,
      body: body,
      abortSignal: options.abortSignal,
      failedResponseHandler: venusAIFailedResponseHandler,
      successfulResponseHandler: createJsonResponseHandler(venusAIChatResponseSchema),
    });

    if (response.code === 0) {
      return {
        text: response.data.response || '',
        // https://iwiki.woa.com/p/4008066550   status===3表示失败
        finishReason: response.data.status === 3 ? 'error' : 'stop',
        usage: {
          promptTokens: response.data.usage.prompt_tokens,
          completionTokens: response.data.usage.completion_tokens,
        },
        rawCall: {
          rawPrompt: options.prompt,
          rawSettings: {},
        },
      };
    } else {
      return {
        text: '',
        finishReason: 'error',
        usage: null,
        rawCall: null,
      };
    }
  }

  async doStream(options: LanguageModelV1CallOptions): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {
    const body = this.combineApiBody(options, { stream: true });
    const customerHeaders = this.venusSettings.getCustomHeader(body);

    const { value: response } = await postJsonToApi({
      url: this.venusSettings.Url,
      headers: customerHeaders,
      body: body,
      abortSignal: options.abortSignal,
      failedResponseHandler: venusAIFailedResponseHandler,
      successfulResponseHandler: eventSourceResponseHandler,
    });

    let finishReason: LanguageModelV1FinishReason = 'unknown';

    let usage: { promptTokens: number; completionTokens: number } = {
      promptTokens: Number.NaN,
      completionTokens: Number.NaN,
    };

    return {
      stream: response.pipeThrough(
        new TransformStream<ResponseChunk, LanguageModelV1StreamPart>({
          transform(chunk, controller) {
            if (chunk.success === false) {
              finishReason = 'error';
              controller.enqueue({ type: 'error', error: chunk.error });
              return;
            }

            const value = chunk.value;
            const result = venusAIChatSSEResponseSchema.safeParse(value);
            if (result.success && result.data.response === null && options.headers['inner-start'] === '1') {
              controller.enqueue({
                type: 'text-delta',
                textDelta: '[START]',
              });
            } else if (result.success && result.data.response !== null && options.headers['inner-end'] === '1') {
              usage = {
                promptTokens: result.data.usage.prompt_tokens,
                completionTokens: Number.NaN,
              };
              finishReason = 'stop';
              controller.enqueue({
                type: 'text-delta',
                textDelta: '[END]',
              });
            } else {
              const data = venusAIChatSSEChunkSchema.safeParse(value);
              if (data.success && data.data.data) {
                controller.enqueue({
                  type: 'text-delta',
                  textDelta: data.data.data,
                });
              }
            }
          },

          flush(controller) {
            if (options.headers['inner-end'] === '1') {
              controller.enqueue({
                type: 'finish',
                finishReason,
                usage,
              });
            }
          },
        }),
      ),
      rawCall: null,
    };
  }

  private combineApiBody(options: LanguageModelV1CallOptions, otherBody: Record<string, any> = {}) {
    const messages = convertToVenusMessages(options.prompt, this.aiModelId);
    const body = {
      appGroupId: this.venusSettings.AppGroupId,
      model: this.aiModelId,
      messages,
      temperature: options.temperature,
      max_tokens: options.maxTokens,
      top_p: options.topP,
      ...otherBody,
    };

    return body;
  }
}
