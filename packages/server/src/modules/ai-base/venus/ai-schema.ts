import { APICallError } from '@ai-sdk/provider';
import { createJsonErrorResponseHandler, ResponseHandler } from '@ai-sdk/provider-utils';
import { z } from 'zod';

export const venusAIErrorDataSchema = z.object({
  traceId: z.string(),
  code: z.number(),
  data: z.null(),
  message: z.string(),
});

export const venusAIFailedResponseHandler: ResponseHandler<APICallError> = createJsonErrorResponseHandler({
  errorSchema: venusAIErrorDataSchema,
  errorToMessage: (data) => data.message,
});

export const venusAIChatResponseSchema = z.object({
  traceId: z.string(),
  code: z.number(),
  retCode: z.number(),
  msg: z.string(),
  data: z
    .object({
      id: z.string().optional(),
      username: z.string(),
      sessionId: z.string(),
      alias: z.string(),
      status: z.number(),
      model: z.string(),
      avatarUrl: z.string(),
      request: z.string(),
      response: z.string(),
      createTime: z.string(),
      updateTime: z.string(),
      usage: z.object({
        prompt_tokens: z.number().optional(),
        completion_tokens: z.number().optional(),
        total_tokens: z.number().optional(),
      }),
    })
    .nullable(),
});

export const venusAIChatSSEChunkSchema = z.object({
  spanId: z.string(),
  data: z.union([z.string(), z.null()]),
});

export const venusAIChatSSEResponseSchema = z.object({
  id: z.string().optional(),
  username: z.string(),
  sessionId: z.string(),
  spanId: z.string(),
  alias: z.string(),
  model: z.string(),
  avatarUrl: z.string(),
  request: z.string(),
  response: z.union([z.string(), z.null()]),
  createTime: z.string(),
  updateTime: z.string(),
  status: z.number(),
  usage: z.object({
    prompt_tokens: z.number().optional(),
    completion_tokens: z.number().optional(),
    total_tokens: z.number().optional(),
  }),
});
