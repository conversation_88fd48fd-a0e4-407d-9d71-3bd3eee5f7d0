import { EmptyResponseBodyError } from '@ai-sdk/provider';
import { extractResponseHeaders, ResponseHandler } from '@ai-sdk/provider-utils';
import { EventSourceMessage, EventSourceParserStream } from 'eventsource-parser/stream';

export type ResponseChunk =
  | {
      value: Record<string, any>;
      success: true;
      rawValue: string;
    }
  | {
      error: string;
      success: false;
    };

export const eventSourceResponseHandler: ResponseHandler<ReadableStream<ResponseChunk>> = async ({
  response,
}: {
  response: Response;
}) => {
  const responseHeaders = extractResponseHeaders(response);

  if (response.body == null) {
    throw new EmptyResponseBodyError({});
  }

  return {
    responseHeaders,
    value: response.body
      .pipeThrough(new TextDecoderStream())
      .pipeThrough(new EventSourceParserStream())
      .pipeThrough(
        new TransformStream<EventSourceMessage, ResponseChunk>({
          transform(chunk, controller) {
            const { data, event } = chunk;
            if (event === '[ERROR]') {
              let error = data;
              try {
                error = JSON.parse(data)?.data ?? data;
              } catch {}
              controller.enqueue({ success: false, error });
              return;
            }
            if (data === '[DONE]') {
              return;
            }

            try {
              const parseResult = JSON.parse(data);
              controller.enqueue({ success: true, value: parseResult, rawValue: data });
            } catch (error) {
              controller.enqueue({ success: false, error: data });
            }
          },
        }),
      ),
  };
};
