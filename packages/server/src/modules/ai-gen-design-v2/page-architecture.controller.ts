import { Body, Controller, Get, Headers, Logger, Post, Query, Req, Sse } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { HEADER_X_USER_ID } from '@shared/constants/system.constants';
import { RequestCloseHandler } from '@shared/interceptors/request-close.interceptor';
import { Request } from 'express';

import { AiUpdateArchitectureDto } from './dto/ai-update-architecture.dto';
import { GenerateArchitectureDto } from './dto/generate-architecture.dto';
import { ManualUpdateArchitectureDto } from './dto/manual-update-architecture.dto';
import { RegenerateArchitectureDto } from './dto/regenerate-architecture.dto';
import { PageArchitectureService } from './page-architecture.service';

@Controller('ai-gen-design/v2/architecture')
export class PageArchitectureController implements RequestCloseHandler {
  constructor(
    private readonly service: PageArchitectureService,
    private readonly logger: Logger,
  ) {}

  requestClose(requestId: string) {
    const abortControllers = this.service.abortControllerMap.get(requestId);
    if (abortControllers && abortControllers.length > 0) {
      // this.logger.log(`[ai-gen-design]${requestId}，调用 ${abortControllers.length} 个abortController.abort`);
      abortControllers.forEach((abortController) => abortController.abort());
      this.service.abortControllerMap.delete(requestId);
    }
  }

  @ApiOperation({
    description: '生成architecture的聊天接口',
  })
  @Post('/generate')
  @Sse('')
  async generateArchitecture(
    @Req() req: Request,
    @Body() body: GenerateArchitectureDto,
    @Headers(HEADER_X_USER_ID) userId: string,
  ) {
    const requestId = req['requestId'];

    return await this.service.generatePageArchitecture(requestId, userId, body);
  }

  @ApiOperation({
    description: '重新生成某个message，文案部分和architecture部分都会重新生成',
  })
  @Post('/regenerate')
  @Sse('')
  async regenerateArchitecture(
    @Req() req: Request,
    @Body() body: RegenerateArchitectureDto,
    @Headers(HEADER_X_USER_ID) userId: string,
  ) {
    const requestId = req['requestId'];
    return await this.service.regeneratePageArchitecture(requestId, userId, body);
  }

  @ApiOperation({
    description: 'LLM更新architecture，和聊天无关',
  })
  @Post('/ai-update')
  @Sse('')
  async updateArchitectureByAi(
    @Req() req: Request,
    @Body() body: AiUpdateArchitectureDto,
    @Headers(HEADER_X_USER_ID) userId: string,
  ) {
    const requestId = req['requestId'];
    return await this.service.updatePageArchitectureByAi(requestId, userId, body);
  }

  @ApiOperation({
    description: '手动更新architecture',
  })
  @Post('/manual-update')
  async updatePageArchitectureByManual(@Body() body: ManualUpdateArchitectureDto) {
    return await this.service.updatePageArchitectureByManual(body);
  }

  @ApiOperation({
    description: '检查architecture是否已经生成过代码',
  })
  @Get('/already-generate-check')
  async checkArchitecture(@Query('architectureMessageId') architectureMessageId: string) {
    return await this.service.architectureAlreadyGenerateCheck(architectureMessageId);
  }
}
