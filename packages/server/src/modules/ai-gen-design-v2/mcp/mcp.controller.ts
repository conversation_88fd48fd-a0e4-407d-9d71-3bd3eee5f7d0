import { PrismaService } from '@modules/prisma/prisma.service';
import { Controller, Get, NotFoundException, Param, Res } from '@nestjs/common';
import controllerFactory from '@shared/mcp/controller-factory';
import { Response } from 'express';

import { GET_CODE_URL, MCP_BASE_URL, MCP_SERVER_NAME, MCP_SERVER_VERSION } from './constant';
import { McpToolsService } from './mcp-tools.service';

export const BaseController = controllerFactory({
  path: MCP_BASE_URL,
  mcpServerName: MCP_SERVER_NAME,
  mcpServerVersion: MCP_SERVER_VERSION,
  setupInjectable: McpToolsService,
});

@Controller(MCP_BASE_URL)
export class Mcp<PERSON>ontroller extends BaseController {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  @Get(`${GET_CODE_URL}/:codeId`)
  async getCode(@Res() res: Response, @Param('codeId') codeId: string) {
    const codeRecord = await this.prisma.mcp_gen_code.findUnique({
      where: {
        id: Number(codeId),
      },
    });

    if (!codeRecord) {
      throw new NotFoundException('code not found');
    }

    res.header('Content-Type', 'text/html');
    return res.send(codeRecord.code);
  }
}
