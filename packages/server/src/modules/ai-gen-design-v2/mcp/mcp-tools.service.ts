import { HttpConfig } from '@config/config.interface';
import { Mcp<PERSON><PERSON><PERSON>, ToolCallback } from '@modelcontextprotocol/sdk/server/mcp.js';
import { RequestHandlerExtra } from '@modelcontextprotocol/sdk/shared/protocol.js';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { TrackService } from '@modules/track/track.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { McpSetupInjectable } from '@shared/mcp/setup-injectable.interface';
import {
  extractCodeFromMd,
  PageArchitectureSchema,
  stripIndents,
  zodToJsonSchemaString,
} from '@tencent/design-ai-utils';
import { joinUrl } from '@utils/.';
import { CoreMessage } from 'ai';
import { nanoid } from 'nanoid';
import { z, ZodRawShape } from 'zod';

import { AiGenDesignService } from '../ai-gen-design.service';
import { PageArchitecture, PageIdLength, PageType, TASK } from '../define';
import { GenerateCodeService } from '../generate-code.service';
import { getPrompt } from '../prompt';
import { getPageTypeString, minifyHtml, validateParamsByZod } from '../utils';
import { GET_CODE_URL, MCP_BASE_URL, MCP_SERVER_NAME } from './constant';

const architectureParamsSchema = {
  pageType: z
    .enum(['app', 'web'])
    .optional()
    .describe(
      stripIndents`The type of application or page to be designed.
      "app" represents the mobile terminal, and "web" represents the computer web page terminal.
      the default is "app"`,
    ),
  prompt: z.string().describe('design requirements'),
  designStylePrompt: z.string().optional().describe('design style prompt'),
};
const codeParamsSchema = {
  pageType: z
    .enum(['app', 'web'])
    .optional()
    .describe(
      stripIndents`The type of application or page to be designed.
      "app" represents the mobile terminal, and "web" represents the computer web page terminal.
      the default is "app"`,
    ),
  architectureId: z
    .number()
    .describe(
      'Page architecture ID, it can be the return value of the ui-architecture tool, or the ID of the already generated page architecture',
    ),
  targetPageId: z
    .string()
    .describe('The ID of the page to be generated currently. This tool can only generate one page at a time'),
  previousGeneratedCodes: z
    .array(z.number().describe('The ID of the already generated code'))
    .optional()
    .describe(
      stripIndents`
      The ID of the code that has already been generated within the same UI architecture will be used as a reference for this generation.
      There is no need to pass parameters when generating the first page.
      Subsequent generations can pass parameters based on the results of the previous call`,
    ),
};
const codeParamsSchemaObject = z.object(codeParamsSchema);

@Injectable()
export class McpToolsService implements McpSetupInjectable {
  private baseUrl: string;
  private toolNameMap = {
    architecture: 'ui-architecture',
    generateHtml: 'generate-html',
    generateHtmlReturnUrl: 'generate-html-return-url',
  };

  constructor(
    private readonly aiBaseService: AiBaseService,
    private readonly generateCodeService: GenerateCodeService,
    private readonly aiGenDesignService: AiGenDesignService,
    private readonly logger: Logger,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly track: TrackService,
  ) {
    this.baseUrl = joinUrl([
      this.configService.get('domain'),
      this.configService.get<HttpConfig>('http').API_PREFIX,
      MCP_BASE_URL,
      GET_CODE_URL,
    ]);
  }

  public setup(server: McpServer) {
    if (server) {
      this.registerArchitectureTool(server);
      this.registerGenerateHtmlTool(server);
      // this.logger.log(
      //   `MCP '${MCP_SERVER_NAME}' 注册tool成功，共${Object.keys(this.toolNameMap).length}个tool: ${Object.values(this.toolNameMap).join(', ')}`,
      // );
    }
  }

  private toolHandlerWrapper<Args extends ZodRawShape>(toolName: string, handler: ToolCallback<Args>) {
    return (async (params, extra) => {
      try {
        this.logger.log(
          `MCP '${MCP_SERVER_NAME}' tool called start (${toolName}) sessionId: ${extra.sessionId}`,
          params,
        );
        const result = await handler(params, extra);
        this.logger.log(`MCP '${MCP_SERVER_NAME}' tool called end (${toolName}) sessionId: ${extra.sessionId}`, result);
        return result;
      } catch (error) {
        this.logger.error(
          `MCP '${MCP_SERVER_NAME}' tool called error (${toolName}) sessionId: ${extra.sessionId}`,
          error,
        );
        return {
          content: [{ type: 'text', text: error.message }],
          isError: true,
        };
      }
    }) as ToolCallback<Args>;
  }

  private registerArchitectureTool(server: McpServer) {
    server.tool(
      this.toolNameMap.architecture,
      stripIndents`
      Based on a simple design requirement, generate a complete UI design architecture.
      If the requirement is to generate an application or website, this tool will design multiple core pages.
      It is not necessary to explicitly specify which pages to design, as the tool can make considerations.
      If the requirement is to design a single page, then only one page will be designed.
      `,
      architectureParamsSchema,
      this.toolHandlerWrapper<typeof architectureParamsSchema>(this.toolNameMap.architecture, async (params, extra) => {
        validateParamsByZod(architectureParamsSchema, params);
        const { pageType = PageType.app, prompt, designStylePrompt } = params;
        const systemPrompt = getPrompt('architecture', {
          page_type: getPageTypeString(pageType as PageType),
        });
        const userPrompt = `${prompt}${designStylePrompt ? `\n设计风格要求: ${designStylePrompt}` : ''}`;
        const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-7');
        const abortController = new AbortController();
        extra.signal.onabort = () => {
          abortController.abort();
        };
        const userId = 'mcp';
        this.track.trackCallAiStart({ userId, task: TASK.gen_page_architecture, modelId, platform });
        const res = await this.aiBaseService.chatTextV2(
          {
            platform,
            modelId,
            userId,
            abort: abortController,
          },
          { system: systemPrompt, messages: [{ role: 'user', content: userPrompt }] },
        );
        this.track.trackCallAiStop({ userId, task: TASK.gen_page_architecture, modelId, platform, usage: res.usage });
        if (res.finishReason === 'error') {
          throw new Error(res.text);
        }

        const archText = res.text.includes('```json') ? extractCodeFromMd(res.text, 'json')[0] : res.text;

        const archObj = JSON.parse(archText) as PageArchitecture;
        archObj.pages.forEach((page) => {
          page.id = nanoid(PageIdLength);
          page.sections.forEach((sec) => {
            sec.id = `${page.id}-${sec.id}`;
          });
        });

        const record = await this.prisma.mcp_ui_architecture.create({
          data: {
            arch: JSON.stringify(archObj),
            session_id: extra.sessionId,
          },
        });
        return {
          content: [
            {
              type: 'text',
              text: `${res.text}\nJSON Schema: ${zodToJsonSchemaString(PageArchitectureSchema)}\nArchitecture ID: ${record.id}`,
            },
          ],
        };
      }),
    );
  }

  private registerGenerateHtmlTool(server: McpServer) {
    server.tool(
      this.toolNameMap.generateHtml,
      'Generate a complete UI draft using HTML/Tailwind CSS according to the design requirements or UI architecture',
      codeParamsSchema,
      this.toolHandlerWrapper<typeof codeParamsSchema>(this.toolNameMap.generateHtml, async (params, extra) => {
        const { codeId, code, pageName } = await this.generateHtml(params, extra);
        return {
          content: [
            {
              type: 'text',
              text: stripIndents`
                    这份代码的codeId为: ${codeId}
                    对应页面是: ${pageName}
                    \`\`\`html
                    ${code}
                    \`\`\`
                    `,
            },
          ],
          isError: false,
        };
      }),
    );

    server.tool(
      this.toolNameMap.generateHtmlReturnUrl,
      stripIndents`Generate a complete UI draft using HTML/Tailwind CSS according to the design requirements or UI architecture.
      The difference from generate-html is that this tool returns the URL of the generated HTML file`,
      codeParamsSchema,
      this.toolHandlerWrapper<typeof codeParamsSchema>(
        this.toolNameMap.generateHtmlReturnUrl,
        async (params, extra) => {
          const { codeId, pageName } = await this.generateHtml(params, extra);
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  url: `${this.baseUrl}/${codeId}`,
                  pageName,
                  pageId: params.targetPageId,
                }),
              },
            ],
            isError: false,
          };
        },
      ),
    );
  }

  private async generateHtml(params: z.infer<typeof codeParamsSchemaObject>, extra: RequestHandlerExtra<any, any>) {
    validateParamsByZod(codeParamsSchema, params);
    const { pageType, architectureId, targetPageId, previousGeneratedCodes } = params;

    const pageArchitecture = await this.prisma.mcp_ui_architecture.findUnique({
      where: { id: architectureId },
    });
    if (!pageArchitecture) {
      throw new Error('page architecture not found');
    }

    const { pages } = JSON.parse(pageArchitecture.arch) as PageArchitecture;
    if (!pages || pages.length === 0) {
      throw new Error('pages is empty');
    }
    const targetPage = pages.find((page) => page.id === targetPageId);
    if (!targetPage) {
      throw new Error('target page not found');
    }

    const systemPrompt = getPrompt('genHtml', {
      page_type: getPageTypeString(pageType as PageType),
    });
    const messages: CoreMessage[] = [{ role: 'system', content: systemPrompt }];

    let firstPrompt = stripIndents`
        基于以下设计架构生成${getPageTypeString(pageType as PageType)}代码
        设计架构：
        \`\`\`json
        ${JSON.stringify({ ...pageArchitecture, pages: [targetPage] })}
        \`\`\`
        JSON Schema:
        \`\`\`json
        ${zodToJsonSchemaString(PageArchitectureSchema)}
        \`\`\`
        `;
    const previousCodes = await this.getCodeFromDb(extra.sessionId, previousGeneratedCodes);
    if (previousCodes.length) {
      firstPrompt = stripIndents`${firstPrompt}
          ## 以下是已经生成的代码，请参考
          ${previousCodes.reduce((acc, cur) => {
            const pn = pages.find((page) => page.id === cur.pageId)?.n;
            // 兜底，如果没找到name，则用pageId作为标题
            const title = pn ? `pageName:${pn}` : `pageId:${cur.pageId}`;
            return acc + `\n### ${title}\n\`\`\`html\n${cur.code}\`\`\``;
          }, '')}
          `;
    }
    messages.push({
      role: 'user',
      content: [
        { type: 'text', text: firstPrompt },
        {
          type: 'text',
          text: `请生成id为${targetPage.id}的页面代码`,
        },
      ],
    });
    const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-7');
    const userId = 'mcp';
    const abortController = new AbortController();
    extra.signal.onabort = () => {
      abortController.abort();
    };

    this.track.trackCallAiStart({ userId, task: TASK.gen_html, modelId, platform });
    const res = await this.aiBaseService.chatTextV2(
      {
        platform,
        modelId,
        userId,
        abort: abortController,
      },
      { messages, maxTokens: 64000 },
    );
    this.track.trackCallAiStop({ userId, task: TASK.gen_html, modelId, platform, usage: res.usage });
    if (res.finishReason === 'error') throw new Error(res.text);
    let completeHtml = res.text;
    try {
      completeHtml = await this.generateCodeService.generateImage4Html(res.text);
    } catch (error) {}
    const { id: codeId } = await this.saveCodeToDb({
      code: completeHtml,
      pageId: targetPageId,
      architectureId,
    });
    return {
      codeId,
      code: await this.generateCodeService.combineCompleteHtml(completeHtml),
      pageName: targetPage.n,
    };
  }

  private saveCodeToDb(data: { code: string; pageId: string; architectureId: number }) {
    return this.prisma.mcp_gen_code.create({
      data: {
        code: data.code,
        page_id: data.pageId,
        arch_id: data.architectureId,
      },
    });
  }

  private async getCodeFromDb(sessionId: string, codeIds: number[]) {
    try {
      if (!codeIds || codeIds.length === 0) return [];
      const codes = await this.prisma.mcp_gen_code.findMany({
        where: { id: { in: codeIds } },
      });

      const result = codes.map((o) => {
        return {
          codeId: o.id,
          code: minifyHtml(o.code),
          pageId: o.page_id,
        };
      });

      return result;
    } catch (error) {
      this.logger.error(`[ai-gen-design-mcp]从数据库获取html代码失败：${error.message}`, '', { codeIds, sessionId });
      return [];
    }
  }
}
