import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

import { ArchitectureContext, PageArchitecture, PageType, UpdateMeasure } from '../define';

export class AiUpdateArchitectureDto {
  @IsNotEmpty({ message: 'sessionId is required' })
  sessionId: string;

  @IsNotEmpty({ message: 'messageId is required' })
  messageId: string;

  @ApiProperty({ description: '更新的尺度', enum: ['whole', 'page', 'section'] })
  @IsOptional()
  updateMeasure?: UpdateMeasure;

  @ApiProperty({ description: '更新的page或者section的ID', type: String })
  @IsOptional()
  updateId?: string;

  @ApiProperty({ description: '页面架构', type: Object })
  @IsOptional()
  pageArchitecture?: PageArchitecture;

  @ApiProperty({ description: '页面类型', enum: PageType })
  @IsOptional()
  pageType: PageType;

  @ApiProperty({ description: '上下文', type: Object })
  @IsOptional()
  context?: ArchitectureContext;
}
