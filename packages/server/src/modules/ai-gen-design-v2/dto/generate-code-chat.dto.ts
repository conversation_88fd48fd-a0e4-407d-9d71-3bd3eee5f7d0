import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

import { GenerateCodeContext, PageArchitecture, PageType } from '../define';

export class GenerateCodeChatDto {
  @ApiProperty({
    description: '页面类型',
    enum: PageType,
  })
  @IsOptional()
  pageType: PageType;

  @ApiProperty({ description: '提示词', type: String })
  @IsNotEmpty({ message: 'prompt is required' })
  prompt: string;

  @ApiProperty({
    description: '页面UI设计架构',
    type: Object,
  })
  @IsNotEmpty({ message: 'pageArchitecture is required' })
  pageArchitecture: PageArchitecture;

  @ApiProperty({
    description: '会话ID',
    type: String,
  })
  @IsNotEmpty({ message: 'sessionId is required' })
  sessionId: string;

  @ApiProperty({
    description: 'architecture 消息ID',
    type: String,
  })
  @IsNotEmpty({ message: 'architectureMessageId is required' })
  architectureMessageId: string;

  @ApiProperty({ description: '上下文', type: Object })
  @IsOptional()
  context?: GenerateCodeContext;
}
