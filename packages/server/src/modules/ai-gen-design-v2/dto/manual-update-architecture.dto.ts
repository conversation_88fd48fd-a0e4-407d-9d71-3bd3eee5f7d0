import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

import { PageArchitecture } from '../define';

export class ManualUpdateArchitectureDto {
  @ApiProperty({ description: '会话ID', type: String })
  @IsNotEmpty({ message: 'sessionId is required' })
  sessionId: string;

  @ApiProperty({ description: '消息ID', type: String })
  @IsNotEmpty({ message: 'messageId is required' })
  messageId: string;

  @ApiProperty({ description: '页面架构', type: Object })
  @IsOptional()
  pageArchitecture?: PageArchitecture;
}
