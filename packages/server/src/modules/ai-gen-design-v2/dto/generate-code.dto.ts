import { IMAGE_MODELS, ImageModel } from '@modules/generate-image/define';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

import { GenerateCodeContext, PageArchitecture, PageType } from '../define';

export class GenerateCodeDto {
  @ApiProperty({
    description: '页面类型',
    enum: PageType,
  })
  @IsOptional()
  pageType: PageType;

  @ApiProperty({
    description: '页面UI设计架构',
    type: Object,
  })
  @IsNotEmpty({ message: 'pageArchitecture is required' })
  pageArchitecture: PageArchitecture;

  @ApiProperty({
    description: '需要生成的页面ID',
    type: String,
  })
  @IsNotEmpty({ message: 'targetPageId is required' })
  targetPageId: string;

  @ApiProperty({
    description: '之前已生成页面的代码ID',
    type: Array,
    required: false,
  })
  @IsOptional()
  previousGeneratedCodes?: string[];

  @ApiProperty({
    description: 'architecture 消息ID',
    type: String,
  })
  @IsNotEmpty({ message: 'architectureMessageId is required' })
  architectureMessageId: string;

  @ApiProperty({ description: '上下文', type: Object })
  @IsOptional()
  context?: GenerateCodeContext;

  @ApiProperty({ description: '生图模型', enum: IMAGE_MODELS })
  @IsOptional()
  imageModel?: ImageModel;
}
