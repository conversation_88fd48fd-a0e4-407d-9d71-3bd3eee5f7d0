import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

import { PageArchitecture, PageType } from '../define';

export class SuggestDto {
  @ApiProperty({
    description: '分析内容',
    type: String,
  })
  @IsNotEmpty({ message: 'prompt is required' })
  prompt: string;

  @ApiProperty({
    description: '页面类型',
    enum: PageType,
  })
  @IsNotEmpty({ message: 'pageType is required' })
  pageType: PageType;

  @ApiProperty({
    description: '是否是更新建议, 1是, 0否',
    type: Number,
  })
  @IsOptional()
  update?: 1 | 0;

  @ApiProperty({
    description: '已存在的页面架构',
    type: Object,
  })
  @IsOptional()
  pageArchitecture?: PageArchitecture;
}
