import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

import { ArchitectureContext, PageType } from '../define';

export class GenerateArchitectureDto {
  @ApiProperty({ description: '分析内容' })
  @IsNotEmpty({ message: 'prompt is required' })
  prompt: string;

  @ApiProperty({ description: '页面类型', enum: PageType })
  @IsOptional()
  pageType: PageType;

  @ApiProperty({ description: '会话ID', type: String })
  @IsNotEmpty({ message: 'sessionId is required' })
  sessionId: string;

  @ApiProperty({ description: '上下文', type: Object })
  @IsOptional()
  context?: ArchitectureContext;

  @ApiProperty({ description: '是否开启隐式垫图', type: Boolean })
  @IsOptional()
  openInternalReferImage?: boolean;
}
