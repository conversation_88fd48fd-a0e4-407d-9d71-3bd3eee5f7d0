import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

import { ArchitectureContext, PageType } from '../define';

export class RegenerateArchitectureDto {
  @ApiProperty({
    description: '对话编号',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'sessionId is required' })
  sessionId: string;

  @ApiProperty({
    description: '消息编号',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'messageId is required' })
  messageId: string;

  @ApiProperty({ description: '页面类型', enum: PageType })
  @IsOptional()
  pageType: PageType;

  @ApiProperty({ description: '上下文', type: Object })
  @IsOptional()
  context?: ArchitectureContext;
}
