import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { AIChatService } from '@modules/ai-chat-session/ai-chat-session.service';
import { CosService } from '@modules/cos/cos.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { TrackService } from '@modules/track/track.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { $Enums } from '@prisma/client';
import { firstValueFrom } from 'rxjs';

import { PageType, PageTypeDB, TASK, UseImageContent } from './define';
import { CreateTaskDto } from './dto/create-task-dto';
import { SuggestDto } from './dto/suggest.dto';
import { getPrompt } from './prompt';
import { extractUserQuery, getPageTypeString } from './utils';
import { removeArchitecture } from './utils/removeArchitecture';

@Injectable()
export class AiGenDesignService {
  public readonly abortControllerMap: Map<string, AbortController[]> = new Map();

  constructor(
    private readonly track: TrackService,
    private readonly aiBaseService: AiBaseService,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly aiChatService: AIChatService,
    private readonly cosService: CosService,
  ) {}

  public async genUiSuggest(requestId: string, userId: string, dto: SuggestDto) {
    const { prompt, pageType = PageType.app, update = 0, pageArchitecture } = dto;
    if (!prompt.trim()) return '';
    const abortController = new AbortController();
    this.abortControllerMap.set(requestId, [abortController]);
    let systemPrompt = getPrompt('uiSuggest', {
      page_type: getPageTypeString(pageType),
    });
    if (update === 1) {
      systemPrompt += `\n${getPrompt('uiUpdateSuggest', {
        page_architecture: JSON.stringify(pageArchitecture),
      })}`;
    }
    const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-5');
    this.track.trackCallAiStart({ userId, task: TASK.ui_suggest, modelId, platform });
    const res = await this.aiBaseService.chatTextV2(
      {
        platform,
        modelId,
        userId,
        abort: abortController,
      },
      {
        system: systemPrompt,
        messages: [{ role: 'user', content: prompt }],
        temperature: 1,
      },
    );
    this.track.trackCallAiStop({ userId, task: TASK.ui_suggest, modelId, platform, usage: res.usage });
    if (res.finishReason === 'error') return '';
    if (res.text === 'error') return '';
    return res.text;
  }

  public async createTask(userId: string, dto: CreateTaskDto) {
    const { pageType } = dto;
    const session = await firstValueFrom(
      this.aiChatService.createSession(userId, undefined, $Enums.ai_chat_session_chat_type.text_to_ui),
    );
    if (!session) {
      throw new BadRequestException('创建会话失败');
    }
    return this.prisma.ai_task_text_to_ui.create({
      data: {
        task_session_id: session.id,
        created_user: userId,
        page_type: PageTypeDB[pageType],
      },
    });
  }

  public async getTaskMessages(sessionId: string) {
    const originalMessages = await this.aiChatService.getMessages(sessionId);

    const architectureRecords = await this.prisma.ai_task_text_to_ui_architecture.findMany({
      where: { task_session_id: sessionId },
      select: { architecture_message_id: true, content: true },
    });
    const map = {};
    architectureRecords.forEach((o) => {
      try {
        map[o.architecture_message_id] = JSON.parse(o.content as string);
      } catch {}
    });
    return originalMessages.map((it) => {
      const isAiMsg = it.role === $Enums.ai_chat_message_role.ASSISTANT;
      // user message提取<user_query>标签内的内容
      // ai message 去除<architecture>标签内的内容
      const content = isAiMsg ? removeArchitecture(it.content) : extractUserQuery(it.content);
      return {
        ...it,
        content,
        architecture: map[it.id] ?? {},
      };
    });
  }

  public async getTaskInfo(sessionId: string) {
    const taskInfo = await this.prisma.ai_task_text_to_ui.findUnique({
      where: { task_session_id: sessionId },
    });
    const referImage = await this.prisma.ai_task_text_to_ui_refer_image.findUnique({
      where: { task_session_id: sessionId },
    });
    if (!taskInfo) return null;
    let image;
    if (referImage) {
      const signUrl = this.cosService.generateCdnSignUrl(referImage.file_key);
      image = {
        url: signUrl,
        data: referImage.file_key,
        reference: referImage.refer_content?.split(',')?.reduce(
          (acc, cur) => {
            acc[cur] = true;
            return acc;
          },
          {} as Record<UseImageContent, boolean>,
        ),
      };
    }
    return {
      ...taskInfo,
      image,
    };
  }
}
