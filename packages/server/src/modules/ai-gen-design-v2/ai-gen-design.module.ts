import { AiBaseModule } from '@modules/ai-base/ai-base.module';
import { AIChatModule } from '@modules/ai-chat-session/ai-chat-session.module';
import { ConcurrentTaskExecutorModule } from '@modules/concurrent-task-executor/concurrent-task-executor.module';
import { CosModule } from '@modules/cos/cos.module';
import { GenerateImageModule } from '@modules/generate-image/generate-image.module';
import { HtmlParserModule } from '@modules/html-parser/html-parser.module';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { TrackModule } from '@modules/track/track.module';
import { UIRagModule } from '@modules/ui-rag/UIRag.module';
import { HttpModule } from '@nestjs/axios';
import { Logger, Module } from '@nestjs/common';

import { AiGenDesignController } from './ai-gen-design.controller';
import { AiGenDesignService } from './ai-gen-design.service';
import { GenerateCodeController } from './generate-code.controller';
import { GenerateCodeService } from './generate-code.service';
import { McpController } from './mcp/mcp.controller';
import { McpToolsService } from './mcp/mcp-tools.service';
import { PageArchitectureController } from './page-architecture.controller';
import { PageArchitectureService } from './page-architecture.service';

@Module({
  imports: [
    HtmlParserModule,
    HttpModule,
    AiBaseModule,
    PrismaModule,
    CosModule,
    AIChatModule,
    ConcurrentTaskExecutorModule,
    UIRagModule,
    TrackModule,
    GenerateImageModule,
  ],
  providers: [Logger, AiGenDesignService, McpToolsService, GenerateCodeService, PageArchitectureService],
  controllers: [AiGenDesignController, PageArchitectureController, GenerateCodeController, McpController],
})
export class AiGenDesignModuleV2 {}
