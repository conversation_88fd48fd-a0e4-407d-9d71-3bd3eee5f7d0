import { BadRequestException, Body, Controller, Get, Headers, Post, Query, Req } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { HEADER_X_USER_ID } from '@shared/constants/system.constants';
import { Request } from 'express';

import { GenerateCodeDto } from './dto/generate-code.dto';
import { GenerateCodeService } from './generate-code.service';

@Controller('ai-gen-design/v2/code')
export class GenerateCodeController {
  constructor(private readonly generateCodeService: GenerateCodeService) {}

  @ApiOperation({
    description: '生成html',
  })
  @Post('/generate')
  async generateCode(@Req() req: Request, @Body() dto: GenerateCodeDto, @Headers(HEADER_X_USER_ID) userId: string) {
    // const requestId = req['requestId'];

    return this.generateCodeService.generateHtml(userId, dto);
  }

  @ApiOperation({
    description: '停止生成html',
  })
  @Post('/stop')
  async stopGenerateCode(@Body() body: { architectureMessageId: string }) {
    if (!body.architectureMessageId) {
      throw new BadRequestException('architectureMessageId is required');
    }
    return this.generateCodeService.abortGenCodeTask(body.architectureMessageId);
  }

  @ApiOperation({
    description: '获取代码',
  })
  @Get('/list')
  async getCodeList(@Query('architectureMessageId') architectureMessageId: string) {
    return this.generateCodeService.getCodesByArchitectureMessageId(architectureMessageId);
  }
}
