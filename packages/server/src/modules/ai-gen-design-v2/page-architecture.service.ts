import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { StreamChunk, StreamChunkMetadata } from '@modules/ai-base/define';
import { AIChatService } from '@modules/ai-chat-session/ai-chat-session.service';
import { CosService } from '@modules/cos/cos.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { TrackService } from '@modules/track/track.service';
import { UIRagService } from '@modules/ui-rag/UIRag.service';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import {
  extractArchitecture,
  PageSchema,
  PageWithComponentSchema,
  SectionSchema,
  SectionWithComponentSchema,
  stripIndents,
  zodToJsonSchemaString,
} from '@tencent/design-ai-utils';
import { fetchImage } from '@utils/fetch-image';
import { smoothStream } from 'ai';
import { finalize, map, Observable, tap } from 'rxjs';

import { PageType, TASK, UseImageContent } from './define';
import { AiUpdateArchitectureDto } from './dto/ai-update-architecture.dto';
import { GenerateArchitectureDto } from './dto/generate-architecture.dto';
import { ManualUpdateArchitectureDto } from './dto/manual-update-architecture.dto';
import { RegenerateArchitectureDto } from './dto/regenerate-architecture.dto';
import { getPageArchitectureSystemPrompt, getPrompt } from './prompt';
import { addUserQuery, confirmReferImage, confirmUseDesignComponent } from './utils';

@Injectable()
export class PageArchitectureService {
  public readonly abortControllerMap: Map<string, AbortController[]> = new Map();

  constructor(
    private readonly logger: Logger,
    private readonly track: TrackService,
    private readonly aiBaseService: AiBaseService,
    private readonly prisma: PrismaService,
    private readonly aiChatService: AIChatService,
    private readonly cosService: CosService,
    private readonly uiRagService: UIRagService,
  ) {}

  public async generatePageArchitecture(requestId: string, userId: string, dto: GenerateArchitectureDto) {
    const { pageType = PageType.app, context = {}, sessionId, prompt } = dto;

    const [taskInfoRecord, latestArchitectureRecord] = await Promise.all([
      this.prisma.ai_task_text_to_ui.findUnique({
        where: { task_session_id: sessionId },
        select: { task_session_id: true },
      }),
      this.prisma.ai_task_text_to_ui_architecture.findFirst({
        where: { task_session_id: sessionId },
        select: { content: true },
        orderBy: { created_at: 'desc' },
      }),
    ]);

    if (!taskInfoRecord) {
      throw new BadRequestException('task not found');
    }

    // 没有architecture记录，则认为是此次会话的第一次生成
    const isFirst = !latestArchitectureRecord;

    let userPrompt = addUserQuery(prompt);
    // 垫图分析结果
    let analyzeImageText;

    if (isFirst) {
      // 只有初次生成时，才需要垫图逻辑
      try {
        const referImageRes = await this.handleReferImage(userPrompt, userId, dto);
        if (referImageRes && referImageRes.success && referImageRes.text) {
          userPrompt += `\n${context?.image?.customizeRequirement || '严格遵守如下设计要求：'}\n${referImageRes.text}`;
          analyzeImageText = referImageRes.text;
        }
      } catch (error) {
        // 垫图失败不影响后续流程
        this.logger.error('[ai-gen-design]垫图失败：' + error.message, '', { userId, requestId });
      }
    } else {
      userPrompt += stripIndents`
设计可能有修改，请以如下设计内容为准：
\`\`\`json
${latestArchitectureRecord.content}
\`\`\`
`;
    }

    const useDesignComponent = confirmUseDesignComponent(context?.designComponent);
    const systemPrompt = getPageArchitectureSystemPrompt(pageType, context, useDesignComponent);

    const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-7');
    this.track.trackCallAiStart({ userId, task: TASK.gen_page_architecture, modelId, platform });
    const streamRes = await this.aiChatService.completion({
      sessionId,
      platform,
      modelId,
      system: systemPrompt,
      prompt: userPrompt,
      maxTokens: 64000,
      experimental_transform: smoothStream({
        chunking: /\S{1,2}|\s{1,3}/m, // 匹配1-2个非空白字符或1-3个空白字符
      }),
      initialTitle: dto.prompt,
    });

    let resText = '';
    let messageMetadata: StreamChunkMetadata;
    return streamRes.pipe(
      tap((chunk) => {
        if (!chunk) return;
        const streamChunk = JSON.parse(chunk) as StreamChunk;
        if (streamChunk.type === '[START]') {
          messageMetadata = streamChunk.metadata;
        }
        if (streamChunk.type === '[TEXT]') {
          resText += streamChunk.content;
        }
        if (streamChunk.type === '[ERROR]') {
          this.logger.error(`[ai-gen-design]generatePageArchitecture 接收到error chunk：${streamChunk.error}`, '', {
            messageId: messageMetadata?.messageId,
            sessionId,
            requestId,
          });
        }
        if (streamChunk.type === '[DONE]') {
          // 数据上报
          this.track.trackCallAiStop({
            userId,
            task: TASK.gen_page_architecture,
            modelId,
            platform,
            usage: streamChunk.metadata?.usage,
          });
        }
      }),
      // 存入db
      finalize(async () => {
        const promises: Promise<any>[] = [
          (async () => {
            if (!messageMetadata?.messageId) return;
            const { architecture } = extractArchitecture(resText);
            if (architecture) {
              await this.prisma.ai_task_text_to_ui_architecture.create({
                data: {
                  task_session_id: sessionId,
                  architecture_message_id: messageMetadata.messageId,
                  content: JSON.stringify(architecture),
                },
              });
            }
          })(),
        ];

        if (isFirst && confirmReferImage(context?.image)) {
          promises.push(
            this.prisma.ai_task_text_to_ui_refer_image.create({
              data: {
                task_session_id: sessionId,
                file_key: context.image.data,
                refer_content: Object.keys(context.image.reference ?? {})
                  .filter((key) => context.image.reference[key])
                  .join(','),
                analyze_text: analyzeImageText,
              },
            }),
          );
        }
        if (isFirst && useDesignComponent) {
          promises.push(
            this.prisma.ai_task_text_to_ui.update({
              where: { task_session_id: sessionId },
              data: {
                design_component: useDesignComponent ? 1 : 0,
              },
            }),
          );
        }
        await Promise.all(promises);
      }),
    );
  }

  public async regeneratePageArchitecture(requestId: string, userId: string, dto: RegenerateArchitectureDto) {
    const { messageId, sessionId, pageType, context } = dto;

    const taskInfoRecord = await this.prisma.ai_task_text_to_ui.findUnique({
      where: { task_session_id: sessionId },
    });
    if (!taskInfoRecord) {
      throw new BadRequestException('task not found');
    }

    const systemPrompt = getPageArchitectureSystemPrompt(pageType, context);
    const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-7');

    this.track.trackCallAiStart({ userId, task: TASK.gen_page_architecture, modelId, platform });

    const streamRes = await this.aiChatService.regenerate({
      sessionId,
      messageId,
      platform,
      modelId,
      system: systemPrompt,
      maxTokens: 64000,
      experimental_transform: smoothStream({
        chunking: /\S{1,2}|\s{1,3}/m, // 匹配1-2个非空白字符或1-3个空白字符
      }),
    });

    let resText = '';
    let messageMetadata: StreamChunkMetadata;
    return streamRes.pipe(
      tap((chunk) => {
        if (!chunk) return;
        const streamChunk = JSON.parse(chunk) as StreamChunk;
        if (streamChunk.type === '[START]') {
          messageMetadata = streamChunk.metadata;
        }
        if (streamChunk.type === '[TEXT]') {
          resText += streamChunk.content;
        }
        if (streamChunk.type === '[ERROR]') {
          this.logger.error(`[ai-gen-design]regeneratePageArchitecture 接收到error chunk：${streamChunk.error}`, '', {
            messageId,
            sessionId,
            requestId,
          });
        }

        if (streamChunk.type === '[DONE]') {
          // 数据上报
          this.track.trackCallAiStop({
            userId,
            task: TASK.gen_page_architecture,
            modelId,
            platform,
            usage: streamChunk.metadata?.usage,
          });
        }
      }),
      finalize(async () => {
        if (!messageMetadata?.messageId) return;
        const { architecture } = extractArchitecture(resText);
        if (architecture) {
          await this.prisma.ai_task_text_to_ui_architecture.create({
            data: {
              task_session_id: sessionId,
              architecture_message_id: messageMetadata.messageId,
              content: JSON.stringify(architecture),
            },
          });
        }
      }),
    );
  }

  public async updatePageArchitectureByAi(requestId: string, userId: string, dto: AiUpdateArchitectureDto) {
    const abortController = new AbortController();
    this.abortControllerMap.set(requestId, [abortController]);
    const { messageId, pageArchitecture, updateMeasure, updateId, context, pageType, sessionId } = dto;

    const useDesignComponent = confirmUseDesignComponent(context?.designComponent);
    const systemPrompt = getPageArchitectureSystemPrompt(pageType, context, useDesignComponent);

    let userPrompt;
    switch (updateMeasure) {
      case 'page':
        const pageExist = pageArchitecture.pages?.some((o) => o?.id === updateId);
        if (!pageExist) throw new BadRequestException('page not found');
        userPrompt = stripIndents`
已有设计架构
\`\`\`json
${JSON.stringify(pageArchitecture)}
\`\`\`
重新设计指定页面(id:${updateId})，页面id不变,其他页面的全部内容也不变
<only_json>以如下json schema回答:
\`\`\`json
${zodToJsonSchemaString(useDesignComponent ? PageWithComponentSchema : PageSchema)}
\`\`\``;
        break;
      case 'section':
        const sectionPage = pageArchitecture.pages?.find((o) => o?.sections?.some((s) => s?.id === updateId));
        if (!sectionPage) throw new BadRequestException('section not found');
        userPrompt = stripIndents`
已有设计架构
\`\`\`json
${JSON.stringify(pageArchitecture)}
\`\`\`
重新设计指定页面(id:${sectionPage.id})中的指定区块(id:${updateId})，只设计这一个区块，区块id不变,其他页面和区块的全部内容也不变
<only_json>以如下json schema回答:
\`\`\`json
${zodToJsonSchemaString(useDesignComponent ? SectionWithComponentSchema : SectionSchema)}
\`\`\``;
        break;
      default:
        userPrompt = `重新设计整个架构<only_json>`;
        break;
    }

    const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-7');
    this.track.trackCallAiStart({ userId, task: TASK.gen_page_architecture, modelId, platform });
    const streamRes = await this.aiBaseService.chatStreamV2(
      {
        platform,
        modelId,
        userId,
        abort: abortController,
      },
      {
        system: systemPrompt,
        messages: [{ role: 'user', content: userPrompt }],
        temperature: 1,
      },
    );

    let resText = '';
    return streamRes.pipe(
      tap((chunk) => {
        if (chunk instanceof Observable) return;
        if (chunk.type === '[TEXT]') {
          resText += chunk.content;
        } else if (chunk.type === '[DONE]') {
          // 数据上报
          this.track.trackCallAiStop({
            userId,
            task: TASK.gen_page_architecture,
            modelId,
            platform,
            usage: chunk.metadata?.usage,
          });
        } else if (chunk.type === '[ERROR]') {
          this.logger.error(`[ai-gen-design]updatePageArchitectureByAi 接收到error chunk：${chunk.error}`, '', {
            messageId,
            sessionId,
            requestId,
          });
        }
      }),
      map((chunk) => JSON.stringify(chunk)),
      finalize(async () => {
        try {
          const newArchitecture = pageArchitecture;
          const resObj = JSON.parse(resText);
          switch (updateMeasure) {
            case 'page': {
              for (const page of newArchitecture.pages ?? []) {
                if (page.id === updateId) {
                  Object.assign(page, resObj);
                  break;
                }
              }
              break;
            }
            case 'section': {
              outer: for (const page of newArchitecture.pages ?? []) {
                for (const section of page.sections ?? []) {
                  if (section.id === updateId) {
                    Object.assign(section, resObj);
                    break outer;
                  }
                }
              }
              break;
            }
          }

          await this.prisma.ai_task_text_to_ui_architecture.update({
            where: { architecture_message_id: messageId },
            data: { content: JSON.stringify(newArchitecture), updated_at: new Date().toISOString() },
          });
        } catch (err) {
          this.logger.error(`[ai-gen-design]AI更新pageArchitecture存入DB失败：${err.message}`, '', { messageId });
          throw err;
        }
      }),
    );
  }

  /** 垫图逻辑，分用户垫图和隐式垫图两种 */
  private async handleReferImage(initialPrompt: string, userId: string, dto: GenerateArchitectureDto) {
    const { context, pageType, openInternalReferImage = false } = dto;

    let imageAnalysisRes: Awaited<ReturnType<typeof this.analyzeImage>> | undefined;
    if (confirmReferImage(context?.image)) {
      const imgBase64 = await this.cosService.getObjectBase64({
        Key: context?.image?.data,
      });
      if (!imgBase64) {
        return;
      }
      imageAnalysisRes = await this.analyzeImage(userId, context?.image?.reference, imgBase64);
    } else if (openInternalReferImage && pageType === PageType.app) {
      // 当前只支持移动端垫图
      const internalImage = await this.uiRagService.retrievalImage(initialPrompt);
      const firstImage = internalImage?.data?.[0];
      if (internalImage.success && firstImage?.url) {
        imageAnalysisRes = await this.analyzeImage(
          userId,
          { structure: true, style: true, copywriting: false },
          await fetchImage(firstImage.url, { type: 'base64' }),
        );
      }
    }
    return imageAnalysisRes;
  }

  private async analyzeImage(
    userId: string,
    imageReferenceContent: Record<UseImageContent, boolean>,
    imgBase64: string,
  ) {
    const failedRes = () => ({ success: false, text: '' });
    const successRes = (text: string) => ({ success: true, text });
    try {
      const promises = [];
      const systemPromptMap = {
        structure: getPrompt('analyzeUiImageStructure'),
        style: getPrompt('analyzeUiImageStyle'),
        copywriting: getPrompt('analyzeUiImageCopywriting'),
      };
      const titleMap = {
        structure: '页面结构要求',
        style: '视觉样式要求',
        copywriting: '文案要求',
      };
      Object.keys(imageReferenceContent).forEach((key) => {
        if (imageReferenceContent[key]) {
          const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-5');
          this.track.trackCallAiStart({ userId, task: TASK.analyze_image, modelId, platform });
          promises.push(
            this.aiBaseService
              .chatTextV2(
                {
                  platform,
                  modelId,
                  userId,
                },
                {
                  system: systemPromptMap[key],
                  messages: [
                    {
                      role: 'user',
                      content: [
                        { type: 'text', text: '分析图片' },
                        { type: 'image', image: imgBase64 },
                      ],
                    },
                  ],
                },
              )
              .then((res) => {
                if (res.finishReason === 'error') {
                  throw new Error(`${titleMap[key]}分析失败：${res.text}`);
                }
                this.track.trackCallAiStop({
                  userId,
                  task: TASK.analyze_image,
                  modelId,
                  platform,
                  usage: res.usage,
                });
                return {
                  title: titleMap[key],
                  content: res.text,
                };
              }),
          );
        }
      });
      let res = '';
      const results = await Promise.allSettled(promises);
      let hasSuccess = false;
      results.forEach((o) => {
        if (o.status === 'fulfilled') {
          res += `\n# ${o.value.title}\n${o.value.content}\n`;
          hasSuccess = true;
        } else {
          this.logger.error('[ai-gen-design]分析图片单个失败：' + o.reason, '');
        }
      });
      if (!hasSuccess) return failedRes();
      return successRes(res);
    } catch (error) {
      this.logger.error('[ai-gen-design]分析图片整体失败：' + error.message, '');
      return failedRes();
    }
  }

  public async updatePageArchitectureByManual(body: ManualUpdateArchitectureDto) {
    return this.prisma.ai_task_text_to_ui_architecture.update({
      where: { architecture_message_id: body.messageId },
      data: {
        content: JSON.stringify(body.pageArchitecture),
      },
    });
  }

  public async architectureAlreadyGenerateCheck(architectureMessageId: string) {
    const codeRecord = await this.prisma.ai_task_text_to_ui_code.findFirst({
      where: { architecture_message_id: architectureMessageId },
    });
    return !!codeRecord;
  }
}
