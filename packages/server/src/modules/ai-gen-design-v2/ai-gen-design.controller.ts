import { PrismaService } from '@modules/prisma/prisma.service';
import {
  Body,
  Controller,
  Get,
  Headers,
  Logger,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { HEADER_X_USER_ID } from '@shared/constants/system.constants';
import { RequestCloseHandler } from '@shared/interceptors/request-close.interceptor';
import { Request, Response } from 'express';

import { AiGenDesignService } from './ai-gen-design.service';
import { CreateTaskDto } from './dto/create-task-dto';
import { SuggestDto } from './dto/suggest.dto';
import { GenerateCodeService } from './generate-code.service';

@Controller('ai-gen-design/v2')
export class AiGenDesignController implements RequestCloseHandler {
  constructor(
    private readonly service: AiGenDesignService,
    private readonly logger: Logger,
    private readonly prisma: PrismaService,
    private readonly generateCodeService: GenerateCodeService,
  ) {}

  requestClose(requestId: string) {
    const abortControllers = this.service.abortControllerMap.get(requestId);
    if (abortControllers && abortControllers.length > 0) {
      // this.logger.log(`[ai-gen-design]${requestId}，调用 ${abortControllers.length} 个abortController.abort`);
      abortControllers.forEach((abortController) => abortController.abort());
      this.service.abortControllerMap.delete(requestId);
    }
  }

  @ApiOperation({
    description: '获取文生UI的task信息',
  })
  @Get('/task')
  async getTaskInfo(@Query('sessionId') sessionId: string) {
    return await this.service.getTaskInfo(sessionId);
  }

  @ApiOperation({
    description: '创建一个会话和task',
  })
  @Post('/create-task')
  async createTask(@Headers(HEADER_X_USER_ID) userId: string, @Body() body: CreateTaskDto) {
    return this.service.createTask(userId, body);
  }

  @ApiOperation({
    description: '获取文生UI的历史消息',
  })
  @Get('/messages')
  async getTaskMessages(@Query('sessionId') sessionId: string) {
    return this.service.getTaskMessages(sessionId);
  }

  @ApiOperation({
    description: '聊天内容补全',
  })
  @Post('/suggest')
  async suggest(@Req() req: Request, @Body() body: SuggestDto, @Headers(HEADER_X_USER_ID) userId: string) {
    const requestId = req['requestId'];

    const suggestion = await this.service.genUiSuggest(requestId, userId, body);
    return suggestion;
  }

  @Get('/html/:id')
  async getHtml(@Res() res: Response, @Param('id') id: string) {
    const codeRecord = await this.prisma.ai_task_text_to_ui_code.findUnique({
      where: {
        code_id: id,
      },
      select: {
        code: true,
      },
    });

    if (!codeRecord) {
      throw new NotFoundException('code not found');
    }

    res.header('Content-Type', 'text/html');
    return res.send(await this.generateCodeService.combineCompleteHtml(codeRecord.code));
  }
}
