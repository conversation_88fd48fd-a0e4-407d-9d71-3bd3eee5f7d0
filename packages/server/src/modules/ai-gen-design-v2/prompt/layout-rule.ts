import { trimNewlines } from '@tencent/design-ai-utils';

export const layoutRulePrompt = trimNewlines`
构建合理的响应式栅格系统

### 栅格系统的基础结构
1. 定义与核心参数
   - 列（Column）：通常将页面横向划分为12列（桌面端主流选择），便于灵活组合不同宽度的内容模块。例如，主内容区占8列，侧边栏占4列。移动端推荐使用4列或6列系统，简化布局同时保持灵活性。
   - 槽（Gutter）：列间距建议为24-32px，确保内容分隔清晰且不过于松散。移动端可缩小至8-16px以适应屏幕，确保界面紧凑但不拥挤。
   - 边距（Margin）：页面两侧的安全边距一般为24-48px，保持内容与屏幕边缘的呼吸感。移动端应考虑使用16-24px的安全区域，特别要考虑刘海屏和曲面屏的显示限制。
2. 网格基数：推荐以8pt为基准单位（如间距、元素尺寸为8的倍数），避免半像素渲染问题。移动端可考虑使用4pt作为最小单位，在高分辨率屏幕上保持精确度。

### 响应式布局的实现
1. 断点设置，根据主流设备尺寸划分断点，例如：
   - 桌面端（≥1200px）：12列，槽宽32px
   - 平板端（768-1199px）：8列，槽宽24px
   - 移动端（<768px）：4列，槽宽16px
   - 小型移动设备（≤375px）：4列，槽宽8px，需特别优化界面元素
2. 弹性布局技术：使用Flex实现动态调整，确保内容模块在不同断点下自动换行或重组。对于移动端，考虑使用Grid布局处理复杂排列，尤其是在需要不规则布局的场景。

### 设计原则与优化策略
1. 视觉层次与对齐
   - 通过模块大小对比（如主图占2/3列，文本占1/3列）突出重点内容。
   - 严格遵循基线对齐，保证文本、图片的垂直对齐一致性。
   - 移动端注重文本大小对比，标题可使用18-24pt，正文16-18pt，确保可读性。

2. 灵活性与突破规则
   - 在统一框架内允许部分元素跨列或偏移，例如用不对称布局增强设计感。
   - 针对特殊内容（如全屏轮播图）暂时脱离栅格，但需保持与其他模块的视觉衔接。
   - 移动端可考虑关键区域使用不同于常规4列布局的特殊处理，如产品展示使用2列或3列卡片。

3. 空白与节奏感
   - 利用8pt倍数间距控制行距、段落间距，提升可读性（如标题行高32px，正文行高24px）。
   - 在信息密集区域（如电商列表）增加横向留白，避免视觉疲劳。
   - 移动端屏幕空间有限，可适当缩减非必要间距，但保持至少16pt的内容块间距。

### 实际应用案例参考
- 资讯类网站：采用12列栅格，分栏展示新闻、广告和侧边栏，如雅虎的960px固定宽度布局。移动端转为瀑布流布局，顶部放置重要新闻，底部设计"加载更多"按钮。
- 电商平台：使用模块化网格，商品卡片按4列（桌面）→2列（平板）→1-2列（移动）动态排布，搭配卡片内统一边距。移动端商品详情页采用全屏图片+底部固定购买栏设计。
- 企业官网：结合响应式网格与全屏背景图，主内容区居中于8列，侧边交互元素灵活浮动。移动端转化为内容堆叠式布局，导航转为可折叠菜单
`;
