import { ArchitectureContext, PageType } from '../define';
import { confirmUseDesignComponent, formatPrompt, getPageTypeString } from '../utils';
import { filterDesignComponentData } from '../utils/design-component';
import {
  analyzeUiImageCopywritingPrompt,
  analyzeUiImageStructurePrompt,
  analyzeUiImageStylePrompt,
} from './analyze-ui-image';
import { genHtmlNoComponentPrompt, genHtmlWithDesignComponentPrompt, templateHtmlPartPrompt } from './gen-html';
import { pageArchitecturePrompt, pageArchitectureWithDcPrompt } from './page-architecture';
import { uiSuggestPrompt, uiUpdateSuggestPrompt } from './ui-suggest';

const promptMap = {
  genHtml: genHtmlNoComponentPrompt,
  genHtmlWithDesignComponent: genHtmlWithDesignComponentPrompt,
  templateHtmlPart: templateHtmlPartPrompt,
  uiSuggest: uiSuggestPrompt,
  uiUpdateSuggest: uiUpdateSuggestPrompt,
  architecture: pageArchitecturePrompt,
  architectureWithDesignComponent: pageArchitectureWithDcPrompt,
  analyzeUiImageStructure: analyzeUiImageStructurePrompt,
  analyzeUiImageStyle: analyzeUiImageStylePrompt,
  analyzeUiImageCopywriting: analyzeUiImageCopywritingPrompt,
};

export function getPrompt(promptName: keyof typeof promptMap, swapData?: Record<string, string>) {
  let prompt = promptMap[promptName];

  if (swapData) {
    prompt = formatPrompt(prompt, swapData);
  }

  return prompt;
}

export function getPageArchitectureSystemPrompt(
  pageType: PageType = PageType.app,
  context: ArchitectureContext = {},
  isUseDesignComponent?: boolean,
) {
  let systemPrompt = '';
  if (isUseDesignComponent || confirmUseDesignComponent(context?.designComponent)) {
    const dcJsonString = filterDesignComponentData(context.designComponent);
    systemPrompt = getPrompt('architectureWithDesignComponent', {
      page_type: getPageTypeString(pageType),
      component_data: dcJsonString,
    });
  } else {
    systemPrompt = getPrompt('architecture', {
      page_type: getPageTypeString(pageType),
    });
  }
  return systemPrompt;
}
