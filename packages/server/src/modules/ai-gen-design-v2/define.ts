import {
  DesignComponentInstanceConfigSchame,
  DesignComponentsSchema,
  DesignComponentsWithResourceSchema,
  PageArchitectureSchema,
  PageSchema,
  TemplateHtmlListSchema,
} from '@tencent/design-ai-utils';
import { z } from 'zod';

export enum UseImageContent {
  structure = 'structure',
  style = 'style',
  copywriting = 'copywriting',
}

export enum PageType {
  app = 'app',
  pc = 'pc',
  // console = 'console',
}

export const PageTypeDB: Record<PageType, 1 | 2> = {
  [PageType.app]: 1,
  [PageType.pc]: 2,
};

export enum DesignMode {
  design_component = 'design_component',
  normal = 'normal',
}

type ContextActive = {
  active: boolean;
};
export interface ArchitectureContext {
  designComponent?: {
    data: DesignComponents;
  } & ContextActive;
  image?: {
    /**
     * 图片url，http/https链接
     */
    data: string;
    /**
     * 图片参考的维度
     */
    reference: Record<UseImageContent, boolean>;
    /**
     * 自定义垫图要求
     */
    customizeRequirement?: string;
  } & ContextActive;
}

export interface GenerateCodeContext {
  designComponent?: {
    data: DesignComponentsWithResource;
  } & ContextActive;
  templateHtml?: {
    data: TemplateHtmlList;
  } & ContextActive;
}

export type PageArchitecture = z.infer<typeof PageArchitectureSchema>;
export type PageSection = z.infer<typeof PageSchema>;
export type DesignComponents = z.infer<typeof DesignComponentsSchema>;
export type DesignComponentsWithResource = z.infer<typeof DesignComponentsWithResourceSchema>;
export type DesignComponentInstanceConfig = z.infer<typeof DesignComponentInstanceConfigSchame>;
export type TemplateHtmlList = z.infer<typeof TemplateHtmlListSchema>;

export const Base64Prefix = {
  jpg: 'data:image/jpeg;base64,',
};

export const TASK = {
  ui_suggest: 'ui-suggest',
  gen_page_architecture: 'gen-page-architecture',
  gen_html: 'gen-html',
  analyze_image: 'analyze-image',
} as const;
export type TaskType = (typeof TASK)[keyof typeof TASK];

export type UpdateMeasure = 'whole' | 'page' | 'section';
export const PageIdLength = 16;
