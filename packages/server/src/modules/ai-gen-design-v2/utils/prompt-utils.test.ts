import { describe, expect, it } from 'vitest';

import { formatPrompt } from './prompt-utils';

describe('提示词工具函数 (Prompt Utilities)', () => {
  describe('格式化提示词 (formatPrompt)', () => {
    it('应使用 swapData 中的值替换提示词中的占位符', () => {
      const prompt = '你好, {{name}}! 欢迎来到 {{location}}.';
      const swapData = { name: '用户', location: '这里' };
      const expected = '你好, 用户! 欢迎来到 这里.';
      expect(formatPrompt(prompt, swapData)).toBe(expected);
    });

    it('应处理多个相同的占位符', () => {
      const prompt = '{{thing}} 很重要, {{thing}} 必须做好.';
      const swapData = { thing: '测试' };
      const expected = '测试 很重要, 测试 必须做好.';
      expect(formatPrompt(prompt, swapData)).toBe(expected);
    });

    it('如果 swapData 中缺少某个键，对应的占位符不应被替换', () => {
      const prompt = '姓名: {{name}}, 年龄: {{age}}';
      const swapData = { name: '张三' };
      const expected = '姓名: 张三, 年龄: {{age}}';
      expect(formatPrompt(prompt, swapData)).toBe(expected);
    });

    it('当提示词中不包含任何占位符时，应返回原始提示词', () => {
      const prompt = '这是一个没有占位符的提示词。';
      const swapData = { key: 'value' };
      expect(formatPrompt(prompt, swapData)).toBe(prompt);
    });
  });
});
