import { describe, expect, it } from 'vitest';

import { ArchitectureContext } from '../define';
import { confirmReferImage } from './confirm-refer-image';

describe('确认是否参考图片 (confirmReferImage)', () => {
  it('当 imageContext 为假值时应返回 false', () => {
    expect(confirmReferImage(undefined)).toBe(false);
    expect(confirmReferImage(null)).toBe(false);
  });

  it('当 imageContext.active 为 false 时应返回 false', () => {
    const imageContext: ArchitectureContext['image'] = {
      active: false,
      data: 'base64string',
      reference: { structure: true, style: false, copywriting: false },
    };
    expect(confirmReferImage(imageContext)).toBe(false);
  });

  it('当 imageContext.data 为假值时应返回 false', () => {
    const imageContext1: ArchitectureContext['image'] = {
      active: true,
      data: '',
      reference: { structure: true, style: false, copywriting: false },
    };
    expect(confirmReferImage(imageContext1)).toBe(false);
  });

  it('当所有 reference 标志为 false 时应返回 false', () => {
    const imageContext: ArchitectureContext['image'] = {
      active: true,
      data: 'base64string',
      reference: { structure: false, style: false, copywriting: false },
    };
    expect(confirmReferImage(imageContext)).toBe(false);
  });

  it('当至少一个 reference 标志为 true 时应返回 true', () => {
    const imageContext1: ArchitectureContext['image'] = {
      active: true,
      data: 'base64string',
      reference: { structure: true, style: false, copywriting: false },
    };
    expect(confirmReferImage(imageContext1)).toBe(true);

    const imageContext2: ArchitectureContext['image'] = {
      active: true,
      data: 'base64string',
      reference: { structure: false, style: true, copywriting: false },
    };
    expect(confirmReferImage(imageContext2)).toBe(true);

    const imageContext3: ArchitectureContext['image'] = {
      active: true,
      data: 'base64string',
      reference: { structure: false, style: false, copywriting: true },
    };
    expect(confirmReferImage(imageContext3)).toBe(true);
  });
});
