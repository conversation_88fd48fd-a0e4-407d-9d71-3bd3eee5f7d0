import { BadRequestException } from '@nestjs/common';

import { ArchitectureContext, GenerateCodeContext } from '../define';

/**
 * 设计组件数据最大长度
 */
export const DesignComponentsMaxLength = 500000;

/**
 * @returns 确认是否使用设计组件
 */
export function confirmUseDesignComponent(
  designComponent?: ArchitectureContext['designComponent'] | GenerateCodeContext['designComponent'],
) {
  return designComponent?.active && designComponent?.data?.length > 0;
}

export function filterDesignComponentData(
  designComponent?: ArchitectureContext['designComponent'] | GenerateCodeContext['designComponent'],
) {
  const dcJsonString = JSON.stringify(
    designComponent?.data?.map((o) => {
      // 组件集去掉id，避免 LLM 直接选用组件集
      if (o.t === 'COMPONENT_SET') {
        return { ...o, id: undefined };
      }
      return o;
    }),
  );

  if (dcJsonString.length > DesignComponentsMaxLength) {
    throw new BadRequestException('designComponents is too long');
  }

  return dcJsonString;
}
