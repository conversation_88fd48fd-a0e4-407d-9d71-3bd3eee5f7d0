export const USER_QUERY = 'user_query';

export function addUserQuery(...texts: string[]) {
  const validTexts = texts.filter((text) => text && text.trim());
  if (validTexts.length === 0) return '';
  return `<${USER_QUERY}>${validTexts.join('\n')}</${USER_QUERY}>`;
}

export function extractUserQuery(text: string) {
  const regex = new RegExp(`<${USER_QUERY}>(.*?)</${USER_QUERY}>`, 's');
  const match = text.match(regex);
  return match ? match[1] : '';
}
