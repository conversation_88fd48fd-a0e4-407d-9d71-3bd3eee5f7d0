import { AwsAIModelId, VenusAIModelId } from '@modules/ai-base/define';

import { TASK, TaskType } from '../define';

export const TASK_MODEL_AWS: Record<TaskType, { platform: 'aws'; model: AwsAIModelId }> = {
  [TASK.ui_suggest]: { platform: 'aws', model: 'us.anthropic.claude-3-5-sonnet-20241022-v2:0' },
  [TASK.gen_page_architecture]: { platform: 'aws', model: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0' },
  [TASK.gen_html]: { platform: 'aws', model: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0' },
  [TASK.analyze_image]: { platform: 'aws', model: 'us.anthropic.claude-3-5-sonnet-20241022-v2:0' },
};
export const TASK_MODEL_VENUS: Record<TaskType, { platform: 'venus'; model: VenusAIModelId }> = {
  [TASK.ui_suggest]: { platform: 'venus', model: 'claude-3-5-sonnet-20241022' },
  [TASK.gen_page_architecture]: { platform: 'venus', model: 'claude-3-7-sonnet-20250219' },
  [TASK.gen_html]: { platform: 'venus', model: 'claude-3-7-sonnet-20250219' },
  [TASK.analyze_image]: { platform: 'venus', model: 'claude-3-5-sonnet-20241022' },
};
export const TASK_MODEL_DEV: Record<TaskType, { platform: 'venus'; model: VenusAIModelId }> = {
  [TASK.ui_suggest]: { platform: 'venus', model: 'gpt-4o' },
  [TASK.gen_page_architecture]: { platform: 'venus', model: 'claude-3-5-sonnet-20241022' },
  [TASK.gen_html]: { platform: 'venus', model: 'claude-3-5-sonnet-20241022' },
  [TASK.analyze_image]: { platform: 'venus', model: 'gpt-4o' },
};

export const getTaskModelMap = (envString: string) => {
  switch (envString) {
    case 'dev-cloud':
      return TASK_MODEL_VENUS;
    case 'local-dev':
      return TASK_MODEL_DEV;
    default:
      return TASK_MODEL_AWS;
  }
};
