import { describe, expect, it } from 'vitest';

import { convertObjectToText } from './convert-object-to-text';

describe('对象转文本 (convertObjectToText)', () => {
  it('应将包含字符串值的简单对象转换为文本', () => {
    const obj = {
      属性1: '值1',
      属性2: '值2',
    };
    const expected = '\n属性1：值1\n属性2：值2';
    expect(convertObjectToText(obj)).toBe(expected);
  });

  it('应忽略值为假值的属性', () => {
    const obj = {
      属性1: '值1',
      属性2: undefined,
      属性3: '值3',
    };
    const expected = '\n属性1：值1\n属性3：值3';
    expect(convertObjectToText(obj)).toBe(expected);
  });

  it('当输入为空对象时应返回空字符串', () => {
    const obj = {};
    const expected = '';
    expect(convertObjectToText(obj)).toBe(expected);
  });
});
