import { describe, expect, it } from 'vitest';

import { minifyHtml } from './minify-html';

describe('压缩 HTML (minifyHtml)', () => {
  it('应提取并处理 body 标签内的内容', () => {
    const html =
      '<html><head></head><body>  <p> Hello  </p> <!-- comment --> <img alt="alt" src="src">  </body></html>';
    const expected = '<p> Hello </p> <img>';
    expect(minifyHtml(html)).toBe(expected);
  });

  it('如果不存在 body 标签，应处理整个 HTML 字符串', () => {
    const html = '  <p> Hello  </p>\n\n<span>World</span> <!-- comment --> <img alt="alt" src="src">  ';
    const expected = '<p> Hello </p>\n<span>World</span> <img>';
    expect(minifyHtml(html)).toBe(expected);
  });

  it('应将多个连续换行符替换为单个换行符', () => {
    const html = '<p>Line 1</p>\n\n\n<p>Line 2</p>';
    const expected = '<p>Line 1</p>\n<p>Line 2</p>';
    expect(minifyHtml(html)).toBe(expected);
  });

  it('应将 HTML 标签之间的多个连续空格替换为单个空格', () => {
    const html = '<div>   <span>Text</span>   </div>';
    const expected = '<div> <span>Text</span> </div>';
    expect(minifyHtml(html)).toBe(expected);
  });

  it('应移除 img 标签的 alt/src 属性', () => {
    const html = '<img src="image.jpg" alt="description" class="test">';
    const html2 = '<img src="image.jpg" alt="description">';
    expect(minifyHtml(html)).toBe('<img class="test">');
    expect(minifyHtml(html2)).toBe('<img>');
  });

  it('应清空含有 dc 属性的 div 标签的内部 HTML 内容', () => {
    const html = '<div class="test" dc="1"> <p>Inner Content</p> </div>';
    const expected = '<div class="test" dc="1"></div>';
    expect(minifyHtml(html)).toBe(expected);
  });

  it('应清空含有 dc 属性（使用单引号）的 div 标签的内部 HTML 内容', () => {
    const html = "<div class='test' dc='1'> <p>Inner Content</p> </div>";
    const expected = "<div class='test' dc='1'></div>";
    expect(minifyHtml(html)).toBe(expected);
  });

  it('应删除 HTML 注释', () => {
    const html = '<div><!-- This is a comment --><span>Content</span></div>';
    const expected = '<div><span>Content</span></div>';
    expect(minifyHtml(html)).toBe(expected);
  });

  it('应处理包含多种需要处理情况的复杂 HTML', () => {
    const html = `
      <body>
        <h1>Title</h1> <!-- Title comment -->
        <p>
          Hello   World!

        </p>
        <img src="path/to/img.png" alt="some alt text">
        <div dc="1">
          This should be cleared.
          <span>Nested</span>
        </div>
        <div>No dc attribute</div>
      </body>
    `;
    const result = minifyHtml(html);
    const expected = `<h1>Title</h1> <p> Hello World! </p> <img> <div dc="1"></div> <div>No dc attribute</div>`;
    expect(result).toBe(expected);
  });
});
