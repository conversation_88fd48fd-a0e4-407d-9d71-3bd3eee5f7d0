import { describe, expect, it } from 'vitest';

import { addUserQuery, extractUserQuery, USER_QUERY } from './user-query';

const text = '这是一个用户查询';

describe('user query tag', () => {
  describe('addUserQuery', () => {
    it('单个参数', () => {
      const expected = `<${USER_QUERY}>${text}</${USER_QUERY}>`;
      expect(addUserQuery(text)).toBe(expected);
    });
    it('多个参数', () => {
      const expected = `<${USER_QUERY}>${text}\n${text}</${USER_QUERY}>`;
      expect(addUserQuery(text, text)).toBe(expected);
    });
    it('多个参数，其中一个是空字符串', () => {
      const expected = `<${USER_QUERY}>${text}</${USER_QUERY}>`;
      expect(addUserQuery('', text)).toBe(expected);
    });
    it('全都是空字符串', () => {
      const expected = '';
      expect(addUserQuery('', '')).toBe(expected);
    });
  });

  describe('extractUserQuery', () => {
    it('提取用户查询', () => {
      expect(extractUserQuery(`<${USER_QUERY}>${text}</${USER_QUERY}>`)).toBe(text);
    });
    it('没有匹配到的内容，返回空字符串', () => {
      expect(extractUserQuery(text)).toBe('');
    });
  });
});
