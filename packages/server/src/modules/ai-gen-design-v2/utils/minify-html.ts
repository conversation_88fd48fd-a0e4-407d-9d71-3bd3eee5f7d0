export function minifyHtml(html: string) {
  const bodyContent = html.match(/<body[^>]*>([\s\S]*)<\/body>/i)?.[1] || html;

  const processedCode = bodyContent
    .replace(/<img\s+([^>]*?)alt=['"][^'"]*['"]([^>]*?)>/gi, '<img $1$2>') // 移除image标签的alt
    .replace(/<img\s+([^>]*?)src=['"][^'"]*['"]([^>]*?)>/gi, '<img $1$2>') // 移除image标签的src
    .replace(/<div([^>]*?dc=['"][^'"]*['"][^>]*?)>([\s\S]*?)<\/div>/gi, '<div$1></div>') // 清空含有dc属性的div标签的内部HTML内容
    .replace(/<!--[\s\S]*?-->/g, '') // 删除注释
    .replace(/<(\w+)\s+>/gi, '<$1>') // 删除所有没有属性的标签中的多余空格
    .replace(/\n{2,}/g, '\n') // 多个连续的换行改成一个
    .replace(/\s{2,}/g, ' ') // 处理所有连续的多个空格，将它们都替换为一个空格
    .trim();

  return processedCode;
}
