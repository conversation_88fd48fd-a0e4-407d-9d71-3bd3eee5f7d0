// import { StreamChunk } from '@modules/ai-base/define';
// import { DeepPartial } from 'ai';
// import cloneDeep from 'lodash/cloneDeep';
// import { nanoid } from 'nanoid';
// import { map } from 'rxjs';

// import { PageArchitecture, PageIdLength, UpdateMeasure } from '../define';
// import { StreamJSONParser } from './stream-json-parser';

// interface StreamToArchitectureOptions {
//   originArchitecture?: PageArchitecture;
//   updateMeasure?: UpdateMeasure;
//   updateId?: string;
//   onParseSuccess?: (result: DeepPartial<PageArchitecture>) => void;
// }

// export function streamToArchitecture(options: StreamToArchitectureOptions) {
//   const jsonParser = new StreamJSONParser();

//   const processedPage = new Map<string, string>();

//   return map((chunk: string) => {
//     const chunkObj = JSON.parse(chunk) as StreamChunk;

//     if (chunkObj?.type === '[TEXT]') {
//       const { success, result } = jsonParser.feed(chunkObj.content);

//       if (!success) return chunkObj;

//       const { originArchitecture, updateMeasure, updateId } = options;

//       switch (updateMeasure) {
//         case 'page': {
//           const copyArchitecture = cloneDeep(originArchitecture);
//           for (const page of copyArchitecture.pages ?? []) {
//             if (page.id === updateId) {
//               page.n = result?.n ?? '';
//               page.sections = Array.isArray(result?.sections) ? result?.sections : [];
//               page.sections.forEach((sec, index) => {
//                 sec.id = `${page.id}-${index}`;
//               });
//               break;
//             }
//           }
//           chunkObj.content = JSON.stringify(copyArchitecture);
//           options.onParseSuccess?.(copyArchitecture);
//           break;
//         }
//         case 'section': {
//           const copyArchitecture = cloneDeep(originArchitecture);
//           outer: for (const page of copyArchitecture.pages ?? []) {
//             for (const section of page.sections ?? []) {
//               if (section.id === updateId) {
//                 section.n = result?.n ?? '';
//                 section.d = result?.d ?? '';
//                 break outer;
//               }
//             }
//           }
//           chunkObj.content = JSON.stringify(copyArchitecture);
//           options.onParseSuccess?.(copyArchitecture);
//           break;
//         }
//         default: {
//           // 只有整体生成时，才需要生成新的page_id
//           for (const page of result?.pages ?? []) {
//             if (!page?.n) continue;
//             if (processedPage.has(page.n)) {
//               page.id = processedPage.get(page.n);
//             } else {
//               const genId = nanoid(PageIdLength);
//               processedPage.set(page.n, genId);
//               page.id = genId;
//             }

//             page.sections?.forEach((sec, index) => {
//               sec.id = `${page.id}-${index}`;
//             });
//           }
//           chunkObj.content = JSON.stringify(result);
//           options.onParseSuccess?.(result);
//           break;
//         }
//       }
//     }

//     return chunkObj;
//   });
// }
