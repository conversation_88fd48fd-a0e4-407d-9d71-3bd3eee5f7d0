import { isAbortError } from '@ai-sdk/provider-utils';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { CosService } from '@modules/cos/cos.service';
import { ImageModel } from '@modules/generate-image/define';
import { GenerateImageService } from '@modules/generate-image/generate-image.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { TrackService } from '@modules/track/track.service';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import {
  PageArchitectureSchema,
  PageArchitectureWithComponentSchema,
  stripIndents,
  zodToJsonSchemaString,
} from '@tencent/design-ai-utils';
import { CoreMessage } from 'ai';
import { load as cheerioLoad } from 'cheerio';
import { nanoid } from 'nanoid';

import { DesignComponentInstanceConfig, PageArchitecture, PageType, TASK } from './define';
import { GenerateCodeDto } from './dto/generate-code.dto';
import { getPrompt } from './prompt';
import { confirmUseDesignComponent, getPageTypeString, minifyHtml } from './utils';

const COS_FILEKEY_PREFIX = 'COSKEY';
const COS_FILEKEY_SUFFIX = 'ENDKEY';

@Injectable()
export class GenerateCodeService {
  private readonly aiGenImageMinWidth: number = 160;
  /**
   * 保存生成代码的 abortController
   * key 格式： architectureMessageId
   * 同一个architecture同一时间下，只会有一个页面在生成代码，所以key不需要包含targetPageId
   */
  private genCodeTaskMap: Map<string, AbortController> = new Map();

  constructor(
    private readonly logger: Logger,
    private readonly track: TrackService,
    private readonly aiBaseService: AiBaseService,
    private readonly prisma: PrismaService,
    private readonly generateImageService: GenerateImageService,
    private readonly cosService: CosService,
  ) {}

  private async saveCodeToDb(data: {
    codeId: string;
    architectureMessageId: string;
    code: string;
    page: PageArchitecture['pages'][number];
  }) {
    return this.prisma.ai_task_text_to_ui_code
      .create({
        data: {
          code_id: data.codeId,
          architecture_message_id: data.architectureMessageId,
          page_name: data.page.n,
          code: data.code,
          page_id: data.page.id,
        },
      })
      .catch((dbErr) => {
        this.logger.error(`[ai-gen-design]数据库保存页面代码${data.codeId}失败: ${dbErr.message}`, dbErr.stack, {
          architectureMessageId: data.architectureMessageId,
          codeId: data.codeId,
        });
      });
  }

  private async getCodeFromDb(codeIds?: string[]) {
    try {
      if (!codeIds || codeIds.length === 0) return [];
      const codes = await this.prisma.ai_task_text_to_ui_code.findMany({
        select: {
          page_name: true,
          code_id: true,
          code: true,
        },
        where: { code_id: { in: codeIds } },
      });

      const result = codes.map((o) => {
        return {
          pageName: o.page_name,
          codeId: o.code_id,
          code: minifyHtml(o.code),
        };
      });
      return result;
    } catch (error) {
      this.logger.error(`[ai-gen-design]从数据库获取html代码失败：${error.message}`, '', {
        codeIds,
      });
      return [];
    }
  }

  public async getCodesByArchitectureMessageId(architectureMessageId: string) {
    const codes = await this.prisma.ai_task_text_to_ui_code.findMany({
      select: {
        page_name: true,
        code_id: true,
        code: true,
        page_id: true,
      },
      where: { architecture_message_id: architectureMessageId },
      orderBy: { created_at: 'asc' },
    });
    return await Promise.all(
      codes.map(async (o) => {
        return {
          pageName: o.page_name,
          pageId: o.page_id,
          codeId: o.code_id,
          code: await this.combineCompleteHtml(o.code),
        };
      }),
    );
  }

  public abortGenCodeTask(architectureMessageId: string) {
    const abortController = this.genCodeTaskMap.get(architectureMessageId);
    if (abortController) {
      this.logger.log(`generateHtml 用户停止生成代码：${architectureMessageId}`);
      abortController.abort();
      this.genCodeTaskMap.delete(architectureMessageId);
    }
  }

  public async generateHtml(userId: string, dto: GenerateCodeDto) {
    const {
      pageArchitecture,
      architectureMessageId,
      context = {},
      pageType = PageType.app,
      targetPageId,
      previousGeneratedCodes = [],
      imageModel,
    } = dto;
    const { pages } = pageArchitecture;
    if (!pages || pages.length === 0) {
      throw new BadRequestException('pages is empty');
    }

    const targetPage = pages.find((page) => page.id === targetPageId);
    if (!targetPage) {
      throw new BadRequestException('target page not found');
    }

    const abortController = new AbortController();
    this.genCodeTaskMap.set(architectureMessageId, abortController);

    const logData = { architectureMessageId, targetPageId, userId };

    const isConfirmUseDC = confirmUseDesignComponent(context?.designComponent);
    let systemPrompt = getPrompt(isConfirmUseDC ? 'genHtmlWithDesignComponent' : 'genHtml', {
      page_type: getPageTypeString(pageType),
      component_data: JSON.stringify(context?.designComponent?.data),
    });
    if (context?.templateHtml?.active && context?.templateHtml?.data?.length > 0) {
      systemPrompt += `\n${getPrompt('templateHtmlPart', {
        template_html_list: JSON.stringify(context?.templateHtml?.data),
      })}`;
    }

    try {
      let firstPrompt = stripIndents`
        基于以下设计架构生成${getPageTypeString(pageType)}代码
        设计架构：
        \`\`\`json
        ${JSON.stringify({ ...pageArchitecture, pages: [targetPage] })}
        \`\`\`
        JSON Schema:
        \`\`\`json
        ${zodToJsonSchemaString(isConfirmUseDC ? PageArchitectureWithComponentSchema : PageArchitectureSchema)}
        \`\`\`
        `;
      const previousCodes = await this.getCodeFromDb(previousGeneratedCodes);
      if (previousCodes.length) {
        firstPrompt = stripIndents`${firstPrompt}
          ## 以下是已经生成的代码，请参考
          ${previousCodes.reduce((acc, cur) => {
            return acc + `\n### ${cur.pageName}\n${cur.code}`;
          }, '')}
          `;
      }

      const messages: CoreMessage[] = [
        {
          role: 'user',
          content: [
            { type: 'text', text: firstPrompt },
            {
              type: 'text',
              text: `这一步请生成id为${targetPage.id}的页面代码 ${isConfirmUseDC ? '，page的每个section都有可能使用组件，请按要求使用组件' : ''}`,
            },
          ],
        },
      ];
      const codeId = nanoid(36);

      if (this.genCodeTaskMap.get(architectureMessageId).signal.aborted) {
        return;
      }

      const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-7');
      this.track.trackCallAiStart({ userId, task: TASK.gen_html, modelId, platform });
      const res = await this.aiBaseService.chatTextV2(
        {
          platform,
          modelId,
          userId,
          abort: this.genCodeTaskMap.get(architectureMessageId),
        },
        { messages, maxTokens: 64000, system: systemPrompt },
      );
      this.track.trackCallAiStop({ userId, task: TASK.gen_html, modelId, platform, usage: res.usage });
      if (res.finishReason === 'error') throw new Error(res.text);
      let html = res.text;
      try {
        html = await this.generateImage4Html(res.text, imageModel);
      } catch (error) {
        this.logger.error('generateHtml 给html生成图片失败：' + error.message, '', logData);
      }
      await this.saveCodeToDb({ codeId, architectureMessageId, code: html, page: targetPage });
      return {
        html: await this.combineCompleteHtml(html),
        pageName: targetPage.n,
        pageId: targetPage.id,
        codeId,
        success: true,
      };
    } catch (err) {
      if (!isAbortError(err)) {
        this.logger.error('generateHtml 失败：' + err.message, '', logData);
      }
      return { pageName: targetPage.n, pageId: targetPage.id, error: err.message, success: false };
    } finally {
      this.genCodeTaskMap.delete(architectureMessageId);
    }
  }

  public async generateImage4Html(htmlStr: string, model: ImageModel = 'flux') {
    const $ = cheerioLoad(htmlStr, null, false);
    const imgElements = $('img');
    const dcElements = $('div[dc]');

    const imgs_config: {
      cheerioEle: any;
      alt: string;
      size: { w: number; h: number };
      dcConfig?: DesignComponentInstanceConfig;
      resourceId?: string;
    }[] = [];

    imgElements.each((i, ele) => {
      const cheerioEle = $(ele);
      const { w, h } = this.computeImageSize(cheerioEle.attr('data-size'));
      imgs_config.push({
        cheerioEle,
        alt: cheerioEle.attr('alt'),
        size: { w, h },
      });
    });

    dcElements.each((i, ele) => {
      const cheerioEle = $(ele);
      const dcJson = cheerioEle.html();
      if (!dcJson) return;

      try {
        const dcConfig = JSON.parse(dcJson);
        dcConfig?.imgs?.forEach((o) => {
          const { w, h } = this.computeImageSize(o.size);
          imgs_config.push({
            cheerioEle,
            alt: o.alt,
            size: { w, h },
            dcConfig,
            resourceId: o.id,
          });
        });
      } catch {}
    });

    const imgResultsFromAI = await this.generateImageService.generateImages(
      model,
      imgs_config.map((o) => ({
        prompt: o.alt,
        width: o.size.w,
        height: o.size.w,
      })),
    );
    imgResultsFromAI.forEach((o, i) => {
      const insertKey = `${COS_FILEKEY_PREFIX}${o.fileKey}${COS_FILEKEY_SUFFIX}`;
      if (o.success) {
        const config = imgs_config[i];
        if (config.resourceId) {
          // 设计组件
          config.dcConfig.imgs.find((o: any) => o.id === config.resourceId).src = insertKey;
        } else {
          // 原生img
          config.cheerioEle.attr('src', insertKey);
        }
      }
    });

    imgs_config
      .filter((o) => o.resourceId)
      .forEach((o) => {
        o.cheerioEle.html(JSON.stringify(o.dcConfig));
      });

    return $.html();
  }

  private computeImageSize(originSize: string) {
    const dataSize = originSize?.split('*') || [];
    if (dataSize.length !== 2) return { w: this.aiGenImageMinWidth, h: this.aiGenImageMinWidth };

    const width = Number(dataSize[0]);
    const height = Number(dataSize[1]);

    if (isNaN(width) || isNaN(height) || height === 0 || width === 0) {
      return { w: this.aiGenImageMinWidth, h: this.aiGenImageMinWidth };
    }

    const ratio = width / height;
    const minHeight = this.aiGenImageMinWidth / ratio;

    return {
      w: Math.max(width, this.aiGenImageMinWidth),
      h: Math.max(height, minHeight),
    };
  }

  public async combineCompleteHtml(htmlStr: string) {
    const fileKeyRegex = new RegExp(`${COS_FILEKEY_PREFIX}(.*?)${COS_FILEKEY_SUFFIX}`, 'g');

    const matches = Array.from(htmlStr.matchAll(fileKeyRegex));
    const fileKeys = matches.map((match) => match[1]);
    const uniqueFileKeys = [...new Set(fileKeys)];

    let processedHtml = htmlStr;

    // 获取所有fileKey的CDN signUrl
    const signUrlResults = uniqueFileKeys.map((fileKey) => {
      try {
        const signUrl = this.cosService.generateCdnSignUrl(fileKey);
        return { fileKey, signUrl, success: true };
      } catch (error) {
        this.logger.error(`[combineCompleteHtml]获取CDN signUrl失败: ${error.message}`, '', { fileKey });
        return { fileKey, signUrl: null, success: false };
      }
    });

    // 替换HTML
    signUrlResults.forEach((result) => {
      if (result.success && result.signUrl) {
        const { fileKey, signUrl } = result;
        const fullMatch = `${COS_FILEKEY_PREFIX}${fileKey}${COS_FILEKEY_SUFFIX}`;
        processedHtml = processedHtml.replaceAll(fullMatch, signUrl);
      }
    });

    const html = template.replace('{{content}}', processedHtml);
    return html;
  }
}

const template = `
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://cdn.tailwindcss.com?plugins=forms"></script>
  <script src="https://unpkg.com/@fortawesome/fontawesome-free@6.7.2/js/all.min.js"></script>
  <style>* {font-family: Noto Sans SC;}</style>
</head>
<body>
  {{content}}
</body>
</html>
`;

// public async generateHtmlSchedulerChat(requestId: string, userId: string, dto: GenerateCodeChatDto) {
//   const { sessionId, prompt, pageArchitecture, pageType, context, architectureMessageId } = dto;
//   const modelId = this.taskModelMap[TASK.gen_html_scheduler_chat].model;
//   const platform = this.taskModelMap[TASK.gen_html_scheduler_chat].platform;
//   const systemPrompt = getPrompt('genHtmlScheduler', {
//     pageArchitecture: JSON.stringify(pageArchitecture),
//   });
//   this.track.trackCallAiStart(userId, TASK.gen_html_scheduler_chat, modelId, platform);
//   const streamRes = await this.aiChatService.completion({
//     sessionId,
//     platform,
//     modelId,
//     system: systemPrompt,
//     prompt: prompt,
//     maxTokens: 64000,
//     // experimental_transform: smoothStream({
//     //   chunking: /\S{1,2}|\s{1,3}/m, // 匹配1-2个非空白字符或1-3个空白字符
//     // }),
//     initialTitle: `生成${pageArchitecture.an}的代码`,
//     maxSteps: 25,
//     tools: {
//       generateHtml: tool({
//         description: '生成一个页面的完整HTML代码，此工具一次只能生成一个页面',
//         parameters: z.object({
//           targetPageId: z.string().describe('当前要生成的页面ID'),
//           previousGeneratedCodes: z
//             .array(z.string().describe('已经生成的代码ID'))
//             .optional()
//             .describe(
//               '已经生成的代码ID，可以用于参考生成当前页面，当你认为当前要生成的页面需要参考其他页面代码时可以传入，第一次生成不需要传入',
//             ),
//         }),
//         execute: async ({ previousGeneratedCodes = [], targetPageId }) => {
//           if (!targetPageId) {
//             throw new Error('targetPageId is required');
//           }
//           return await this.generateHtml(userId, {
//             pageArchitecture,
//             architectureMessageId,
//             context,
//             pageType,
//             targetPageId,
//             previousGeneratedCodes: [...new Set(previousGeneratedCodes)].filter(Boolean),
//           });
//         },
//       }),
//     },
//   });

//   return streamRes.pipe(
//     tap((chunk) => {
//       if (!chunk) return;
//       const streamChunk = JSON.parse(chunk) as StreamChunk;
//       if (streamChunk.type === '[ERROR]') {
//         this.logger.error(`[ai-gen-design]generateHtmlSchedulerChat 接收到error chunk：${streamChunk.error}`, '', {
//           sessionId,
//           requestId,
//         });
//       }
//       if (streamChunk.type === '[DONE]') {
//         // 数据上报
//         this.track.trackCallAiStop(
//           userId,
//           TASK.gen_html_scheduler_chat,
//           modelId,
//           platform,
//           streamChunk.metadata?.usage,
//         );
//       }
//     }),
//   );
// }
