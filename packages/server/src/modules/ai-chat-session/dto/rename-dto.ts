import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class RenameDto {
  @ApiProperty({
    description: '对话编号',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'sessionId is required' })
  public sessionId: string;

  @ApiProperty({
    description: '标题',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'title is required' })
  public title: string;
}
