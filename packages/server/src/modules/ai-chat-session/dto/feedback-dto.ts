import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class FeedbackDto {
  @ApiProperty({
    description: '消息编号',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'messageId is required' })
  public messageId: string;

  @ApiProperty({
    description: '反馈类型',
    type: String,
    required: false,
  })
  public type: 'like' | 'dislike'
}
