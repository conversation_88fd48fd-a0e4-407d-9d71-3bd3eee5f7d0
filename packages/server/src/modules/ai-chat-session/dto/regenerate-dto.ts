import { AIModelId, AIModelIdArray } from '@modules/ai-base/define';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsIn, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class RegenerateDto {
  @ApiProperty({
    description: '对话编号',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'chatSessionId is required' })
  public chatSessionId: string;

  @ApiProperty({
    description: '消息编号',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'childMessageId is required' })
  public childMessageId: string;

  @ApiProperty({
    description: '模型编号',
    type: String,
    required: true,
  })
  @IsString({ message: 'chatModelId 必须是字符串' })
  @IsIn(AIModelIdArray, {
    message: '无效的模型编号',
  })
  @IsNotEmpty({ message: 'chatModelId is required' })
  public chatModelId: AIModelId;

  @ApiProperty({
    description: '提示词',
    type: String,
    required: true,
  })
  @IsOptional()
  public prompt: string;

  @ApiProperty({
    description: '是否开启深度思考',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  public thinkingEnabled: boolean = false;

  @ApiProperty({
    description: '是否开启联网搜索',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  public searchEnabled: boolean = false;
}
