// import { StreamChunk } from '@modules/ai-base/define';
// import { Injectable, Logger } from '@nestjs/common';
// import { tool } from 'ai';
// import { catchError, concatMap, defer, finalize, from, Observable, switchMap, throwError } from 'rxjs';
// import { z } from 'zod';

// import { AIChatService } from './ai-chat-session.service';
// import { CompletionOptions } from './define';

// interface MutiStepCompletionOptions extends CompletionOptions {
//   maxSteps?: number;
// }
// interface SingleStepParams extends MutiStepCompletionOptions {
//   currentStep: number;
//   maxSteps: number;
// }

// @Injectable()
// export class AIChatMultiStepService {
//   constructor(private readonly aiChatService: AIChatService) {}

//   private readonly logger = new Logger(AIChatMultiStepService.name);

//   /**
//    * 多步工具调用的流式聊天完成
//    * 通过循环调用 AIChatService.completion 方法实现多步工具调用
//    * @returns Observable 流式响应
//    */
//   async completionMutiSteps(options: MutiStepCompletionOptions): Promise<Observable<string>> {
//     const { maxSteps = Infinity } = options;

//     return this.executeMultiStepLoop({
//       ...options,
//       maxSteps,
//       currentStep: 1,
//       tools: {
//         weather: tool({
//           description: 'Get the weather in a location',
//           parameters: z.object({
//             location: z.string().describe('The location to get the weather for'),
//           }),
//           execute: async ({ location }) => ({
//             location,
//             temperature: 72 + Math.floor(Math.random() * 21) - 10,
//           }),
//         }),
//         add: tool({
//           description: 'Add two numbers',
//           parameters: z.object({
//             a: z.number().describe('The first number'),
//             b: z.number().describe('The second number'),
//           }),
//           execute: async ({ a, b }) => ({
//             result: a + b,
//           }),
//         }),
//       },
//     });
//   }

//   private executeMultiStepLoop(params: SingleStepParams): Observable<string> {
//     const { sessionId, modelId, currentStep, maxSteps } = params;

//     this.logger.log(`执行第 ${currentStep} 步调用`, { sessionId, currentStep, maxSteps });

//     const completionOptions: CompletionOptions = {
//       ...params,
//     };

//     return defer(() => this.aiChatService.completion(completionOptions)).pipe(
//       switchMap((stream) => this.processStepResult(stream, params)),
//       catchError((error) => {
//         this.logger.error(`多步调用第 ${currentStep} 步失败: ${error.message}`, {
//           sessionId,
//           modelId,
//           currentStep,
//           stack: error.stack,
//         });
//         return throwError(() => error);
//       }),
//     );
//   }

//   /**
//    * 处理单步调用结果，判断是否需要继续下一步
//    */
//   private processStepResult(stream: Observable<string>, params: SingleStepParams): Observable<string> {
//     let finishReason = '';
//     let isCompleted = false;

//     return stream.pipe(
//       concatMap((jsonChunk) => {
//         const chunk: StreamChunk = JSON.parse(jsonChunk);
//         switch (chunk.type) {
//           case '[DONE]': {
//             finishReason = chunk.metadata.finishReason;
//             isCompleted = true;

//             // 检查是否需要启动下一步
//             if (finishReason === 'tool-calls' && params.currentStep < params.maxSteps) {
//               // 不返回[DONE] chunk，直接启动下一步
//               return this.executeMultiStepLoop({
//                 ...params,
//                 currentStep: params.currentStep + 1,
//               });
//             }
//             break;
//           }
//         }
//         return from([jsonChunk]);
//       }),
//       finalize(() => {
//         if (isCompleted) {
//           if (finishReason === 'tool-calls' && params.currentStep >= params.maxSteps) {
//             this.logger.warn(`多步调用达到最大步数限制`, {
//               sessionId: params.sessionId,
//               maxSteps: params.maxSteps,
//             });
//           } else {
//             this.logger.log(`多步调用完成`, {
//               sessionId: params.sessionId,
//               totalSteps: params.currentStep,
//               finishReason,
//             });
//           }
//         }
//       }),
//     );
//   }
// }
