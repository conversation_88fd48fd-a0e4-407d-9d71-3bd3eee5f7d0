import { AIModelId, AIPlatform } from '@modules/ai-base/define';
import { StreamTextTransform, Tool, ToolChoice, ToolSet } from 'ai';

/**
 * 聊天对话选项
 */
export type CompletionOptions = {
  /**
   * 对话 ID
   */
  sessionId: string;
  /**
   * 平台
   */
  platform?: AIPlatform;
  /**
   * 模型编号
   */
  modelId: AIModelId;
  /**
   * 提示词
   */
  prompt: string;
  /**
   * 系统提示词
   */
  system?: string;
  /**
   * 最大令牌数限制
   */
  maxTokens?: number;
  /**
   * 是否启用深度思考
   */
  thinkingEnabled?: boolean;
  /**
   * 流式转换
   */
  experimental_transform?: StreamTextTransform<ToolSet>;
  /**
   * 初始化的标题
   */
  initialTitle?: string;
  /**
   * 工具
   */
  tools?: Record<string, Tool>;
  /**
   * 工具选择策略
   */
  toolChoice?: ToolChoice<ToolSet>;
  /**
   * 执行的最大步数，默认 1
   */
  maxSteps?: number;
  /**
   * 请求 ID
   */
  requestId?: string;
};

/**
 * 重新生成聊天对话选项
 */
export type RegenerateOptions = Omit<CompletionOptions, 'prompt' | 'initialTitle'> & {
  /**
   * 消息编号
   */
  messageId: string;
  /**
   * 提示词
   */
  prompt?: string;
};

/**
 * 流式生成选项
 */
export type StreamOptions = {
  /**
   * 对话 ID
   */
  sessionId: string;
  /**
   * 模型编号
   */
  modelId: AIModelId;
  /**
   * 用户编号
   */
  userId: string;
  /**
   *  上级消息编号
   */
  parentId: string;
  /**
   * 是否启用深度思考
   */
  thinkingEnabled: boolean;
  /**
   * 错误消息
   */
  errorMsg: string;
  /**
   * 请求 ID
   */
  requestId?: string;
};

/**
 * 消息状态
 */
export enum MessageStatus {
  INCOMPLETE = 0,
  COMPLETE = 1,
  ERROR = 2,
}

/**
 * 消息工具调用状态
 */
export enum MessageToolCallStatus {
  INCOMPLETE = 0,
  COMPLETE = 1,
  ERROR = 2,
}
