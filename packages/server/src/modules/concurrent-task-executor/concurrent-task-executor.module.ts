import { RedisModule } from '@modules/redis/redis.module';
import { Module } from '@nestjs/common';

import { ConcurrentTaskExecutorService } from './concurrent-task-executor.service';

/**
 * 并发任务执行器
 * 用于并发执行有并发限制的一系列任务，保证任务的执行顺序
 */
@Module({
  imports: [RedisModule],
  providers: [ConcurrentTaskExecutorService],
  exports: [ConcurrentTaskExecutorService],
})
export class ConcurrentTaskExecutorModule {}
