import { RedisService } from '@modules/redis/redis.service';
import { Injectable } from '@nestjs/common';

export interface TaskExecutorOptions {
  /**
   * Redis键前缀，用于区分不同的任务类型
   */
  keyPrefix?: string;
  /**
   * 任务超时时间（毫秒），防止死锁
   */
  taskTimeout?: number;
}

const TIMEOUT_SIGNAL = Symbol('timeout');

export interface TaskResult<T> {
  success: boolean;
  result?: T;
  error?: string;
  index: number;
}

@Injectable()
export class ConcurrentTaskExecutorService {
  constructor(private readonly redisService: RedisService) {}

  /**
   * 执行并发控制的任务
   * @param tasks 任务数组，这里实际是任务的入参
   * @param taskExecutor 任务执行函数
   * @param concurrentLimit 并发限制，不传则无限制
   * @param options
   * @returns 返回与输入顺序一致的结果数组
   */
  async executeTasks<TInput, TOutput>(
    tasks: TInput[],
    taskExecutor: (arg: TInput, index: number) => Promise<TOutput>,
    concurrentLimit?: number,
    options: TaskExecutorOptions = {},
  ): Promise<TaskResult<TOutput>[]> {
    if (tasks.length === 0) return [];

    const {
      keyPrefix = 'concurrent_task_default_key',
      taskTimeout = 60000, // 1分钟默认超时
    } = options;

    if (!concurrentLimit) {
      return await this.executeWithoutLimit(tasks, taskExecutor);
    }

    return await this.executeWithRedisLimit(tasks, taskExecutor, concurrentLimit, keyPrefix, taskTimeout);
  }

  /**
   * 无并发限制的执行
   */
  private async executeWithoutLimit<TInput, TOutput>(
    tasks: TInput[],
    taskExecutor: (arg: TInput, index: number) => Promise<TOutput>,
  ): Promise<TaskResult<TOutput>[]> {
    const results = await Promise.allSettled(
      tasks.map(async (taskArg, index) => {
        try {
          const result = await taskExecutor(taskArg, index);
          return { success: true, result, index } as TaskResult<TOutput>;
        } catch (error) {
          return {
            success: false,
            error: error.message || String(error),
            index,
          } as TaskResult<TOutput>;
        }
      }),
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          error: result.reason?.message || String(result.reason),
          index,
        };
      }
    });
  }

  /**
   * 使用Redis控制分布式并发限制的执行
   */
  private async executeWithRedisLimit<TInput, TOutput>(
    tasks: TInput[],
    taskExecutor: (arg: TInput, index: number) => Promise<TOutput>,
    concurrentLimit: number,
    keyPrefix: string,
    taskTimeout: number,
  ): Promise<TaskResult<TOutput>[]> {
    return new Promise(async (resolve) => {
      let completedCount = 0;
      const concurrentKey = `${keyPrefix}:concurrent_count`;
      const results: TaskResult<TOutput>[] = new Array(tasks.length);

      const taskQueue = tasks.map((arg, index) => ({ arg, index }));

      // 执行单个任务的函数，处理了错误，processQueue不需要处理错误
      const executeTask = async (taskItem: { arg: TInput; index: number }): Promise<TaskResult<TOutput>> => {
        try {
          const result = await Promise.race([
            taskExecutor(taskItem.arg, taskItem.index),
            this.createTimeoutPromise(taskTimeout),
          ]);

          if (result === TIMEOUT_SIGNAL) {
            return {
              success: false,
              error: '任务执行超时',
              index: taskItem.index,
            };
          }

          return {
            success: true,
            result: result as TOutput,
            index: taskItem.index,
          };
        } catch (error) {
          return {
            success: false,
            error: error.message || String(error),
            index: taskItem.index,
          };
        }
      };

      this.redisService.subscribe(concurrentKey, () => {
        setImmediate(() => processQueue());
      });

      const checkAllTasksCompleted = async () => {
        if (completedCount >= tasks.length) {
          this.redisService.unsubscribe(concurrentKey);
          resolve(results);
        }
      };

      const processQueue = async () => {
        await checkAllTasksCompleted();

        // 没有待执行任务，等待
        if (taskQueue.length <= 0) {
          return;
        }

        while (taskQueue.length > 0) {
          const acquired = await this.tryAcquirePermission(concurrentKey, concurrentLimit);
          if (!acquired) {
            break;
          }

          const taskItem = taskQueue.shift();
          if (!taskItem) {
            // 如果没有任务了，释放刚获取的权限
            await this.decrCurrentCount(concurrentKey);
            break;
          }

          executeTask(taskItem)
            .then(async (res) => {
              results[taskItem.index] = res;
            })
            .finally(async () => {
              completedCount++;
              // 每次执行完都检查一下，没必要都等到redis事件回调中检查
              await checkAllTasksCompleted();
              this.decrCurrentCount(concurrentKey);
            });
        }
      };

      processQueue();
    });
  }

  /**
   * 使用Lua脚本确保检查和增加操作的原子性，避免竞态
   * @returns 是否获取到执行权限（超出并发限制则获取不到），true表示获取到，false表示没有获取到
   */
  private async tryAcquirePermission(concurrentKey: string, limit: number): Promise<boolean> {
    try {
      // 可以执行返回1，否则返回0
      const luaScript = `
        local current = redis.call('GET', KEYS[1])
        if current == false then
          current = 0
        else
          current = tonumber(current)
        end
        
        if current < tonumber(ARGV[1]) then
          redis.call('INCR', KEYS[1])
          return 1
        else
          return 0
        end
      `;

      const result = await this.redisService.client.eval(luaScript, 1, concurrentKey, limit.toString());
      return result === 1;
    } catch {
      return false;
    }
  }

  private async decrCurrentCount(concurrentKey: string) {
    await this.redisService.decr(concurrentKey);
    await this.redisService.publish(concurrentKey, '');
  }

  private createTimeoutPromise(timeout: number) {
    return new Promise<symbol>((resolve) => {
      setTimeout(() => {
        resolve(TIMEOUT_SIGNAL);
      }, timeout);
    });
  }
}
