import { VDBConfig } from '@config/config.interface';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { CosService } from '@modules/cos/cos.service';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BM25Encoder } from '@tencent/bm25-encoder';
import { stripIndents } from '@tencent/design-ai-utils';
import { firstValueFrom } from 'rxjs';

import { COS_FILE_KEY_PREFIX, UI_TYPE_MAP } from './constant';
import { CollectionDocument, CollectionDocumentWithScore } from './type';
import { getWordTokens, longestCommonTokenSequence, removeStopWords } from './utils';

@Injectable()
export class UIRagService {
  private bm25Encoder = BM25Encoder.default();

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly aiBaseService: AiBaseService,
    private readonly cos: CosService,
  ) {}

  private async vdbHttpApi(url: string, method: 'POST' | 'GET', data: any) {
    const vdbConfig = this.configService.get<VDBConfig>('vdb');
    const response = await this.httpService.request({
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer account=${vdbConfig.VDB_ACCOUNT}&api_key=${vdbConfig.VDB_API_KEY}`,
      },
      url: `${vdbConfig.VDB_HOST}${url}`,
      method,
      data: {
        database: 'ui',
        collection: 'ui_image',
        ...data,
      },
    });

    return firstValueFrom(response);
  }

  public async insertDocuments(docs: CollectionDocument[]) {
    try {
      const res = await this.vdbHttpApi('/document/upsert', 'POST', {
        documents: docs,
      });

      const { data } = res;
      if (data.code === 0) {
        return {
          success: true,
          message: data.msg,
          affectedCount: data.affectedCount,
          embeddingExtraInfo: data.embeddingExtraInfo,
        };
      }

      throw new Error(data.msg);
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  private async summarizeQuery(query: string): Promise<{
    type: string | null;
    summaryQuery: string;
    query: string;
  }> {
    const { platform, modelId } = this.aiBaseService.getModelInfoWithEnv('claude-3-5');
    const [summaryRes, typeRes] = await Promise.allSettled([
      this.aiBaseService.chatTextV2(
        { platform, modelId },
        {
          messages: [
            {
              role: 'user',
              content: stripIndents`
请根据用户的设计需求，帮我用一句话描述用户想要的页面，删掉所有细节，只保留页面的区域分布、应用领域以及功能，控制在50字以内
用户的设计需求如下：
${query}

## outputs
AAAA页面，包含BBBB区域，提供/支持CCCC功能。

### outputs说明
其中AAAA简单概括页面的抽象功能而不关注具体功能；BBBB介绍主要区域；CCCC介绍细分功能。描述文本中不能包括app名字

## examples
query: 创建一个简约风格的首页,顶部是导航栏,包含logo和菜单项。正中间是一个大型广告区域,左侧是标题文案,右侧是特色图片。下方是三列产品特性介绍,每列包含图标、标题和简短描述。底部是行动按钮。
answer: 首页页面，包含导航栏、广告区域和产品特性展示区，提供品牌展示和产品特性介绍功能。
query: 设计一个个人中心页面,顶部是用户信息卡片,展示头像、昵称和简介。下方分成左右两栏,左侧是导航菜单,右侧是内容区域,默认显示用户动态列表。
answer: 个人中心页面，包含用户信息卡片、导航菜单和内容区域，支持用户信息展示和动态浏览功能。
query: 简洁的设置界面,左侧是分类导航,包含个人资料、账号安全、通知提醒等。右侧是具体设置项,采用表单布局,底部有保存按钮。
answer: 设置页面，包含左侧分类导航和右侧表单区域，提供个人信息及系统配置功能。
          `,
            },
          ],
        },
      ),
      this.aiBaseService.chatTextV2(
        { platform, modelId },
        {
          messages: [
            {
              role: 'user',
              content: stripIndents`
现在有10类UI界面，分别是：购物、社交、健康健美、导航、音乐、财务、工具、教育、生活、娱乐。
请你根据用户的query，判断用户请求的UI界面属于哪一类。
注意只输出类型，不要包含任何其他内容。
如果你无法判断类别，请输出"None"。

### examples
query: 设计一个全屏播放页面，中央是专辑封面旋转效果，底部是进度条和控制按钮，支持歌词同步显示
answer: 音乐
query: 设计一个商品详情页，包含商品图片轮播、价格信息、规格选择、评价区域，突出购买按钮
answer: 购物
query: 设计一个社交平台的信息流页面，包含状态更新、图片分享、互动按钮，支持下拉刷新
answer: 社交
query: 设计一个健身训练计划页面，包含每日任务、进度追踪、数据统计图表，使用活力型配色
answer: 健康健美
query: 设计一个路线规划页面，显示多种出行方案、时间估算、路况提醒，界面清晰易读
answer: 导航
query: 设计一个搜索app的首页，包含搜索框、热门推荐、分类导航，支持语音搜索
answer: 工具
query: 设计一个课程列表页面，包含课程封面、标题、简介、价格信息，支持筛选和排序
answer: 教育

用户的query如下：
${query}
          `,
            },
          ],
        },
      ),
    ]);
    let summaryQuery = '';
    if (summaryRes.status !== 'fulfilled' || summaryRes.value?.finishReason === 'error') {
      summaryQuery = query;
    } else {
      summaryQuery = summaryRes.value.text;
    }

    let type = null;
    if (typeRes.status === 'fulfilled' && typeRes.value?.finishReason !== 'error') {
      type = UI_TYPE_MAP[typeRes.value.text] ?? null;
    }
    return {
      type,
      query,
      summaryQuery,
    };
  }

  private async hybridSearch(query: string, topK: number, type: string | null) {
    const sparseVector = this.bm25Encoder.encodeQueries(query);

    return this.vdbHttpApi('/document/hybridSearch', 'POST', {
      search: {
        ann: [
          {
            fieldName: 'text',
            data: [query],
            params: {
              ef: 200,
            },
            limit: topK * 10,
          },
        ],
        match: [
          {
            fieldName: 'sparse_vector',
            data: [sparseVector],
            limit: topK * 10,
            terminateAfter: 4000,
            cutoffFrequency: 0.1,
          },
        ],
        rerank: {
          method: 'rrf',
        },
        limit: topK * 2,
        filter: type ? `ui_type in ("${type}")` : undefined,
        outputFields: ['text', 'id', 'keywords', 'ui_type'],
      },
    });
  }

  private async getTagRanking(types: string[]) {
    const tagRankings = await Promise.allSettled(
      types.map(async (type) => {
        const buffer = (
          await this.cos.getObject({
            Key: `${COS_FILE_KEY_PREFIX}/${type}/tag_ranking.json`,
          })
        ).Body;
        return {
          type,
          tagRanking: JSON.parse(buffer.toString('utf-8')),
        };
      }),
    );

    const tagRanking = {
      UI_type_content: [],
      UI_component: [],
      stop_words: [],
    };

    for (const tr of tagRankings) {
      if (tr.status === 'fulfilled') {
        tagRanking.UI_type_content.push(...tr.value?.tagRanking?.UI_type_content);
        tagRanking.UI_component.push(...tr.value?.tagRanking?.UI_component);
        tagRanking.stop_words.push(...tr.value?.tagRanking?.stop_words);
      }
    }
    return {
      UI_type_content: [...new Set(tagRanking.UI_type_content)],
      UI_component: [...new Set(tagRanking.UI_component)],
      stop_words: [...new Set(tagRanking.stop_words)],
    };
  }

  public async retrievalImage(originQuery: string, topK: number = 5) {
    try {
      // 1. 对用户输入的query进行摘要
      const { summaryQuery, type } = await this.summarizeQuery(originQuery);

      // 2. 检索
      const res = await this.hybridSearch(summaryQuery, topK, type);
      const { data } = res;
      if (data.code !== 0) {
        throw new Error(data.msg);
      }

      // 3. 对召回的结果进行关键词匹配分析和重排序
      const documents = data.documents?.[0] as CollectionDocumentWithScore[];

      if (!documents || documents.length === 0) {
        throw new Error('No image found');
      }

      const enhancedResults: (CollectionDocumentWithScore & {
        original_score: number;
        matched_keywords: string[];
        score: number;
      })[] = [];

      const allTypes = [...new Set(documents.map((doc) => doc.ui_type))];
      const tagRanking = await this.getTagRanking(allTypes);

      const pureQuery = removeStopWords(originQuery, tagRanking.stop_words);
      const queryTokens = getWordTokens(pureQuery);

      for (const doc of documents) {
        const tagList = doc.keywords || [];
        let originalScore = doc.score;
        const matchedKeywords = [];

        for (const tag of tagList) {
          const pureTag = removeStopWords(tag, tagRanking.stop_words);
          const tagTokens = getWordTokens(pureTag);
          const { matchedTokens } = longestCommonTokenSequence(queryTokens, tagTokens);
          const matchedCharsLength = matchedTokens.reduce((sum, token) => sum + token.length, 0);
          const targetCharsLength = pureTag.length;
          const score = matchedCharsLength / targetCharsLength;

          if (score > 0.5) {
            if (tagRanking.UI_type_content.includes(tag)) {
              originalScore += 0.01;
            } else if (tagRanking.UI_component.includes(tag)) {
              originalScore += 0.005;
            }
            matchedKeywords.push(tag);
          }
        }

        enhancedResults.push({
          ...doc,
          original_score: originalScore,
          matched_keywords: matchedKeywords,
          score: originalScore,
        });
      }

      // 4. 根据最终得分排序
      const sortedResults = enhancedResults.sort((a, b) => b.score - a.score).slice(0, topK);
      const sortedResultsMap = new Map(sortedResults.map((res) => [res.id, res]));

      // 5. 获取图片，构建结果
      const imageResults = sortedResults.map((res) => {
        const fileKey = `${COS_FILE_KEY_PREFIX}/${res.ui_type}/images/${res.id}.png`;
        const signUrl = this.cos.generateCdnSignUrl(fileKey);
        return { url: signUrl, id: res.id };
      });

      const finalData: {
        url: string | null;
        id: CollectionDocumentWithScore['id'];
        ui_type: CollectionDocumentWithScore['ui_type'];
        score: number;
      }[] = [];

      for (const res of imageResults) {
        const doc = sortedResultsMap.get(res.id);
        finalData.push({
          url: res.url,
          id: doc.id,
          ui_type: doc.ui_type,
          score: doc.score,
        });
      }

      return {
        success: true,
        data: finalData,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  private async internal_createCollection() {
    return this.vdbHttpApi('/collection/create', 'POST', {
      database: 'ui',
      collection: 'ui_image',
      replicaNum: 1,
      shardNum: 1,
      description: '用于 UI RAG',
      embedding: {
        field: 'text',
        vectorField: 'vector',
        model: 'BAAI/bge-m3',
      },
      indexes: [
        {
          fieldName: 'id',
          fieldType: 'string',
          indexType: 'primaryKey',
        },
        {
          fieldName: 'app_name',
          fieldType: 'string',
          indexType: 'filter',
        },
        {
          fieldName: 'text',
          fieldType: 'string',
          indexType: 'filter',
        },
        {
          fieldName: 'ui_type',
          fieldType: 'string',
          indexType: 'filter',
        },
        {
          fieldName: 'keywords',
          fieldType: 'array',
          indexType: 'filter',
        },
        {
          fieldName: 'vector',
          fieldType: 'vector',
          indexType: 'HNSW',
          metricType: 'COSINE',
          params: {
            M: 16,
            efConstruction: 200,
          },
        },
        {
          fieldName: 'sparse_vector',
          fieldType: 'sparseVector',
          indexType: 'inverted',
          metricType: 'IP',
        },
      ],
    });
  }
}
