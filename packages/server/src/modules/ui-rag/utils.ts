import * as jieba from 'nodejieba';

/**
 * 移除停用词
 * @param text 文本
 * @param stopWords 停用词列表
 * @returns 移除停用词后的文本
 */
export const removeStopWords = (text: string, stopWords: string[]) => {
  for (const stopWord of stopWords) {
    text = text.replace(stopWord, '');
  }
  return text;
};

/**
 * 获取分词后的词元数组
 * @param text 文本
 * @returns 词元数组
 */
export const getWordTokens = (text: string) => {
  return jieba.cut(text, false);
};

/**
 * 计算最长公共词元序列
 * @param tokens1
 * @param tokens2
 * @returns
 */
export function longestCommonTokenSequence(
  tokens1: string[],
  tokens2: string[],
): { maxLength: number; matchedTokens: string[] } {
  const m = tokens1.length;
  const n = tokens2.length;

  const dp = Array(m + 1)
    .fill(null)
    .map(() => Array(n + 1).fill(0));

  let maxLength = 0;
  let matchedTokens = [];

  // 记录最长匹配位置
  let endPos = 0;

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (tokens1[i - 1] === tokens2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1;
        if (dp[i][j] > maxLength) {
          maxLength = dp[i][j];
          endPos = i;
        }
      }
    }
  }

  // 回溯获取匹配的词元
  if (maxLength > 0) {
    const startPos = endPos - maxLength;
    matchedTokens = tokens1.slice(startPos, endPos);
  }

  return { maxLength, matchedTokens };
}
