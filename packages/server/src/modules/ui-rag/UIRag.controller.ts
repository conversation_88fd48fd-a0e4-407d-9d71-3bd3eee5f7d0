import { Body, Controller, Post } from '@nestjs/common';

import { InsertDto } from './dto/insert.dto';
import { SearchDto } from './dto/search.dto';
import { UIRagService } from './UIRag.service';

@Controller('ui-rag')
export class UIRagController {
  constructor(private readonly uiRagService: UIRagService) {}

  @Post('search')
  async search(@Body() body: SearchDto) {
    return this.uiRagService.retrievalImage(body.query, body.topK);
  }

  @Post('insert')
  async insert(@Body() body: InsertDto) {
    return this.uiRagService.insertDocuments(body.documents);
  }
}
