import { AiBaseModule } from '@modules/ai-base/ai-base.module';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { HttpModule } from '@nestjs/axios';
import { Logger, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AIPromptLibraryController } from './ai-prompt-library.controller';
import { AIPromptLibraryService } from './ai-prompt-library.service';

@Module({
  imports: [ConfigModule, HttpModule, PrismaModule, AiBaseModule],
  providers: [Logger, AIPromptLibraryService, AiBaseService],
  controllers: [AIPromptLibraryController],
})
export class AIPromptLibraryModule {}
