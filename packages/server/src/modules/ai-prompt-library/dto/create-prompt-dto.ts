import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber } from 'class-validator';

export class CreatePromptDto {
  @ApiProperty({
    description: '名称',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'name is required' })
  public name: string;

  @ApiProperty({
    description: '详情内容',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'content is required' })
  public content: string;

  @ApiProperty({
    description: '用户编号',
    type: Number,
    required: true,
  })
  @IsNotEmpty({ message: 'userId is required' })
  @IsNumber()
  public userId: number;
}
