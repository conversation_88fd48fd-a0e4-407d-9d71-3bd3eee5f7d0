import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber } from 'class-validator';

export class CopyPromptDto {
  @ApiProperty({
    description: '提示词编号',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'id is required' })
  public id: string;

  @ApiProperty({
    description: '用户编号',
    type: Number,
    required: true,
  })
  @IsNotEmpty({ message: 'userId is required' })
  @IsNumber()
  public userId: number;
}
