import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class UpdatePromptDto {
  @ApiProperty({
    description: '提示词编号',
    type: Number,
    required: true,
  })
  @IsNotEmpty({ message: 'id is required' })
  public id: string;

  @ApiProperty({
    description: '名称',
    type: String,
    required: true,
  })
  public name: string;

  @ApiProperty({
    description: '详情内容',
    type: String,
    required: true,
  })
  public content: string;

  @ApiProperty({
    description: '用户编号',
    type: Number,
    required: true,
  })
  @IsNotEmpty({ message: 'userId is required' })
  @IsNumber()
  public userId: number = 0;
}
