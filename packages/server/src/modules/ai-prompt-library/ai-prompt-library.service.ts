/**
 * AI提示词库服务类
 * 提供提示词的增删改查等基础操作
 *
 * @class AIPromptLibraryService
 */
import { createIdGenerator } from '@ai-sdk/provider-utils';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import {
  GatewayTimeoutException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { IPager } from '@shared/decorator/list-params.decorator';
import { templateReplace } from '@utils/tmpl';
import { fromPairs, map as lodashMap } from 'lodash';
import { catchError, defer, forkJoin, from, map, retry, switchMap, throwError, TimeoutError } from 'rxjs';

import { UpdatePromptDto } from './dto/update-prompt-dto';
import { OPTIMIZE_PROMPT_TEMPLATE } from './helper/prompt';

@Injectable()
export class AIPromptLibraryService {
  private readonly logger = new Logger(AIPromptLibraryService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiBaseService: AiBaseService,
  ) {}

  create(data: Partial<Prisma.ai_prompt_userCreateInput>) {
    const recordId = createIdGenerator({ size: 36 })();

    return from(
      this.prisma.ai_prompt_user.create({
        select: { id: true, name: true, content: true },
        data: {
          id: recordId,
          ...data,
          status: 0,
        },
      }),
    ).pipe(
      catchError((error) => {
        this.logger.error(`创建失败: ${error.message}`);
        return throwError(() => new InternalServerErrorException('数据库操作失败'));
      }),
    );
  }

  update(data: UpdatePromptDto) {
    const { id, name, content } = data;
    return from(
      this.prisma.ai_prompt_user.update({
        select: { id: true, name: true, content: true },
        where: { id },
        data: {
          name,
          content,
          updated_at: new Date().toISOString(),
        },
      }),
    ).pipe(
      catchError((error) => {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === 'P2025') {
            this.logger.warn(`更新失败: 未找到ID为 ${data.id} 的记录`);
            return throwError(() => new NotFoundException('指定提示词不存在'));
          }
        }

        this.logger.error(`更新异常: ${error.stack}`);
        return throwError(() => new InternalServerErrorException('更新服务不可用'));
      }),
    );
  }

  delete(ids: string[]) {
    return from(
      this.prisma.ai_prompt_user.updateMany({
        where: { id: { in: ids } },
        data: {
          status: 1,
        },
      }),
    ).pipe(
      catchError((error) => {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === 'P2025') {
            this.logger.warn(`删除失败: 未找到ID为 ${ids} 的记录`);
            return throwError(() => new NotFoundException('要删除的记录不存在'));
          }
        }
        this.logger.error(`删除失败: ${error.message}`);
        return throwError(() => new InternalServerErrorException('数据库操作失败'));
      }),
    );
  }

  deepDelete(ids: string[]) {
    return from(
      this.prisma.ai_prompt_user.deleteMany({
        where: { id: { in: ids } },
      }),
    ).pipe(
      catchError((error) => {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === 'P2025') {
            this.logger.warn(`删除失败: 未找到ID为 ${ids} 的记录`);
            return throwError(() => new NotFoundException('要删除的记录不存在'));
          }
        }
        this.logger.error(`删除失败: ${error.message}`);
        return throwError(() => new InternalServerErrorException('数据库操作失败'));
      }),
    );
  }

  copy(id: string, userId: number) {
    return from(
      this.prisma.ai_prompt_user.findUnique({
        where: { id, type: 1 },
        select: { id: true, name: true, content: true },
      }),
    ).pipe(
      switchMap((publicPrompt) => {
        if (!publicPrompt) {
          this.logger.warn(`复制失败: 未找到ID为 ${id} 的公共提示词`);
          return throwError(() => new NotFoundException('公共提示词不存在'));
        }

        const newId = createIdGenerator({ size: 36 })();
        return from(
          this.prisma.ai_prompt_user.create({
            select: { id: true, name: true, content: true },
            data: {
              ...publicPrompt,
              id: newId,
              type: 0,
              created_user: userId,
              prompt_id: publicPrompt.id,
              status: 0,
            },
          }),
        );
      }),
      catchError((error) => {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          // 处理唯一约束冲突
          if (error.code === 'P2002') {
            this.logger.error(`唯一键冲突: ${error.meta?.target}`);
            return throwError(() => new InternalServerErrorException('创建失败，请重试'));
          }
          // 处理其他已知 Prisma 错误
          this.logger.error(`数据库操作异常: ${error.message}`);
          return throwError(() => new InternalServerErrorException('数据库操作失败'));
        }

        // 处理查询阶段的未知错误
        this.logger.error(`复制过程中发生错误: ${error.message}`);
        return throwError(() => new InternalServerErrorException('复制服务不可用'));
      }),
    );
  }

  findSharedByPage({ pageNum, pageSize, order }: IPager, type: number) {
    const where: Prisma.ai_prompt_userWhereInput = {
      type,
      status: 0,
    };

    // 创建查询列表的 Observable
    const list$ = from(
      this.prisma.ai_prompt_user.findMany({
        select: { id: true, name: true, content: true },
        skip: (pageNum - 1) * pageSize,
        take: pageSize,
        where,
        orderBy: lodashMap(order, (it) => fromPairs([it])),
      }),
    );

    // 创建查询总数的 Observable
    const count$ = from(this.prisma.ai_prompt_user.count({ where }));

    // 使用 forkJoin 并行执行查询
    return forkJoin([list$, count$]).pipe(map(([list, count]) => ({ list, count })));
  }

  findByPage({ pageNum, pageSize, order }: IPager, userId: number) {
    const where: Prisma.ai_prompt_userWhereInput = {
      type: 0,
      created_user: userId,
      status: 0,
    };

    // 创建查询列表的 Observable
    const list$ = from(
      this.prisma.ai_prompt_user.findMany({
        select: { id: true, name: true, content: true },
        skip: (pageNum - 1) * pageSize,
        take: pageSize,
        where,
        orderBy: lodashMap(order, (it) => fromPairs([it])),
      }),
    );

    // 创建查询总数的 Observable
    const count$ = from(this.prisma.ai_prompt_user.count({ where }));

    // 使用 forkJoin 并行执行查询
    return forkJoin([list$, count$]).pipe(map(([list, count]) => ({ list, count })));
  }

  optimize(text: string, userId: number, requestId: string) {
    const content = templateReplace(OPTIMIZE_PROMPT_TEMPLATE, text);

    return defer(() =>
      this.aiBaseService.chatTextV2(
        {
          platform: 'venus',
          modelId: 'claude-3-7-sonnet-20250219',
          userId: String(userId),
          requestId,
        },
        { messages: [{ role: 'user', content }], maxTokens: 12000 },
      ),
    ).pipe(
      map((result) => result.text),
      catchError((error) => {
        this.logger.error(`AI 提示词优化服务调用失败: ${error.message}`, {
          userId,
          requestId,
          stack: error.stack,
        });
        return throwError(() =>
          error instanceof TimeoutError
            ? new GatewayTimeoutException('优化服务响应超时')
            : new InternalServerErrorException('AI 优化服务异常'),
        );
      }),
      retry(2), // 失败后自动重试 2 次
    );
  }
}
