import { Body, Controller, Get, InternalServerErrorException, Logger, Post, Query, Request } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiOperation, ApiQuery } from '@nestjs/swagger';
import { IPager, PagerParams } from '@shared/decorator/list-params.decorator';
import { catchError, throwError } from 'rxjs';

import { AIPromptLibraryService } from './ai-prompt-library.service';
import { CopyPromptDto } from './dto/copy-prompt-dto';
import { CreatePromptDto } from './dto/create-prompt-dto';
import { DeletePromptDto } from './dto/delete-prompt-dto';
import { UpdatePromptDto } from './dto/update-prompt-dto';

@Controller('prompt-library')
export class AIPromptLibraryController {
  constructor(
    private configService: ConfigService,
    private readonly aiPromptService: AIPromptLibraryService,
  ) {}

  private readonly logger = new Logger(AIPromptLibraryController.name);

  @ApiOperation({
    tags: ['提示词库管理'],
    description: '创建用户提示词',
  })
  @Post('/create')
  create(@Body() dto: CreatePromptDto) {
    const { name, content, userId } = dto;
    return this.aiPromptService.create({
      name,
      content,
      created_user: userId,
      type: 0,
    });
  }

  @ApiOperation({
    tags: ['提示词库管理'],
    description: '创建公共提示词',
  })
  @Post('/create/shared')
  createShared(@Body() dto: CreatePromptDto) {
    const { name, content, userId } = dto;
    return this.aiPromptService.create({
      name,
      content,
      created_user: userId,
      type: 1,
    });
  }

  @ApiOperation({
    tags: ['提示词库管理'],
    description: '更新用户提示词',
  })
  @Post('/update')
  update(@Body() dto: UpdatePromptDto) {
    return this.aiPromptService.update(dto);
  }

  @ApiOperation({
    tags: ['提示词库管理'],
    description: '删除用户提示词',
  })
  @Post('/delete')
  delete(@Body() dto: DeletePromptDto) {
    return this.aiPromptService.delete(dto.ids);
  }

  @Post('/delete/deep')
  deepDelete(@Body() dto: DeletePromptDto) {
    return this.aiPromptService.deepDelete(dto.ids);
  }

  @ApiOperation({
    tags: ['提示词库管理'],
    description: '复制公共提示词',
  })
  @Post('/copy')
  copy(@Body() dto: CopyPromptDto) {
    return this.aiPromptService.copy(dto.id, dto.userId);
  }

  @ApiOperation({
    tags: ['提示词库管理'],
    description: '分页查询公共提示词',
  })
  @ApiQuery({ name: 'pageNum', description: '分页页码', required: false })
  @ApiQuery({
    name: 'pageSize',
    description: '分页每页记录数',
    required: false,
  })
  @ApiQuery({ name: 'orderBy', description: '排序字段', required: false })
  @ApiQuery({
    name: 'order',
    description: '排序方式,可选值【desc、asc】',
    required: false,
  })
  @Get('find/shared')
  async findSharedByPage(
    @PagerParams({
      // 默认排序
      defaultOrder: [['created_at', 'asc']],
    })
    pager: IPager,
  ) {
    return this.aiPromptService.findSharedByPage(pager, 1);
  }

  @ApiOperation({
    tags: ['提示词库管理'],
    description: '分页查询用户提示词',
  })
  @ApiQuery({ name: 'pageNum', description: '分页页码', required: false })
  @ApiQuery({
    name: 'pageSize',
    description: '分页每页记录数',
    required: false,
  })
  @ApiQuery({ name: 'orderBy', description: '排序字段', required: false })
  @ApiQuery({
    name: 'order',
    description: '排序方式,可选值【desc、asc】',
    required: false,
  })
  @Get('find/user')
  findByPage(
    @PagerParams({
      // 默认排序
      defaultOrder: [['created_at', 'asc']],
    })
    pager: IPager,
    @Query('userId') userId: number,
  ) {
    return this.aiPromptService.findByPage(pager, userId);
  }

  @ApiOperation({
    tags: ['提示词库管理'],
    description: '优化提示词库',
  })
  @Post('/optimize')
  optimize(@Body() dto: CreatePromptDto, @Request() req) {
    const requestId = req.requestId;

    return this.aiPromptService.optimize(dto.content, dto.userId, requestId).pipe(
      catchError((error) => {
        this.logger.error(`提示词优化失败: ${error.message}`, {
          userId: dto.userId,
          requestId,
          stack: error.stack,
        });

        return throwError(() => new InternalServerErrorException('提示词优化服务暂不可用'));
      }),
    );
  }
}
