export interface Block {
  nodeId: string; // 添加nodeId字段，用于精确定位
  parentNodeId: string; // 添加parentNodeId字段，用于精确定位
  originalContent: string;
  nodeType: string; // 添加替换级别
  description?: string;
  action?: 'add' | 'remove' | 'modify'; // 添加操作类型
  actionType?: 'property' | 'content'; // 添加修改类型
  posIndex?: number; // 添加修改位置
}

export interface ApplyBlock extends Block {
  newContent: string;
}

export interface OptimizeTaskMessage {
  taskId: string;
  status: 'pending' | 'running' | 'success' | 'error';
  total: number;
  completed: number;
  error?: string;
  data?: ApplyBlock;
}
