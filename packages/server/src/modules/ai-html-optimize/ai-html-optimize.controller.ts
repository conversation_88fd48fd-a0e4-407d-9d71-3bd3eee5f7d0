import { Body, Controller, Logger, Post, Req } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiBody, ApiOperation } from '@nestjs/swagger';

import { AiHtmlOptimizeService } from './ai-html-optimize.service';
import { OptimizeDto } from './dto/optimize.dto';
import { minimizeHtml, minimizePrompt } from './utils';
import { ApplyBlock, OptimizeTaskMessage } from './types';
import { SocketService } from '@modules/socket/socket.service';
import { from, last, mergeMap, scan, tap } from 'rxjs';

@Controller('html-optimize')
export class AiHtmlOptimizeController {
  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
    private readonly aiHtmlOptimizeService: AiHtmlOptimizeService,
    private readonly socketService: SocketService,
  ) {}

  @ApiOperation({ description: '优化 HTML 内容' })
  @ApiBody({
    description: '优化任务对象',
    required: true,
    isArray: false,
    type: OptimizeDto,
  })
  @Post('optimize')
  async optimize(@Body() dto: OptimizeDto) {
    this.logger.log('Received HTML content for optimization');
    const res = await this.aiHtmlOptimizeService.optimize(dto);
    return res;
  }

  @ApiOperation({ description: '优化 HTML 内容（链式调用）' })
  @ApiBody({
    description: '优化任务对象',
    required: true,
    isArray: false,
    type: OptimizeDto,
  })
  @Post('optimize-chain')
  async optimizeChain(@Body() dto: OptimizeDto) {
    this.logger.log('Received HTML content for optimization chain');

    const htmlStr = minimizeHtml(dto.htmlStr);
    const prompt = minimizePrompt(dto.prompt);

    const { designSystem, blocks } = await this.aiHtmlOptimizeService.analyzeAndIdentifyBlocks(htmlStr, prompt);

    const applyBlocks: ApplyBlock[] = await Promise.all(
      blocks.map(async (block) => {
        const newContent = await this.aiHtmlOptimizeService.applyModification(
          block.originalContent,
          block.description,
          designSystem,
        );
        return {
          ...block,
          newContent,
        };
      }),
    );
    const res = await this.aiHtmlOptimizeService.mergeBlocks(htmlStr, applyBlocks);
    return res;
  }

  // task
  @Post('task')
  @ApiOperation({ description: '优化 HTML 内容（任务）' })
  @ApiBody({
    description: '优化任务对象',
    required: true,
    isArray: false,
    type: OptimizeDto,
  })
  async task(@Req() req: Request, @Body() dto: OptimizeDto) {
    const requestId = (req as any)?.requestId;
    this.taskRun(requestId, dto);
    return requestId;
  }

  private async taskRun(taskId, dto: OptimizeDto) {
    const htmlStr = minimizeHtml(dto.htmlStr);
    const prompt = minimizePrompt(dto.prompt);
    const connectionId = dto.connectionId;

    const { designSystem, blocks } = await this.aiHtmlOptimizeService.analyzeAndIdentifyBlocks(htmlStr, prompt);

    const message: OptimizeTaskMessage = {
      taskId,
      total: blocks.length,
      completed: 0,
      status: 'running',
    };

    if (!blocks.length) {
      this.socketService.triggerEventJSON({
        ...message,
        status: 'error',
        error: `无法根据${prompt} 提取修改区块`,
      });
      return;
    }

    from(blocks)
      .pipe(
        mergeMap(async (block) => {
          const newContent = await this.aiHtmlOptimizeService.applyModification(
            block.originalContent,
            block.description,
            designSystem,
          );

          return {
            ...block,
            newContent,
          };
        }),
        scan<ApplyBlock, { completed: number; results: ApplyBlock[] }>(
          (acc, current) => ({
            completed: acc.completed + 1,
            results: [...acc.results, current],
          }),
          { completed: 0, results: [] },
        ),
        tap(({ completed, results }) => {
          this.socketService.triggerEventJSON({
            ...message,
            completed,
            data: results[results.length - 1],
          });
        }),
        last(),
      )
      .subscribe({
        next: ({ results }) => {
          this.socketService.triggerEventJSON({
            ...message,
            completed: blocks.length,
            status: 'success',
            data: results,
          });
        },
        error: (error) => {
          this.socketService.triggerEventJSON({
            ...message,
            status: 'error',
            error: error.message,
          });
        },
      });
  }
}
