import 'jest';
import { minimizeHtml, minimizePrompt } from './prompt-mini';

describe('minimizeHtml', () => {
  test('should remove leading and trailing newlines', () => {
    const input = `\n\nHello World\n\n`;
    const expected = 'Hello World';
    expect(minimizeHtml(input)).toBe(expected);
  });
  test('should replace multiple newlines with a single newline', () => {
    const input = `Hello\n\n\nWorld`;
    const expected = 'Hello\nWorld';
    expect(minimizeHtml(input)).toBe(expected);
  });
  test('should replace multiple spaces with a single space', () => {
    const input = `Hello     World`;
    const expected = 'Hello World';
    expect(minimizeHtml(input)).toBe(expected);
  });

  test('should remove newlines with spaces', () => {
    const input = ` \n \n \n Hello \n \n \n World`;
    const expected = '\nHello\nWorld';
    expect(minimizeHtml(input)).toBe(expected);
  });
});

describe('minimizePrompt', () => {
  test('should remove leading and trailing newlines', () => {
    const input = `\n\nHello World\n\n`;
    const expected = 'Hello World';
    expect(minimizePrompt(input)).toBe(expected);
  });
  test('should replace multiple newlines with a single newline', () => {
    const input = `Hello\n\n\nWorld`;
    const expected = 'Hello\nWorld';
    expect(minimizePrompt(input)).toBe(expected);
  });
  test('should remove leading spaces from each line', () => {
    const input = `  Hello\n  World`;
    const expected = 'Hello\nWorld';
    expect(minimizePrompt(input)).toBe(expected);
  });
});
