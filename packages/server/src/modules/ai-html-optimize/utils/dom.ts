import type { Cheerio } from 'cheerio';

/**
 * 在指定索引位置插入元素
 * @param parent 父元素
 * @param index 索引
 * @param htmlStr 插入的元素
 */
export function insertAt(parent: Cheerio<any>, index: number, htmlStr: string) {
  const children = parent.children();

  // 索引越界检查 - 如果索引大于子元素数量，则添加到末尾
  if (index >= children.length) {
    parent.append(htmlStr);
    return;
  }

  // 如果是插入到首位
  if (index === 0) {
    parent.prepend(htmlStr);
    return;
  }

  // 在指定索引位置的元素前插入
  children.eq(index).before(htmlStr);
}
