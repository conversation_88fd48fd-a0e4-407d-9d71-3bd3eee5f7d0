/**
 * 收集字段路径
 * @param  field  路径字段  'a.b.c'  'a.[b].c'
 * @returns
 */
export function collect(data: any, field: string): string[] {
  const segments = field.split('.');
  const paths: string[] = [];

  function traverse(currentData: any, currentPath: string[], index: number) {
    if (index === segments.length) {
      paths.push(currentPath.join('.'));
      return;
    }

    const segment = segments[index];
    const isArraySegment = segment.startsWith('[') && segment.endsWith(']');

    if (isArraySegment) {
      const arrayName = segment.slice(1, -1);
      const array = currentData[arrayName];

      if (Array.isArray(array)) {
        array.forEach((_, i) => {
          traverse(array[i], [...currentPath, `${arrayName}.${i}`], index + 1);
        });
      }
    } else {
      traverse(currentData[segment], [...currentPath, segment], index + 1);
    }
  }

  traverse(data, [], 0);
  return paths;
}
