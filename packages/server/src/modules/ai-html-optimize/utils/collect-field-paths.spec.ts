import 'jest';
import { collect } from './collect-field-paths';

describe('collect', () => {
  test('a.b.c', () => {
    const data = {
      a: {
        b: {
          c: 'c',
        },
      },
    };
    expect(collect(data, 'a.b.c')).toEqual(['a.b.c']);
  });

  test('a.[b].c', () => {
    const data = {
      a: {
        b: [
          {
            c: 'c1',
          },
          {
            c: 'c2',
          },
        ],
      },
    };
    expect(collect(data, 'a.[b].c')).toEqual(['a.b.0.c', 'a.b.1.c']);
  });
});
