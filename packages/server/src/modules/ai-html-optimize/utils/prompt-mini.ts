/**
 * 移除html中的多余的空格、换行
 * @param html The HTML string to be minimized
 */
export function minimizeHtml(html: string): string {
  return html
    .replace(/^[\r\n]+/, '') // 移除开头的换行符
    .replace(/[\r\n]+$/, '') // 移除结尾的换行符
    .replace(/( *[\r\n] *){2,}/g, '\n') // 将中间连续的换行符替换为单个换行符
    .replace(/ +/g, ' '); // 将多个空格替换为一个空格
}

/**
 * 移除提示中的多余的空格、换行,并去除每行的缩进
 * @param prompt The prompt string to be minimized
 */
export function minimizePrompt(prompt: string): string {
  return prompt
    .replace(/^[ \t]+/gm, '') // 去除每行的缩进
    .replace(/^[\r\n]+/, '') // 移除开头的换行符
    .replace(/[\r\n]+$/, '') // 移除结尾的换行符
    .replace(/[\r\n]{2,}/g, '\n'); // 将中间连续的换行符替换为单个换行符
}
