import { Injectable, Logger } from '@nestjs/common';
import { CoreMessage } from 'ai';
import { htmlOptimizePrompt } from './helper/prompt';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { OptimizeDto } from './dto/optimize.dto';
import { minimizeHtml, minimizePrompt, insertAt } from './utils';
import { Storage } from './helper/storage';
import { load as cheerioLoad } from 'cheerio';

import { Block, ApplyBlock } from './types';
import { BASE64_IMG_REGEXP } from './constants';

@Injectable()
export class AiHtmlOptimizeService {
  private readonly logger = new Logger(AiHtmlOptimizeService.name);

  constructor(private readonly aiBaseService: AiBaseService) {}

  @Storage(BASE64_IMG_REGEXP, {
    inFelid: 'htmlStr',
  })
  async optimize(dto: OptimizeDto) {
    const { htmlStr, prompt } = dto;
    const messages: CoreMessage[] = [
      {
        role: 'system',
        content: htmlOptimizePrompt,
      },
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: minimizeHtml(htmlStr),
          },
          {
            type: 'text',
            text: minimizePrompt(prompt),
          },
        ],
      },
    ];
    const res = await this.aiBaseService.chatText('venus', 'claude-3-5-sonnet-20241022', {
      messages,
      temperature: 0.2,
      topK: 20,
      topP: 0.9,
    });
    if (res.finishReason === 'error') return '';

    const codeBlockIndex = res.text.indexOf('```html');
    if (codeBlockIndex !== -1) {
      return res.text.slice(codeBlockIndex + 7, res.text.lastIndexOf('```'));
    }

    return res.text;
  }
  @Storage(BASE64_IMG_REGEXP, {
    outFelid: '[blocks].originalContent',
  })
  async analyzeAndIdentifyBlocks(htmlStr: string, prompt: string): Promise<{ designSystem: any; blocks: Block[] }> {
    const messages: CoreMessage[] = [
      {
        role: 'system',
        content: minimizePrompt(`
          你是Figma HTML分析专家,分析页面布局之后完成两项任务并输出单个JSON:

          【任务1】提取设计系统
            分析HTML内联样式，提取颜色、排版、间距和组件样式规则。

          【任务2】提取需修改区块和修改描述
          【步骤1】布局分析与修改计划
            1. 分析现有DOM结构和布局方式(flex/grid/block)，包括位置属性(position/z-index)和尺寸约束
            2. 确定页面主要区域与布局容器，建立元素间的层级与空间关系模型
            3. 提取用户意图中提到的位置关系词(右侧/左侧/顶部/底部/中间等)

          【步骤2】生成修改区块和修改描述
            所有修改区块必须遵循:
            - 每个区块保留原始HTML及其data属性，不做任何修改和删减
            - 每个区块必须包含完整HTML结构
            - 使用data-node-id作为nodeId标识
            - 使用data-node-type为nodeType
            - action: "add"/"remove"/"modify"
              - action为add时，增加"posIndex"属性，表示在父元素中的位置以0开始
              - action为add时，"nodeId" 和 "originalContent" 为空
            - actionType: "property"/"content"
              - 如果"property"和"content"同时修改，则优先选择"content"
              - action为"add"时，actionType为"content"
            - add操作时需提供posIndex和parentNodeId
            - parentNodeId表示父元素
            - description详细描述该修改如何实现用户意图需包含:
              - 修改目的: 简明陈述此修改解决什么问题或实现什么功能
              - 元素引用: 明确引用用户提及的UI元素[nodeId]
              - 位置关系: 明确说明修改元素与相邻元素[nodeId|parentNodeId]的位置关系
           
          【步骤3】验证与优化
            - 检查复合元素间的关系是否在同一修改块中处理(如卡片间的遮挡、对齐)
            - 确保每个功能性修改都被封装在单个修改块中，而不是分散在多个块中
            - 当无法确定原始HTML结构时，基于上下文做合理假设并在description中说明

          注意：必须返回有效JSON，包含designSystem和blocks两部分
          规则：
            - 严格按照输出格式输出，不需要解释  
            - 所有修改区块不能相互依赖，必须独立完成
            - 同一区域的所有修改(布局容器调整和内容添加)必须合并为一个修改块
            - 禁止将容器添加和内容添加拆分成两个修改区块
            -【任务2】无法理解用户意图时blocks为[]
            - description字段绝不能简化或省略用户原始意图
      `),
      },
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: minimizePrompt(`HTML代码:

            \`\`\`html\n${htmlStr}\n\`\`\`
            修改需求：${prompt}

            无法理解用户意图返回格式：
            \`\`\`json\n{"designSystem":{...},"blocks":[]}\n\`\`\`

            合法返回格式:
            \`\`\`json\n{"designSystem":{...},"blocks":[{"parentNodeId":"xx:xx","nodeId":"xx:xx","originalContent":"<div...>","action":"...","actionType":"...","posIndex":"...","nodeType":"...","description":"..."}]}\n\`\`\`
            `),
          },
        ],
      },
    ];

    const res = await this.aiBaseService.chatText('venus', 'claude-3-5-sonnet-20241022', {
      messages,
      temperature: 0.05,
    });

    if (res.finishReason === 'error') return { designSystem: '', blocks: [] };
    this.logger.log('提取的设计系统和区块:', res.text);
    const jsonMatch = res.text.match(/```json\s*({[\s\S]*?})\s*```/);
    if (jsonMatch) {
      const { designSystem, blocks } = JSON.parse(jsonMatch[1]);
      return { designSystem, blocks };
    }
    const { designSystem, blocks } = JSON.parse(res.text);
    return { designSystem, blocks };
  }

  @Storage(BASE64_IMG_REGEXP)
  async applyModification(htmlStr: string, prompt: string, designSystem: any = '') {
    const messages: CoreMessage[] = [
      {
        role: 'system',
        content:
          minimizePrompt(`你是HTML修改专家。遵循设计系统规范修改或增加HTML代码。只返回修改后的完整HTML代码，不包含任何标记或解释。
        
        ## Profile
          - Language: 默认中文，如果用户使用英文对话，或者用户要求使用英文，请使用英文
        
        #Rules:
        1. 严格按照输出格式输出，不需要解释
        2. 禁止使用注释或解释代替完整的HTML代码
        3. 保留所有data属性
        4. 保留以__prompt-mini__开头的未修改属性或内容
        5. 如果 html 片段为空，根据要求和设计系统生成完整的HTML代码`),
      },
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `HTML片段:\n\`\`\`html\n${htmlStr}\n\`\`\`\n\n修改或新增要求:${prompt}\n\n设计系统:\n\`\`\`json\n${JSON.stringify(designSystem)}\n\`\`\`\n\n只返回修改后的HTML代码。`,
          },
        ],
      },
    ];
    const res = await this.aiBaseService.chatText('venus', 'claude-3-5-sonnet-20241022', {
      messages,
      temperature: 0.1,
    });
    if (res.finishReason === 'error') return '';
    const content = res.text;
    // 提取HTML片段
    const htmlMatch = content.match(/```html\s*([\s\S]*?)\s*```/);
    if (!htmlMatch) {
      // 如果没有HTML标记，则假设整个响应就是代码
      return content.trim();
    }
    return htmlMatch[1];
  }

  async mergeBlocks(htmlStr: string, applyBlocks: ApplyBlock[]) {
    const $ = cheerioLoad(htmlStr, null, false);

    this.logger.log('合并区块:====', applyBlocks);

    applyBlocks.forEach((block) => {
      const { nodeId, newContent, action, actionType, posIndex } = block;
      if (action === 'add') {
        // 如果是添加操作，直接插入新的内容
        const parentNodeId = block.parentNodeId;
        const parentElement = $(`[data-node-id="${parentNodeId}"]`);
        if (parentElement.length) {
          insertAt(parentElement, posIndex, newContent);
        }
        return;
      }
      // 如果是修改或删除操作，找到对应的元素进行替换
      const element = $(`[data-node-id="${nodeId}"]`);

      if (element.length) {
        if (actionType === 'property') {
          const newAttr = cheerioLoad(newContent, null, false)(`[data-node-id="${nodeId}"]`).attr();
          element.attr(newAttr);
          return;
        }
        element.replaceWith(newContent);
      }
    });

    return $.html();
  }
}
