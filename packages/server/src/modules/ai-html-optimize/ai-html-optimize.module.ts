import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { SocketModule } from '@modules/socket/socket.module';
import { SocketService } from '@modules/socket/socket.service';
import { RedisService } from '@modules/redis/redis.service';
import { RedisModule } from '@modules/redis/redis.module';
import { PrismaModule } from '@modules/prisma/prisma.module';

import { AiBaseModule } from '@modules/ai-base/ai-base.module';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { AiHtmlOptimizeService } from './ai-html-optimize.service';
import { AiHtmlOptimizeController } from './ai-html-optimize.controller';

@Module({
  imports: [ConfigModule, AiBaseModule, SocketModule, RedisModule, PrismaModule],
  providers: [Logger, AiBaseService, AiHtmlOptimizeService, SocketService, RedisService],
  controllers: [AiHtmlOptimizeController],
})
export class AiHtmlOptimizeModule {}
