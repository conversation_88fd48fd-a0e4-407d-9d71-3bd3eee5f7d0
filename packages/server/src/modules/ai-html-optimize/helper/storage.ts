import { get, set } from 'lodash';
import { getRandomString } from '@utils/request';

import { collect } from '../utils';

/**
 * 函数装饰，将函数某个入参中符合正则的字符串存储到缓存中，函数  返回值中将该字符串替换为缓存中的值
 * 该函数用于处理 HTML 字符串中的 base64 图片数据，避免重复存储和传输
 * @param {RegExp} regExp - 正则表达式
 * @param {Object} options - 配置选项
 * @param {number} options.paramIndex  - 参数索引
 * @param {string} options.inFelid - 输入字段路径
 * @param {string} options.outFelid - 输出字段路径
 * @return {Function} 装饰器函数
 */
export function Storage(
  regExp: RegExp,
  options?: {
    paramIndex?: number;
    inFelid?: string;
    outFelid?: string;
  },
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cache = new Map<string, string>();

      // 处理输入参数
      const paramIndex = options?.paramIndex ?? 0;
      const input = args[paramIndex];

      let inputField = input;
      if (options?.inFelid) {
        inputField = get(input, options.inFelid);
      }
      if (typeof inputField === 'string') {
        // 查找所有匹配的字符串
        const matches = inputField.match(regExp);
        if (matches) {
          matches.forEach((match) => {
            // 生成唯一的缓存键
            const cacheKey = `__prompt-mini__${getRandomString(16)}`;
            // 将匹配的字符串存储到缓存中
            cache.set(cacheKey, match);
            // 替换输入字段中的匹配内容为引用
            inputField = inputField.replace(match, cacheKey);
          });
        }
      }
      // 更新输入参数
      if (options?.inFelid) {
        set(input, options.inFelid, inputField);
      } else {
        args[0] = inputField;
      }

      // 调用原始方法
      const result = await originalMethod.apply(this, args);

      const replace = (str: string) => {
        cache.forEach((value, key) => {
          // 替换输出字段中的引用为缓存中的值
          str = str.replace(key, value);
        });
        return str;
      };
      // 处理输出
      if (result) {
        if (typeof result === 'string') {
          // 替换输出字段中的匹配内容为引用
          const modifiedOutput = replace(result);
          return modifiedOutput;
        }

        if (options?.outFelid && typeof result === 'object') {
          const outFelidArr = collect(result, options.outFelid);
          outFelidArr.forEach((outFelid) => {
            const outputField = get(result, outFelid);
            if (typeof outputField === 'string') {
              // 替换输出字段中的匹配内容为引用
              const modifiedOutput = replace(outputField);
              set(result, outFelid, modifiedOutput);
            }
          });
        }

        return result;
      }

      return result;
    };

    return descriptor;
  };
}
