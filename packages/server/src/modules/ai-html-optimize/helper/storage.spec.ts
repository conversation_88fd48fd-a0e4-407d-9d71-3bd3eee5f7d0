import 'jest';
import { Storage } from './storage';

describe('Storage Decorator', () => {
  test('should replace matched strings with placeholders', async () => {
    class TestClass {
      @Storage(/data:image\/(png|jpg|jpeg|gif|webp|bmp);base64,[A-Za-z0-9+/=]+/g, {
        inFelid: 'htmlStr',
      })
      async testMethod(options: { htmlStr: string }) {
        const { htmlStr } = options;
        return htmlStr;
      }
    }

    const testInstance = new TestClass();
    const inputHtml =
      'This is a test string with base64 image data: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA\n' +
      'AAAFCAYAAACNbyblAAAAHElEQVR42mP8z8AIAwAB/9+Q4AAAAABJRU5ErkJggg==';

    const result = await testInstance.testMethod({ htmlStr: inputHtml });
    expect(result).toBe(inputHtml);
  });

  test('should replace matched strings with outFelid', async () => {
    class TestClass {
      @Storage(/data:image\/(png|jpg|jpeg|gif|webp|bmp);base64,[A-Za-z0-9+/=]+/g, {
        outFelid: '[blobs].content',
      })
      async testMethod(html) {
        return {
          blobs: [
            {
              content: html,
            },
            {
              content: html,
            },
          ],
        };
      }
    }

    const testInstance = new TestClass();
    const inputHtml =
      'This is a test string with base64 image data: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA\n' +
      'AAAFCAYAAACNbyblAAAAHElEQVR42mP8z8AIAwAB/9+Q4AAAAABJRU5ErkJggg==';

    const result = await testInstance.testMethod(inputHtml);
    expect(result).toStrictEqual({
      blobs: [
        {
          content: inputHtml,
        },
        {
          content: inputHtml,
        },
      ],
    });
  });
});
