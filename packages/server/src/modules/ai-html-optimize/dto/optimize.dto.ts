import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class OptimizeDto {
  @ApiProperty({
    description: 'html内容',
    type: String,
  })
  @IsNotEmpty({ message: 'html is required' })
  htmlStr: string;

  @ApiProperty({
    description: '优化内容',
    type: String,
  })
  @IsNotEmpty({ message: 'prompt is required' })
  prompt: string;

  @ApiProperty({
    description: 'socket 链接id',
    type: String,
  })
  connectionId?: string;
}
