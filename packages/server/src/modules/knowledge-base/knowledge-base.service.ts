import { KnowledgeBaseConfig } from '@config/config.interface';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { AiStreamService } from '@modules/ai-base/ai-stream.service';
import { AIModelId } from '@modules/ai-base/define';
import { PrismaService } from '@modules/prisma/prisma.service';
import { HttpService } from '@nestjs/axios';
import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Prisma } from '@prisma/client';
import { IPager } from '@shared/decorator/list-params.decorator';
import { CoreMessage, createIdGenerator } from 'ai';
import FormData from 'form-data';
import { fromPairs, map as lodashMap } from 'lodash';
import { catchError, forkJoin, from, map, switchMap, throwError } from 'rxjs';

import { PromptDto } from './dto/prompt-dto';

@Injectable()
export class KnowledgeBaseService {
  private BASE_URL = '';
  private ENTERPRISE_ID = '';
  private API_KEY = '';

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly aiBaseService: AiBaseService,
    private readonly aiStreamService: AiStreamService,
    private readonly prisma: PrismaService,
  ) {
    const config = this.configService.get<KnowledgeBaseConfig>('knowledgeBase');
    if (!config?.URL || !config?.ENTERPRISE_ID || !config?.API_KEY) {
      throw new InternalServerErrorException('知识库配置缺失，请检查环境变量');
    }
    this.BASE_URL = config.URL;
    this.ENTERPRISE_ID = config.ENTERPRISE_ID;
    this.API_KEY = config.API_KEY;
  }

  private readonly logger = new Logger(KnowledgeBaseService.name);

  async clear() {
    return await this.prisma.knowledge_base.deleteMany();
  }

  /**
   * 构建知识库里相关API的完整请求URL
   * @param path - API的相对路径（例如 '/create' 或 '/files'）
   * @returns 拼接后的完整请求URL字符串（格式：BASE_URL/api/v1/enterprises/企业ID/knowledge-bases/相对路径）
   */
  private getApiUrl(path: string): string {
    return `${this.BASE_URL}/api/v1/enterprises/${this.ENTERPRISE_ID}/knowledge-bases${path}`;
  }

  /**
   * 获取用于调用外部知识库API的认证请求头
   * @returns 包含认证信息的请求头对象（格式符合 NestJS HttpService 请求要求）
   *          - headers.Content-Type: 指定请求体格式为 JSON
   *          - headers.Authorization: 使用 Bearer 令牌认证，令牌来自配置的 API_KEY
   */
  private getAuthHeaders(): { headers: { [key: string]: string } } {
    return {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.API_KEY}`,
      },
    };
  }

  /**
   * 获取用于调用外部知识库API的认证请求头
   * @returns 包含认证信息的请求头对象（格式符合 NestJS HttpService 请求要求）
   *          - headers.Content-Type: 指定请求体格式为 JSON
   *          - headers.Authorization: 使用 Bearer 令牌认证，令牌来自配置的 API_KEY
   */
  private handleApiError(operation: string, context: { userId: string; knowledgeId?: string; fileId?: string }) {
    return (error: any) => {
      this.logger.error(`${operation} 失败: ${error.message}`, {
        ...context,
        stack: error.stack,
        data: error.response?.data,
      });
      return throwError(() => new InternalServerErrorException(error.response?.data?.msg || '服务端异常'));
    };
  }

  create(name: string, description: string, userId: string) {
    return this.httpService
      .request({
        url: this.getApiUrl(''),
        method: 'post',
        data: {
          knowledge_base_name: name + '_' + userId,
          knowledge_base_description: description,
          visible_type: 'all',
        },
        ...this.getAuthHeaders(),
      })
      .pipe(
        switchMap((res) => {
          if (res.data.code === 0) {
            const id = res.data.data.knowledge_base_id;

            this.logger.log(`创建知识库成功`, {
              userId,
              knowledgeId: id,
              requestId: res.data.requestId,
            });

            return from(
              this.prisma.knowledge_base.create({
                data: {
                  id,
                  name,
                  description,
                  type: 'custom',
                  visible_type: 'all',
                  is_enabled: 1,
                  created_user: userId,
                  updated_user: userId,
                },
              }),
            );
          } else {
            throw new Error(res.data.message);
          }
        }),
        switchMap((result) => {
          return this.httpService
            .request({
              url: this.getApiUrl(`/${result.id}/status`),
              method: 'post',
              ...this.getAuthHeaders(),
              data: {
                knowledge_base_name: result.name,
                knowledge_base_description: result.description,
                enabled: true,
                query: 'updated query',
              },
            })
            .pipe(
              map((res) => {
                if (res.data.code === 0) {
                  this.logger.log(`启用知识库成功`, {
                    userId,
                    knowledgeId: result.id,
                    requestId: res.data.requestId,
                  });

                  return {
                    knowledgeId: result.id,
                  };
                } else {
                  throw new Error(res.data.message);
                }
              }),
            );
        }),
        catchError(this.handleApiError('创建知识库', { userId })),
      );
  }

  delete(id: string, userId: string) {
    return this.httpService.delete(this.getApiUrl(`/${id}`), this.getAuthHeaders()).pipe(
      switchMap((res) => {
        if (res.data.code === 0) {
          this.logger.log(`删除知识库成功`, {
            userId,
            knowledgeId: id,
            requestId: res.data.requestId,
          });

          return from(
            this.prisma.knowledge_base.update({
              where: {
                id,
                status: 0,
              },
              data: {
                status: 1,
                updated_user: userId,
                updated_at: new Date().toISOString(),
              },
            }),
          ).pipe(
            map(() => ({
              knowledgeId: id,
            })),
          );
        } else {
          throw new Error(res.data.message);
        }
      }),
      catchError(this.handleApiError('删除知识库', { userId, knowledgeId: id })),
    );
  }

  update(id: string, name: string, description: string, userId: string) {
    return this.httpService
      .request({
        url: this.getApiUrl(`/${id}/status`),
        method: 'post',
        ...this.getAuthHeaders(),
        data: {
          knowledge_base_name: name + '_' + userId,
          knowledge_base_description: description,
          enabled: true,
          query: 'updated query',
        },
      })
      .pipe(
        switchMap((res) => {
          if (res.data.code === 0) {
            this.logger.log(`更新知识库成功`, {
              userId,
              knowledgeId: id,
              requestId: res.data.requestId,
            });

            return from(
              this.prisma.knowledge_base.update({
                where: {
                  id,
                  status: 0,
                },
                data: {
                  name,
                  description,
                  updated_user: userId,
                  updated_at: new Date().toISOString(),
                },
              }),
            ).pipe(
              map(() => ({
                knowledgeId: id,
              })),
            );
          } else {
            throw new Error(res.data.message);
          }
        }),
        catchError(this.handleApiError('更新知识库', { userId, knowledgeId: id })),
      );
  }

  findByPager({ pageNum, pageSize, order }: IPager, userId: string, name: string) {
    const where: Prisma.knowledge_baseWhereInput = {
      created_user: userId,
      status: 0,
    };

    if (name) {
      where.name = { contains: name };
    }

    // 创建查询列表的 Observable
    const list$ = from(
      this.prisma.knowledge_base.findMany({
        select: { id: true, name: true, description: true, updated_at: true },
        skip: (pageNum - 1) * pageSize,
        take: pageSize,
        where,
        orderBy: lodashMap(order, (it) => fromPairs([it])),
      }),
    );

    // 创建查询总数的 Observable
    const count$ = from(this.prisma.knowledge_base.count({ where }));

    // 查询知识库中台数据
    const original$ = this.httpService.request({
      url: this.getApiUrl('?filter.type=custom&page_size=1000'),
      method: 'get',
      ...this.getAuthHeaders(),
    });

    // 使用 forkJoin 并行执行查询
    return forkJoin([list$, count$, original$]).pipe(
      map(([list, count, res]) => {
        if (res.data.code === 0) {
          const knowledges = res.data.data.knowledge_bases;

          const mergeList = list.map((item) => {
            const knowledge = knowledges.find((it) => it.id === item.id);

            return {
              ...item,
              file_num: knowledge.file_num,
            };
          });

          return {
            list: mergeList,
            count,
          };
        } else {
          this.logger.error(`获取知识库列表失败`, {
            userId,
            data: res?.data,
          });
          return {
            list,
            count,
          };
        }
      }),
      catchError(this.handleApiError('获取知识库列表', { userId })),
    );
  }

  uploadFile(file: Express.Multer.File, userId: string, knowledgeId: string) {
    // 创建 FormData 实例
    const form = new FormData();
    form.append('file', file.buffer, {
      filename: file.originalname,
      contentType: file.mimetype,
    });

    return this.httpService
      .putForm(this.getApiUrl(`/${knowledgeId}/files`), form, {
        headers: { 'Content-Type': 'multipart/form-data', 'Authorization': 'Bearer ' + this.API_KEY },
      })
      .pipe(
        map((res) => {
          if (res.data.code === 0) {
            this.logger.log(`上传文件到知识库成功`, {
              userId,
              knowledgeId,
              fileName: file.originalname,
              requestId: res.data.requestId,
            });

            return {
              knowledgeId,
            };
          } else {
            throw new Error(res.data.message);
          }
        }),
        catchError(this.handleApiError('上传文件到知识库', { userId, knowledgeId })),
      );
  }

  findFilesByPage(id: string, page: number, pageSize: number, name: string, userId: string) {
    return this.httpService
      .request({
        url: this.getApiUrl(`/${id}/files`),
        method: 'get',
        ...this.getAuthHeaders(),
        params: {
          'filter.name': name,
          page,
          'page_size': pageSize,
        },
      })
      .pipe(
        map((res) => {
          if (res.data.code === 0) {
            this.logger.log(`获取知识库文件列表成功`, {
              userId,
              knowledgeId: id,
              requestId: res.data.requestId,
            });

            return res.data.data;
          } else {
            throw new Error(res.data.message);
          }
        }),
        catchError(this.handleApiError('获取知识库文件列表', { userId, knowledgeId: id })),
      );
  }

  queryKnowledge(knowledgeId: string, query: string, userId: string) {
    return this.httpService
      .request({
        url: this.getApiUrl(`/${knowledgeId}/query`),
        method: 'post',
        ...this.getAuthHeaders(),
        data: {
          query,
        },
      })
      .pipe(
        map((res) => {
          if (res.data.code === 0) {
            this.logger.log(`检索知识库成功`, {
              userId,
              knowledgeId,
              requestId: res.data.requestId,
            });

            return res.data.data;
          } else {
            throw new Error(res.data.message);
          }
        }),
        catchError(this.handleApiError('检索知识库', { userId, knowledgeId })),
      );
  }

  deleteFile(knowledgeId: string, fileId: string, userId: string) {
    return this.httpService.delete(this.getApiUrl(`/${knowledgeId}/files/${fileId}`), this.getAuthHeaders()).pipe(
      map((res) => {
        if (res.data.code === 0) {
          this.logger.log(`删除知识库文件成功`, {
            userId,
            knowledgeId,
            requestId: res.data.requestId,
          });

          return {
            knowledgeId,
            fileId,
          };
        } else {
          throw new Error(res.data.message);
        }
      }),
      catchError(this.handleApiError('删除知识库文件', { userId, knowledgeId, fileId })),
    );
  }

  optimize(prompt: string, modelId: AIModelId, userId: string) {
    const streamId = createIdGenerator({ size: 36 })();

    // 中断控制器
    const abort = this.aiStreamService.createStream(streamId);

    const messages: CoreMessage[] = [
      {
        role: 'user',
        content: prompt,
      },
    ];

    return from(
      this.aiBaseService.chatTextV2(
        {
          platform: 'venus',
          modelId: modelId,
          userId,
          abort,
        },
        {
          messages,
        },
      ),
    ).pipe(
      map((response) => ({
        text: response.text,
      })),
      catchError((error) => {
        this.logger.error(`优化文案失败: ${error.message}`, {
          userId,
          prompt,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  optimizeWithKnowledge(options: PromptDto, userId: string) {
    const { knowledgeId, messages, prompt, modelId } = options;

    const streamId = createIdGenerator({ size: 36 })();

    // 中断控制器
    const abort = this.aiStreamService.createStream(streamId);

    return from(
      this.aiBaseService.chatTextV2(
        {
          platform: 'venus',
          modelId: modelId,
          userId,
          abort,
        },
        {
          messages,
        },
      ),
    ).pipe(
      map((response) => ({
        text: response.text,
      })),
      catchError((error) => {
        this.logger.error(`优化文案【知识库】失败: ${error.message}`, {
          userId,
          prompt,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }
}
