import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Logger,
  Param,
  Post,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IPager, PagerParams } from '@shared/decorator/list-params.decorator';
import { extname } from 'path';

import { CreateDto } from './dto/create-dto';
import { PromptDto } from './dto/prompt-dto';
import { QueryDto } from './dto/query-dto';
import { UpdateDto } from './dto/update-dto';
import { KnowledgeBaseService } from './knowledge-base.service';

@ApiTags('知识库管理')
@Controller('knowledge-base')
export class KnowledgeBaseController {
  constructor(private readonly knowledgeBaseService: KnowledgeBaseService) {}

  private readonly logger = new Logger(KnowledgeBaseController.name);

  @ApiOperation({
    description: '创建知识库',
  })
  @ApiBody({
    description: '创建对象',
    required: true,
    isArray: false,
    type: CreateDto,
  })
  @ApiResponse({
    status: 200,
    description: '创建成功',
    content: {
      'application/json': {
        example: {
          code: 0,
          data: {
            knowledge_base_id: 'kb_1ab4bb26476f7000',
          },
          message: 'successful',
          httpStatus: 200,
          timestamp: 1747640230209,
        },
      },
    },
  })
  @Post('/create')
  create(@Body() dto: CreateDto, @Headers('X-User-Id') userId: string) {
    const { name, description } = dto;
    return this.knowledgeBaseService.create(name, description, userId);
  }

  @ApiOperation({
    description: '删除知识库',
  })
  @Delete('delete/:id')
  delete(@Param('id') id: string, @Headers('X-User-Id') userId: string) {
    return this.knowledgeBaseService.delete(id, userId);
  }

  @ApiOperation({
    description: '更新知识库',
  })
  @ApiBody({
    description: '更新对象',
    required: true,
    isArray: false,
    type: UpdateDto,
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    content: {
      'application/json': {
        example: {
          code: 0,
          data: {
            knowledge_base_id: 'kb_1ab4bb26476f7000',
          },
          message: 'successful',
          httpStatus: 200,
          timestamp: 1747640230209,
        },
      },
    },
  })
  @Post('/update')
  update(@Body() dto: UpdateDto, @Headers('X-User-Id') userId: string) {
    const { id, name, description } = dto;
    return this.knowledgeBaseService.update(id, name, description, userId);
  }

  @ApiOperation({
    description:
      '分页查询知识库（支持名称搜索和排序），默认按创建时间（created_at）升序排序；未传分页参数时默认 pageNum=1, pageSize=10',
  })
  @ApiQuery({ name: 'pageNum', description: '分页页码', required: false })
  @ApiQuery({
    name: 'pageSize',
    description: '分页每页记录数',
    required: false,
  })
  @ApiQuery({ name: 'orderBy', description: '排序字段', required: false })
  @ApiQuery({
    name: 'order',
    description: '排序方式,可选值【desc、asc】',
    required: false,
  })
  @ApiQuery({
    name: 'name',
    type: 'string',
    description: '知识库名称搜索关键字（模糊匹配）',
    required: false,
    example: '',
  })
  @ApiResponse({
    status: 200,
    description: '分页查询成功',
    content: {
      'application/json': {
        example: {
          code: 0,
          data: {
            list: [
              {
                id: 'kb_1ab4bb26476f7000',
                name: '测试知识库 001',
                description: '这是一个测试知识库',
                updated_at: '2025-05-19T07:37:09.000Z',
              },
            ],
            count: 5, // 总记录数
          },
          message: 'successful',
          httpStatus: 200,
          timestamp: 1747640230209,
        },
      },
    },
  })
  @Get('list')
  findByPage(
    @PagerParams({
      // 默认排序
      defaultOrder: [['created_at', 'asc']],
    })
    pager: IPager,
    @Headers('X-User-Id') userId: string,
    @Query('name') title: string,
  ) {
    return this.knowledgeBaseService.findByPager(pager, userId, title);
  }

  @ApiOperation({
    description: '上传文件到知识库',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    description: '目标知识库 ID',
    required: true,
    example: 'kb_1ab4bb26476f7000',
  })
  @ApiBody({
    description: '上传的 Markdown 文件',
    required: true,
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Markdown 文件（.md/.markdown 格式）',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '文件上传成功',
    content: {
      'application/json': {
        example: {
          code: 0,
          data: {
            knowledge_base_id: 'kb_1ab4bb26476f7000',
          },
          message: '文件上传成功',
          httpStatus: 200,
          timestamp: 1747640230209,
        },
      },
    },
  })
  @Post('/file/:id')
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        const allowedExtensions = ['.md', '.markdown', '.docx', '.pdf'];
        const ext = extname(file.originalname).toLowerCase();

        // 验证文件后缀名
        if (!allowedExtensions.includes(ext)) {
          return callback(new BadRequestException('仅支持 Markdown、DOCX 和 PDF 文件'), false);
        }

        // 验证 MIME 类型
        const allowedMimeTypes = [
          'text/markdown',
          'text/x-markdown',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/pdf',
        ];

        if (!allowedMimeTypes.includes(file.mimetype)) {
          return callback(new BadRequestException('不支持的文件类型 【' + file.mimetype + '】'), false);
        }

        callback(null, true);
      },
    }),
  )
  uploadFile(@UploadedFile() file: Express.Multer.File, @Param('id') id: string, @Headers('X-User-Id') userId: string) {
    return this.knowledgeBaseService.uploadFile(file, userId, id);
  }

  @ApiOperation({
    description: '分页指定知识库内文件处理状态',
  })
  @ApiQuery({ name: 'pageNum', description: '分页页码', required: false })
  @ApiQuery({
    name: 'pageSize',
    description: '分页每页记录数',
    required: false,
  })
  @ApiQuery({
    name: 'name',
    type: 'string',
    description: '文件名称搜索关键字（模糊匹配）',
    required: false,
    example: '',
  })
  @ApiResponse({
    status: 200,
    description: '分页查询成功',
    content: {
      'application/json': {
        example: {
          code: 0,
          data: {
            total_count: 10,
            total_page: 2,
            files: [
              {
                file_id: 'file_1ab4c73f66ef7000',
                rel_file_path: 'XXX.txt',
                file_size: '162',
                is_enabled: true,
                retrieval_count: 0,
                created_at: 1747643401,
                process_status: 'finished',
                process_status_reason: '',
              },
            ],
          },
          message: 'successful',
          httpStatus: 200,
          timestamp: 1747640230209,
        },
      },
    },
  })
  @Get('files/:knowledgeId')
  findFilesByPage(
    @Headers('X-User-Id') userId: string,
    @Param('knowledgeId') id: string,
    @Query('pageNum') pageNum: number,
    @Query('pageSize') pageSize: number,
    @Query('name') title: string,
  ) {
    return this.knowledgeBaseService.findFilesByPage(id, pageNum || 1, pageSize || 10, title || '', userId);
  }

  @ApiOperation({
    description: '删除知识库文件',
  })
  @ApiParam({
    name: 'knowledgeId',
    type: 'string',
    description: '目标知识库 ID',
    required: true,
    example: 'kb_1ab4bb26476f7000',
  })
  @ApiParam({
    name: 'fileId',
    type: 'string',
    description: '待删除的文件 ID',
    required: true,
    example: 'file_1ab4c73f66ef7000',
  })
  @ApiResponse({
    status: 200,
    description: '文件删除成功',
    content: {
      'application/json': {
        example: {
          code: 0,
          data: {
            knowledgeId: 'kb_1ab4bb26476f7000',
            fileId: 'file_1ab4c73f66ef7000',
          },
          message: 'successful',
          httpStatus: 200,
          timestamp: 1747640230209,
        },
      },
    },
  })
  @Delete('files/:knowledgeId/:fileId')
  deleteFile(
    @Param('knowledgeId') knowledgeId: string,
    @Param('fileId') fileId: string,
    @Headers('X-User-Id') userId: string,
  ) {
    return this.knowledgeBaseService.deleteFile(knowledgeId, fileId, userId);
  }

  @ApiOperation({
    description: '文案优化',
  })
  @ApiBody({
    description: '文案优化请求体',
    required: true,
    type: PromptDto,
    schema: {
      type: 'object',
      properties: {
        prompt: { type: 'string', description: '需要优化的原始文案', example: '帮我写一段产品推广文案' },
        modelId: { type: 'string', description: '使用的大模型ID（可选）', example: 'gpt-4o' },
        knowledgeId: {
          type: 'string',
          description: '关联的知识库ID（可选，用于增强优化效果）',
          example: 'kb_1ab4bb26476f7000',
        },
        text: {
          type: 'string',
          description: '原始文案内容（knowledgeId不为空时必填）',
          example: '这是一段需要优化的文案',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '请求成功',
    content: {
      'application/json': {
        example: {
          code: 0,
          data: {
            text: '【产品推广】我们的智能助手采用先进AI技术...（完整优化文案）',
          },
          message: 'successful',
          httpStatus: 200,
          timestamp: 1747640230209,
        },
      },
    },
  })
  @Post('optimize')
  optimize(@Body() dto: PromptDto, @Headers('X-User-Id') userId: string) {
    const { prompt, modelId, knowledgeId } = dto;
    if (knowledgeId) {
      return this.knowledgeBaseService.optimizeWithKnowledge(
        {
          ...dto,
          modelId: modelId || 'gpt-4o',
        },
        userId,
      );
    } else {
      return this.knowledgeBaseService.optimize(prompt, modelId || 'gpt-4o', userId);
    }
  }

  @ApiOperation({
    description: '检索知识库',
  })
  @ApiBody({
    description: '查询对象',
    required: true,
    isArray: false,
    type: QueryDto,
  })
  @ApiResponse({
    status: 200,
    description: '请求成功',
    content: {
      'application/json': {
        example: {
          code: 0,
          data: {
            items: [
              {
                score: 0.5150161,
                chunk: '文本块',
                metadata: {},
              },
            ],
          },
          message: 'successful',
          httpStatus: 200,
          timestamp: 1747640230209,
        },
      },
    },
  })
  @Post('query')
  query(@Body() dto: QueryDto, @Headers('X-User-Id') userId: string) {
    const { knowledgeId, text } = dto;
    return this.knowledgeBaseService.queryKnowledge(knowledgeId, text, userId);
  }

  // @Get('/clear')
  // async clearSession() {
  //   return this.knowledgeBaseService.clear();
  // }
}
