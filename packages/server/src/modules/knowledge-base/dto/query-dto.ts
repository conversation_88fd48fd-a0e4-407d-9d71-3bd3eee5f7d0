import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class QueryDto {
  @ApiProperty({
    description: '知识库编号',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'knowledgeId is required' })
  public knowledgeId: string;

  @ApiProperty({
    description: '文案内容',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'text is required' })
  public text: string;
}
