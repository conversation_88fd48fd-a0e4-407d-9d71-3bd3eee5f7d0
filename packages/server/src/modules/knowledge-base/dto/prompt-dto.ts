import { AIModelId, AIModelIdArray } from '@modules/ai-base/define';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsIn, IsOptional, IsString, ValidateNested } from 'class-validator';

export enum ChatRole {
  User = 'user',
  Assistant = 'assistant',
  System = 'system',
}

export class MessageEntryDto {
  @ApiProperty({
    enum: ChatRole,
    description: '对话角色标识',
    example: ChatRole.User,
  })
  @IsEnum(ChatRole)
  role: ChatRole;

  @ApiProperty({
    description: '消息内容',
    example: '请帮我解释这个代码片段',
  })
  @IsString()
  content: string;
}
export class PromptDto {
  @ApiProperty({
    description: '提示词',
    type: String,
    required: false,
  })
  @IsOptional()
  public prompt: string;

  @ApiProperty({
    description: '模型编号',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'modelId 必须是字符串' })
  @IsIn(AIModelIdArray, {
    message: '无效的模型编号',
  })
  public modelId: AIModelId;

  @ApiProperty({
    description: '知识库编号',
    type: String,
    required: false,
  })
  @IsOptional()
  public knowledgeId: string;

  @ApiProperty({
    description: '对话消息历史记录',
    type: [MessageEntryDto],
    required: false,
    example: [
      {
        role: 'user',
        content: '你好',
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MessageEntryDto)
  public messages: MessageEntryDto[];
}
