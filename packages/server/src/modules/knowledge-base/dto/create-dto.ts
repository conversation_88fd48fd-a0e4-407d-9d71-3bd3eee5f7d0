import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, MaxLength } from 'class-validator';

export class CreateDto {
  @ApiProperty({
    description: '名称',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'name is required' })
  @MaxLength(20, { message: 'name must be shorter than or equal to 20 characters' })
  public name: string;

  @ApiProperty({
    description: '描述',
    type: String,
    required: true,
  })
  @MaxLength(30, { message: 'name must be shorter than or equal to 30 characters' })
  public description: string;
}
