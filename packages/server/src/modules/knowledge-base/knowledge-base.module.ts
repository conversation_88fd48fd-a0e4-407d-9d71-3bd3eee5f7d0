import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { AiBaseModule } from '@modules/ai-base/ai-base.module';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { KnowledgeBaseController } from './knowledge-base.controller';
import { KnowledgeBaseService } from './knowledge-base.service';
import { AiStreamService } from '@modules/ai-base/ai-stream.service';

@Module({
  imports: [ConfigModule, HttpModule, PrismaModule, AiBaseModule],
  providers: [Logger, KnowledgeBaseService, AiBaseService, AiStreamService],
  controllers: [KnowledgeBaseController],
})
export class KnowledgeBaseModule {}
