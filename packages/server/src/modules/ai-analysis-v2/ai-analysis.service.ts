import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { catchError, from, map, switchMap, throwError } from 'rxjs';
import { createIdGenerator } from '@ai-sdk/provider-utils';

import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { CosService } from '@modules/cos/cos.service';
import { MessageStatus } from '@modules/ai-chat-session/define';
import { AIChatService } from '@modules/ai-chat-session/ai-chat-session.service';
import { templateReplace } from '@utils/tmpl';
import { PROMPT_TEMPLATE } from './helper/prompt';
import { AiStreamService } from '@modules/ai-base/ai-stream.service';
import { CoreMessage } from 'ai';
import { AIModelId, ChatOptions } from '@modules/ai-base/define';

@Injectable()
export class AiAnalysisService {
  constructor(
    private aiBaseService: AiBaseService,
    private httpService: HttpService,
    private readonly prisma: PrismaService,
    private readonly aiChatService: AIChatService,
    private readonly cosService: CosService,
    private readonly aiStreamService: AiStreamService,
  ) {}

  private readonly logger = new Logger(AiAnalysisService.name);

  createTask(userId: string, analysisModel: string) {
    const recordId = createIdGenerator({ size: 36 })();
    const startTime = Date.now();

    return from(
      this.prisma.ai_task_analysis.create({
        select: { id: true, created_at: true },
        data: {
          id: recordId,
          analysis_model: analysisModel,
          created_user: userId,
          updated_user: userId,
          status: MessageStatus.INCOMPLETE,
        },
      }),
    ).pipe(
      switchMap((task) =>
        this.aiChatService.createSession(userId, task.id, 'analysis').pipe(
          map((session) => ({
            taskId: task.id,
            userId,
            sessionId: session.id,
            duration: Date.now() - startTime,
          })),
        ),
      ),
      catchError((error) => {
        this.logger.error(`创建AI分析任务失败: ${error.message}`, {
          userId,
          analysisModel,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  getPrompt(analysisModelId: string, analysisParams: Record<string, string>) {
    const prompt = templateReplace(
      PROMPT_TEMPLATE[analysisModelId].optionalIsEmpty,
      analysisParams['产品/服务'],
      analysisParams['客户画像&目标'],
    );

    return prompt;
  }

  completionText(chatOptions: ChatOptions, prompt: string) {
    const streamId = createIdGenerator({ size: 36 })();

    // 中断控制器
    const abort = this.aiStreamService.createStream(streamId);

    const messages: CoreMessage[] = [
      {
        role: 'user',
        content: prompt,
      },
    ];

    return from(
      this.aiBaseService.chatTextV2(
        {
          ...chatOptions,
          abort,
        },
        {
          messages,
        },
      ),
    ).pipe(
      map((response) => ({
        text: response.text,
      })),
      catchError((error) => {
        this.logger.error(`分析模型失败: ${error.message}`, {
          userId: chatOptions.userId,
          prompt,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }
}
