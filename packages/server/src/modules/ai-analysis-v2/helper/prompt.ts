import { ANALYSIS_MODEL_CONSTANTS } from '../define';

export const PROMPT_TEMPLATE = {
  [ANALYSIS_MODEL_CONSTANTS.USER_JOURNEY_MAPPING]: {
    optionalIsEmpty: `
    你是一名用户体验分析专家，擅长深入理解用户行为模式和动机，识别用户痛点和需求，分析用户情绪曲线变化，并提供基于数据的洞察。
    现在请你借助用户旅程地图模型处理用户请求，用户提供的信息是：
    # 产品/服务 
    {1}
    # 客户画像&目标
    {2}
    请你确定用户的基本信息及用户使用产品或服务的核心需求，列出 6～7 项关键的旅程阶段。给出每个旅程阶段中的用户想法，用户行为，服务触点，用户感受和机会点这几个方面的信息，各个方面帮我列出的2～3项信息。
    用户想法是在当前阶段用户内心在想什么；
    用户行为是描述是用户采取的实际行为和用户使用的步骤。
    服务触点是用户在旅程中与产品、服务或品牌互动的所有关键时刻；
    用户感受是用户在旅程各阶段的情绪反应，可选项为'担忧'、'好奇'、'放心'、'中立'、'满意'、'犹豫'、'安心'、'等待'、'开心'、'观望',任选其一；
    用户痛点是用户遇到的问题、障碍或不满之处，这些都是改进的机会点；
    机会点是通过对客户的需求分析，在当前阶段针对用户想法的合理改变；
    请你遵循以下的格式：
    # 用户基本信息
    * 姓名: XXX
    * 年龄: XXX
    * 性别: XXX
    * 职业: XXX
    * 日常形态: XXX

    # 核心需求
    * XXX
    ...
    * XXX
    # 用户旅程阶段
    * XXX
    ...
    * XXX

    # 旅程阶段——XXX
    ## 用户想法
    * XXX
    ... 
    * XXX
    ## 用户想法
    * XXX
    ... 
    * XXX
    ## 服务触点
    * XXX
    ... 
    * XXX
    ## 用户感受
    * XXX
    ## 机会点
    * XXX
    ... 
    * XXX
    `,
  },
};
