import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { AIModelId, AIModelIdArray } from '@modules/ai-base/define';
import { ANALYSIS_MODEL_CONSTANTS, AnalysisModelId } from '../define';

export class AnalysisTaskDto {
  @ApiProperty({
    description: '模型编号',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'modelId 必须是字符串' })
  @IsIn(AIModelIdArray, {
    message: '无效的模型编号',
  })
  public modelId: AIModelId;

  @ApiProperty({
    description: '分析模型编号',
    type: String,
    required: true,
    enum: ANALYSIS_MODEL_CONSTANTS,
  })
  @IsNotEmpty({ message: '分析模型编号不能为空' })
  @IsString({ message: 'analysisModelId 必须是字符串' })
  public analysisModelId: AnalysisModelId;

  @ApiProperty({
    description: '分析参数',
    type: Object,
    required: true,
  })
  @IsNotEmpty({ message: '分析参数不能为空' })
  public analysisParams: Record<string, string>;
}
