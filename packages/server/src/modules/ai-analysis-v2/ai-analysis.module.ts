import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { AiAnalysisService } from './ai-analysis.service';
import { AiAnalysisController } from './ai-analysis.controller';
import { AiBaseModule } from '@modules/ai-base/ai-base.module';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { CosModule } from '@modules/cos/cos.module';
import { CosService } from '@modules/cos/cos.service';
import { AIChatModule } from '@modules/ai-chat-session/ai-chat-session.module';
import { AIChatService } from '@modules/ai-chat-session/ai-chat-session.service';

@Module({
  imports: [ConfigModule, HttpModule, AiBaseModule, PrismaModule, CosModule, AIChatModule],
  providers: [Logger, AiAnalysisService, AiBaseService, CosService, AIChatService],
  controllers: [AiAnalysisController],
})
export class AiAnalysisModuleV2 {}
