/**
 * 分析模型常量集合 - 用于统一管理各种分析模型的标识符
 */
export const ANALYSIS_MODEL_CONSTANTS = {
  /** 用户旅程地图 */
  USER_JOURNEY_MAPPING: 'user-journey-mapping',
  /** 用户画像 */
  USER_PERSONA: 'user-persona',
  /** 用户故事 */
  USER_STORY_MAPPING: 'user-story-mapping',
  /** 用户访谈与分析 */
  USER_INTERVIEW_ANALYSIS: 'user-interview-analysis',
  /** 价值主张分析 */
  VALUE_PROPOSITION: 'value-proposition',
  /** AARRR模型 */
  AARRR: 'aarrr',
  /** 头脑风暴 */
  BRAIN_STORMING: 'brain-storming',
  /** 干系人地图 */
  STAKEHOLDER_MAPPING: 'stakeholder-mapping',
  /** 品牌信息屋 */
  BRAND_HOUSE: 'brand-house',
  /** 品牌资产金字塔 */
  BRAND_EQUITY_PYRAMID: 'brand-equity-pyramid',
  /** 品牌角色原型 */
  BRAND_ARCHETYPES: 'brand-archetypes',
  /** 品牌营销规划PPT */
  BRAND_MARKETING_PPT: 'brand-marketing-ppt',
  /** 营销分析 */
  MARKETING_ANALYSIS: 'marketing-analysis',
  /** 消费者趋势画布 */
  CONSUMER_TREND_CANVAS: 'consumer-trend-canvas',
  /** 营销创意泵 */
  MARKETING_IDEATION_ENGINE: 'marketing-ideation-engine',
  /** 营销规划 */
  MARKETING_PLANNING: 'marketing-planning',
  /** 商业模式画布 */
  BUSINESS_MODEL_CANVAS: 'business-model-canvas',
  /** 价值主张画布 */
  VALUE_PROPOSITION_CANVAS: 'value-proposition-canvas',
  /** 产品进化飞轮 */
  PRODUCT_EVOLUTION_FLYWHEEL: 'product-evolution-flywheel',
  /** 战略地图 */
  STRATEGY_MAPPING: 'strategy-mapping',
  /** SWOT模型 */
  SWOT: 'swot',
  /** 波特五力模型 */
  FIVE_FORCE: 'five-force',
  /** 商业热点引擎 */
  BUSINESS_TREND_ENGINE: 'business-trend-engine',
  /** 销售话术生出 */
  SALES_PLAYBOOK: 'sales-playbook',
} as const;

/**
 * 分析模型ID类型 - 所有可用分析模型的标识符联合类型
 */
export type AnalysisModelId = (typeof ANALYSIS_MODEL_CONSTANTS)[keyof typeof ANALYSIS_MODEL_CONSTANTS];
