import { ConfigService } from '@nestjs/config';
import { Body, Controller, Header, Logger, Post, Res, Response, Sse, Headers } from '@nestjs/common';
import { ApiBody, ApiOperation } from '@nestjs/swagger';

import { HEADER_X_REQUEST_ID } from '@shared/constants/system.constants';
import { genRequestId } from '@utils/request';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { AiAnalysisService } from './ai-analysis.service';
import { AnalysisTaskDto } from './dto/analysis-task.dto';
import { firstValueFrom, switchMap } from 'rxjs';
import { AIChatService } from '@modules/ai-chat-session/ai-chat-session.service';
import { createIdGenerator } from '@ai-sdk/provider-utils';

@Controller('analysis/v2')
export class AiAnalysisController {
  constructor(
    private configService: ConfigService,
    private aiBaseService: AiBaseService,
    private readonly aiChatService: AIChatService,
    private aiAnalysisService: AiAnalysisService,
  ) {}

  private readonly logger = new Logger(AiAnalysisController.name);

  @ApiOperation({
    tags: ['AI 分析'],
    description: '创建设计模型分析任务',
  })
  @ApiBody({
    description: '任务对象',
    required: true,
    isArray: false,
    type: AnalysisTaskDto,
  })
  @Post('/task')
  @Header(HEADER_X_REQUEST_ID, () => {
    return genRequestId(null);
  })
  createTask(@Body() dto: AnalysisTaskDto, @Headers('X-User-Id') userId: string) {
    const { modelId, analysisModelId, analysisParams } = dto;

    return this.aiAnalysisService.createTask(userId, analysisModelId).pipe(
      switchMap(({ duration, taskId, sessionId }) => {
        const prompt = this.aiAnalysisService.getPrompt(analysisModelId, analysisParams);
        return this.aiChatService.completionText(
          {
            sessionId,
            modelId,
            userId,
          },
          prompt,
          taskId,
        );
      }),
    );
  }
}
