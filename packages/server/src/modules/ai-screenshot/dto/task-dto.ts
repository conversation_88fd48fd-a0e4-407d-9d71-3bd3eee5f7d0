import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class ScreenshotTaskDto {
  @ApiProperty({
    description: '图片 Cos 存储路径',
    type: String,
  })
  @IsNotEmpty({ message: 'fileKey is required' })
  fileKey: string;

  @ApiProperty({
    description: '图片 Cos 地址',
    type: String,
  })
  @IsNotEmpty({ message: 'url is required' })
  url: string;

  @ApiProperty({
    description: '文件编号',
    type: String,
  })
  @IsNotEmpty({ message: 'fileId is required' })
  fileId: string;

  @ApiProperty({
    description: 'width',
    type: Number,
  })
  @IsNotEmpty({ message: 'width is required' })
  width: number;

  @ApiProperty({
    description: 'height',
    type: Number,
  })
  @IsNotEmpty({ message: 'height is required' })
  height: number;
}
