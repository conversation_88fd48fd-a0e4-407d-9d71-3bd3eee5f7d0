import { UIElement } from '@ai-assitant/ai-image-to-design';

/**
 * 判断一个 UI 元素是否为另一个 UI 元素的嵌套子元素。
 * @param a - 作为父元素基准的 UI 元素，用于检查其是否包含另一个元素。
 * @param b - 需要检查是否为嵌套子元素的 UI 元素。
 * @returns 如果 b 是 a 的嵌套子元素，则返回 true；否则返回 false。
 */
export function isNestedChild(a: UIElement, b: UIElement): boolean {
  if (!a.children || a.children.length === 0) {
    return false;
  }

  const isDirectChild = a.children.some((child) => child.id === b.id);
  if (isDirectChild) {
    return true;
  }

  return a.children.some((child) => isNestedChild(child, b));
}

export function sortElementTreeByY(elements: UIElement[]): UIElement[] {
  // 对当前层级的元素按 bbox[1] 排序
  const sortedElements = elements.sort((a, b) => b.bbox[1] - a.bbox[1]);

  // 递归处理每个元素的子元素
  return sortedElements.map((element) => ({
    ...element,
    children: element.children ? sortElementTreeByY(element.children) : undefined,
  }));
}
