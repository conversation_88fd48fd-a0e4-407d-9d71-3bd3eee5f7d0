export function parseGroupsFromText(text: string | null): any[] {
  const groups = [];

  if (text !== null) {
    const lines = text.split('\n');
    lines.forEach((it) => {
      if (it.trimStart().charAt(0) !== '#' && it !== '') {
        try {
          const lineText = it.split('#')[0];
          const groupIds = JSON.parse(lineText);
          groups.push(groupIds);
        } catch (err) {}
      }
    });
  }

  return groups;
}
