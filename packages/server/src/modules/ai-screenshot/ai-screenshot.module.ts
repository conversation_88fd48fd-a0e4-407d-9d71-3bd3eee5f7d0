import { AiBaseModule } from '@modules/ai-base/ai-base.module';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { AIChatModule } from '@modules/ai-chat-session/ai-chat-session.module';
import { AIChatService } from '@modules/ai-chat-session/ai-chat-session.service';
import { AiImageToDesignModule } from '@modules/ai-image-to-design/ai-image-to-design.module';
import { AiImageToDesignService } from '@modules/ai-image-to-design/ai-image-to-design.service';
import { CosModule } from '@modules/cos/cos.module';
import { CosService } from '@modules/cos/cos.service';
import { ImageUIToolsModule } from '@modules/image-ui-tools/image-ui-tools.module';
import { ImageUIToolsService } from '@modules/image-ui-tools/image-ui-tools.service';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { RedisService } from '@modules/redis/redis.service';
import { SocketModule } from '@modules/socket/socket.module';
import { SocketService } from '@modules/socket/socket.service';
import { HttpModule } from '@nestjs/axios';
import { Logger, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@nestjs-modules/ioredis';

import { AIScreenshotController } from './ai-screenshot.controller';
import { AIScreenshotService } from './ai-screenshot.service';

@Module({
  imports: [
    ConfigModule,
    HttpModule,
    AiBaseModule,
    SocketModule,
    RedisModule,
    PrismaModule,
    CosModule,
    AIChatModule,
    AiImageToDesignModule,
    ImageUIToolsModule,
  ],
  providers: [
    Logger,
    AiBaseService,
    AIScreenshotService,
    SocketService,
    RedisService,
    CosService,
    AIChatService,
    AiImageToDesignService,
    ImageUIToolsService,
  ],
  controllers: [AIScreenshotController],
})
export class AIScreenshotModule {}
