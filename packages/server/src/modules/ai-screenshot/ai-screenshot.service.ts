import {
  BBox,
  computeBbox,
  computeBBoxIntersectAreaScale,
  convertToElementTree,
  flattenNestedGroup,
  flattenUIElement,
  insertUIElement,
  isContainedUIElement,
  isIntersectBox,
  isIntersectUIElement,
  mathRoundBBox,
  NestedGroup,
  removeUIElement,
  RleMatrix,
  RleString,
  UIElement,
} from '@ai-assitant/ai-image-to-design';
import { createIdGenerator } from '@ai-sdk/provider-utils';
import { AiBaseService } from '@modules/ai-base/ai-base.service';
import { MessageStatus } from '@modules/ai-chat-session/define';
import { PROMPT_TEMPLATE } from '@modules/ai-image-to-design/helper/prompt';
import { CosService } from '@modules/cos/cos.service';
import { ImageUIToolsService } from '@modules/image-ui-tools/image-ui-tools.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { convertBase64ToBuffer, removeBase64Prefix } from '@utils/index';
import { templateReplace } from '@utils/tmpl';
import { CoreMessage } from 'ai';
import {
  catchError,
  concatMap,
  defer,
  expand,
  finalize,
  from,
  iif,
  map,
  mergeMap,
  of,
  reduce,
  retry,
  switchMap,
  tap,
  throwError,
  toArray,
} from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

import helper from '../../native/index';
import { ScreenshotContext, ScreenshotGroupOptions } from './define';
import { isNestedChild, sortElementTreeByY } from './helper/element';

// 可以作为容器的元素类型
const FRAME_BOX_TYPES = ['外框形状', '上下全宽度栏', '键盘', '图片'];

const AI_TOOL_URL = 'http://49.235.138.227:7077';

const AI_FONT_TOOL_URL = 'http://49.235.138.227:7721';

const AI_DIALOG_TOOL_URL = 'http://49.235.138.227:40000';

// 定义标签类型映射
const labelTypeMap = {
  0: '图标',
  1: '图片',
  2: '外框形状',
  3: 'ios安全区域小黑条',
  4: '分页指示器',
  5: '切换器',
  6: '复选框',
  7: '滑块',
  8: '活动指示器',
  9: '分割线',
  10: '超链接',
  11: '上下全宽度栏',
  12: '键盘',
};

@Injectable()
export class AIScreenshotService {
  constructor(
    private aiBaseService: AiBaseService,
    private httpService: HttpService,
    private readonly prisma: PrismaService,
    private readonly cosService: CosService,
    private imageUIToolsService: ImageUIToolsService,
  ) {}

  private readonly logger = new Logger(AIScreenshotService.name);

  /**
   * 创建一个AI图片转UI任务
   * @param userId 用户ID
   * @returns Observable<{id: string, created_at: Date}> 返回包含任务ID和创建时间的Observable
   * @throws InternalServerErrorException 当数据库操作失败时抛出
   */
  createTask(userId: string, url: string) {
    const recordId = createIdGenerator({ size: 36 })();

    return from(
      this.prisma.ai_task_image_to_ui.create({
        select: { id: true, created_at: true },
        data: {
          id: recordId,
          url,
          created_user: userId,
          updated_user: userId,
          status: MessageStatus.INCOMPLETE,
        },
      }),
    ).pipe(
      catchError((error) => {
        this.logger.error(`创建AI任务失败: ${error.message}`, {
          userId,
          url,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 将 base64 格式的图片上传至腾讯云 COS 对象存储
   * @param base64Image - base64 编码的图片字符串
   * @returns Observable 包含上传结果:
   *  - url: 上传后的图片公开访问链接
   *  - duration: 上传耗时(毫秒)
   * @throws Error 当 COS 上传失败时(状态码不为200)
   * @private
   */
  uploadToCos(base64Image: string, ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const cosKey = `${Date.now()}.png`;
    const base64Buffer = convertBase64ToBuffer(base64Image);

    return from(this.cosService.uploadToAiCos(cosKey, base64Buffer, ctx.userId)).pipe(
      map((result) => {
        return {
          key: result.Key,
          duration: Date.now() - startTime,
        };
      }),
      catchError((error) => {
        this.logger.error(`上传图片至COS失败: ${error.message}`, {
          fileId: ctx.fileId,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  getScreenshotCosUrl(fileKey: string) {
    const url = this.cosService.generateCdnSignUrl(fileKey);
    return url;
  }

  /**
   * 根据URL获取图片的Base64编码字符串，并记录请求耗时
   * @param url - 图片的URL地址
   * @returns Observable，包含图片的Base64编码字符串和请求耗时
   * - base64Image: string - 图片的Base64编码字符串
   * - duration: number - 请求耗时(毫秒)
   */
  getImageAsBase64(url: string) {
    const startTime = Date.now();

    return this.httpService
      .get(url, {
        responseType: 'arraybuffer',
      })
      .pipe(
        map((response) => {
          const buffer = Buffer.from(response.data, 'binary');
          const base64Image = buffer.toString('base64');

          return {
            base64Image,
            duration: Date.now() - startTime,
          };
        }),
        retry(2), // 添加重试机制
        catchError((error) => {
          this.logger.error(`获取图片的Base64数据失败: ${error.message}`, {
            url,
            stack: error.stack,
          });
          return throwError(() => error);
        }),
      );
  }

  /**
   * 更新AI图转UI任务的标注数据
   * @param taskId - 任务ID
   * @param labelJson - 标注数据JSON字符串
   * @returns Observable<{id: string, updated_at: Date}> - 返回更新后的任务ID和更新时间
   * @throws 当数据库更新失败时抛出错误
   */
  updateTaskLabelJson(taskId: string, labelJson: string) {
    return from(
      this.prisma.ai_task_image_to_ui.update({
        select: { id: true, updated_at: true },
        where: { id: taskId },
        data: {
          label_json: labelJson,
          updated_at: new Date().toISOString(),
        },
      }),
    ).pipe(
      catchError((error) => {
        this.logger.error(`更新图转UI任务标注失败: ${error.message}`, {
          taskId,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 更新AI图片转UI任务的结果
   * @param taskId - 任务ID
   * @param result - 任务结果
   * @returns Observable<{id: string, updated_at: string}> - 返回更新后的任务ID和更新时间
   * @throws Error - 当更新失败时抛出错误
   */
  updateTaskResult(taskId: string, result: string) {
    return from(
      this.prisma.ai_task_image_to_ui.update({
        select: { id: true, updated_at: true },
        where: { id: taskId },
        data: {
          task_result: result,
          updated_at: new Date().toISOString(),
        },
      }),
    ).pipe(
      catchError((error) => {
        this.logger.error(`更新图转UI任务结果失败: ${error.message}`, {
          taskId,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  updateTaskComplete(taskId: string) {
    return from(
      this.prisma.ai_task_image_to_ui.update({
        select: { id: true, updated_at: true },
        where: { id: taskId },
        data: {
          status: MessageStatus.COMPLETE,
          updated_at: new Date().toISOString(),
        },
      }),
    ).pipe(
      catchError((error) => {
        this.logger.error(`更新图转UI任务状态失败: ${error.message}`, {
          taskId,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 批量处理UI元素并生成基于Base64的SOM图像
   *
   * @param elements - UI元素数组
   * @param base64Image - 原始图像的base64字符串
   * @param width - 图像宽度
   * @param height - 图像高度
   * @returns Observable，包含处理后的图像URL数组和处理耗时
   * - items: {url: string, index: number}[] - 处理后的图像URL及对应索引
   * - duration: number - 处理耗时(毫秒)
   *
   * @description
   * 该方法会对每个UI元素进行以下处理:
   * 1. 提取分组内所有子元素的边界框
   * 2. 裁剪对应区域的图像
   * 3. 调整边界框坐标
   * 4. 生成SOM图像
   */
  getBatchSomAsBase64(base64Image: string, width: number, height: number, ctx: Partial<ScreenshotContext>) {
    const { prepareElements, taskId } = ctx;

    const flatElements = prepareElements.map((it) => {
      const elements = flattenUIElement(it)
        .map((ele) => {
          return {
            ...ele,
            children: undefined,
          };
        })
        .filter((item) => item.type !== '分组' && item.dialogType !== 'floating_icon');
      // 注：floating_icon 类型的浮层元素是需要绝对定位的，排除在编组计算中

      return {
        ...it,
        children: elements,
      };
    });

    const startTime = Date.now();

    const base64Str = removeBase64Prefix(base64Image);

    return from(flatElements).pipe(
      map(({ bbox, children, id }) => {
        const y = Math.max(0, bbox[1]);
        const desiredHeight = bbox[3] - bbox[1];
        const maxHeight = height - y;
        const h = Math.min(desiredHeight, maxHeight);
        const w = width;

        const base64 = helper.image_crop(base64Str, 0, y, w, h) as string;

        return {
          id,
          base64,
          children,
          offsetY: y,
        };
      }),
      concatMap(({ base64, children, offsetY, id }) => {
        const adjustedBboxes: BBox[] = children.map((it) => {
          const [x1, y1, x2, y2] = it.bbox;
          return [x1, y1 - offsetY, x2, y2 - offsetY];
        });
        return this.getSomAsBase64(base64, adjustedBboxes, ctx).pipe(map(({ url }) => ({ url, id, children })));
      }),
      reduce((acc, cur) => [...acc, cur], [] as { url: string; id: string; children: UIElement[] }[]),
      map((items) => ({
        items,
        duration: Date.now() - startTime,
      })),
      catchError((error) => {
        this.logger.error(`批量生成编组图片失败: ${error.message}`, {
          taskId,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 将图片和边界框信息发送到SOM服务进行处理,并将结果上传到COS存储
   *
   * @param base64 - 输入图片的base64编码字符串
   * @param boxes - 边界框数组,包含需要处理的区域信息
   * @returns Observable<{url: string, duration: number}> - 返回处理后图片的URL和处理耗时
   *
   * @throws {Error} 当SOM服务调用失败时抛出 'SOM_SERVICE_FAILED' 错误
   * @throws {Error} 当COS上传失败时抛出相应错误
   *
   * @remarks
   * - 会自动重试最多3次请求
   * - 上传到COS时使用public-read权限
   * - 处理过程中会记录耗时日志
   */
  getSomAsBase64(base64: string, boxes: BBox[], ctx: Partial<ScreenshotContext>) {
    const SOM_SERVICE_URL = `${AI_TOOL_URL}/som`;
    const somConfig = {
      url: SOM_SERVICE_URL,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        image_base64: base64,
        aggregation: false,
        threshold: 0.1,
        boxes,
        avoid_overlap: true,
        avoid_mark: true,
        avoid_box_strategy: 'min_iou',
      },
    };

    return defer(() => {
      const startTime = Date.now();
      return this.httpService.request(somConfig).pipe(
        switchMap((response) => {
          const end = Date.now();

          const cosKey = `${Date.now()}.png`;
          const base64Buffer = convertBase64ToBuffer(response.data.image_base64);

          return from(this.cosService.uploadToAiCos(cosKey, base64Buffer, ctx.userId)).pipe(
            map((result) => {
              return {
                url: this.cosService.generateCdnSignUrl(result.Key),
                duration: end - startTime,
              };
            }),
          );
        }),
        retry(3),
      );
    });
  }

  /**
   * 使用OCR服务对传入的Base64编码图片进行文字识别
   * @param base64 - 待识别的图片的Base64编码字符串
   * @returns Observable<{ocrResult: any, duration: number}> - 返回一个Observable，包含OCR识别结果和识别耗时（毫秒）
   * @throws Error - 当OCR识别失败时抛出 'OCR_SERVICE_FAILED' 错误
   */
  getTextByOcr(base64: string) {
    return defer(() => {
      const startTime = Date.now();
      return from(
        this.imageUIToolsService.ocr({
          imageBase64: base64,
          apiType: 'high quality',
          isWords: true,
          enableDetectSplit: false,
        }),
      ).pipe(
        map((ocrResult) => ({
          ocrResult,
          duration: Date.now() - startTime,
        })),
        retry(3),
        catchError((error) => {
          this.logger.error(`OCR 识别失败: ${error.message}`, {
            stack: error.stack,
          });
          throw new Error('OCR_SERVICE_FAILED');
        }),
      );
    });
  }

  getTextWeight(base64: string, boxes: number[][]) {
    const TEXT_WEIGHT_SERVICE_URL = `${AI_FONT_TOOL_URL}/text_classification`;
    const colorConfig = {
      url: TEXT_WEIGHT_SERVICE_URL,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: { image_base64: base64, boxes },
    };

    return defer(() => {
      const startTime = Date.now();
      return this.httpService.request(colorConfig).pipe(
        map((response) => ({
          result: response.data,
          duration: Date.now() - startTime,
        })),
        retry(3),
        catchError((error) => {
          this.logger.error(`文字粗体判断失败: ${error.message}`, {
            stack: error.stack,
          });

          throw new Error('TEXT_WEIGHT_SERVICE_FAILED');
        }),
      );
    });
  }

  getDialogElements(base64: string) {
    const DIALOG_SERVICE_URL = `${AI_DIALOG_TOOL_URL}/predict`;
    const config = {
      url: DIALOG_SERVICE_URL,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: { image_base64: base64, confidence_threshold: 0.3 },
    };

    return defer(() => {
      const startTime = Date.now();
      return this.httpService.request(config).pipe(
        map((response) => ({
          result: response.data,
          duration: Date.now() - startTime,
        })),
        retry(3),
        catchError((error) => {
          this.logger.error(`弹窗检测失败: ${error.message}`, {
            stack: error.stack,
          });

          throw new Error('DIALOG_SERVICE_SERVICE_FAILED');
        }),
      );
    });
  }

  /**
   * 获取截图分组信息
   * @param options - 截图分组选项
   * @param options.userId - 用户ID
   * @param options.modelId - 模型ID
   * @param options.url - 图片URL
   * @param options.platform - 平台信息
   * @param options.abort - 中止控制器
   * @returns 返回AI处理后的文本结果
   * @description
   * 该方法通过AI模型分析截图内容,生成分组信息。
   * 将图片和提示模板组合成消息,发送给AI模型进行处理。
   * 使用较低的temperature以获得更确定的输出。
   */
  getScreenShotGroup(options: ScreenshotGroupOptions) {
    const { userId, modelId, url, originUrl, platform, abort, requestId } = options;
    const content = templateReplace(PROMPT_TEMPLATE.ScreenShot_Element_Group, '');

    const messages: CoreMessage[] = [
      {
        role: 'system',
        content: 'You are an AI assistant that helps people find information.',
      },
      {
        role: 'user',
        content: [
          {
            type: 'image',
            image: originUrl,
          },
          {
            type: 'image',
            image: url,
          },
          {
            type: 'text',
            text: content,
          },
        ],
      },
    ];

    return this.aiBaseService.chatTextV2(
      {
        platform,
        modelId,
        userId,
        abort,
        requestId,
      },
      {
        messages,
        temperature: 0.2,
        topP: 1,
        maxTokens: 2048,
      },
    );
  }

  /**
   * 将图片进行填充处理
   * @param base64 - 输入图片的base64编码字符串
   * @param boxes - 需要填充的区域坐标数组
   * @param masks - 需要填充的区域蒙版数组
   * @returns Observable<{base64: string, duration: number}> - 返回填充后的图片base64编码和处理耗时
   * @throws {Error} 当填图服务调用失败时抛出 'INPAINT_SERVICE_FAILED' 错误
   */
  getInpaintAsBase64(base64: string, boxes: BBox[], masks: RleString[]) {
    const newBoxes = boxes.map(mathRoundBBox);

    const INPAINT_SERVICE_URL = `${AI_TOOL_URL}/inpaint`;
    const inpaintConfig = {
      url: INPAINT_SERVICE_URL,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        image_base64: base64,
        boxes: newBoxes,
        masks,
      },
    };

    return defer(() => {
      const startTime = Date.now();
      return this.httpService.request(inpaintConfig).pipe(
        map((response) => ({
          base64: response.data.image_base64,
          duration: Date.now() - startTime,
        })),
        retry(3),
        catchError((error) => {
          this.logger.error(`图片填色失败: ${error.message}`, {
            stack: error.stack,
          });

          return throwError(() => error);
        }),
      );
    });
  }

  prepareUIElements(elements: UIElement[], width: number, height: number, cropItems: RleMatrix[]) {
    // 遍历分类容器元素和非容器元素
    const containerElements = [];
    const otherElements = [];

    elements.forEach((element) => {
      if (FRAME_BOX_TYPES.includes(element.type)) {
        containerElements.push(element);
      } else {
        otherElements.push(element);
      }
    });

    // 将容器元素转换为树状结构
    const treeElements = convertToElementTree(containerElements).sort((a, b) => a.bbox[1] - b.bbox[1]);

    // 使用识别到的图标数据修正对应图标元素的 Bbox 范围框
    cropItems.forEach((item) => {
      let minX, minY, maxX, maxY;

      for (let x = 0; x < width; x++) {
        for (let y = 0; y < height; y++) {
          if (item.matrix[x][y] === 1) {
            // 记录图标的边界
            minX = Math.min(minX || x, x);
            minY = Math.min(minY || y, y);
            maxX = Math.max(maxX || x, x);
            maxY = Math.max(maxY || y, y);
          }
        }
      }
      const element = otherElements.find((it) => it.id === item.id);
      if (element && element.type === '图标') {
        element.bbox = [minX, minY, maxX, maxY];
      }
    });

    const subIds = [];
    // 基于网格原则处理容器元素
    const frameElements = this.prepareFrameElements(treeElements, width, height);

    // 先将属于弹窗元素的非容器元素插入到弹窗元素中
    this.insertUIElementToDialog(frameElements, otherElements, subIds);

    frameElements.forEach((item) => {
      // 遍历非容器元素，将它插入到包含的容器元素中
      otherElements.forEach((it) => {
        if (!subIds.includes(it.id) && this.containsUIElement(item, it)) {
          insertUIElement(item, it);
          subIds.push(it.id);
        }
      });
    });

    // 过滤无法插入到容器元素的元素
    let singleElements = otherElements.filter((it) => !subIds.includes(it.id));

    if (singleElements.length > 0) {
      singleElements.forEach((it) => {
        const elementWidth = it.bbox[2] - it.bbox[0];
        const elementHeight = it.bbox[3] - it.bbox[1];
        if (it.type === '滑块' && elementHeight > elementWidth * 3) {
          return;
        } else if (it.type === '分割线') {
          for (let i = 0; i < frameElements.length; i++) {
            if (isIntersectUIElement(frameElements[i], it)) {
              const children = [...frameElements[i].children, it];
              const childrenBox = computeBbox(children);
              frameElements[i] = {
                ...frameElements[i],
                bbox: [0, childrenBox[1], width, childrenBox[3]],
                children,
              };
              subIds.push(it.id);
              break;
            }
          }

          return;
        } else if (it.type === '图标' || it.type === '文本') {
          // 判断 如果元素同时与两个分组相交，则将这两个分组元素合并
          for (let i = 0; i < frameElements.length - 1; i++) {
            const currFrame = frameElements[i];
            const nextFrame = frameElements[i + 1];
            if (isIntersectUIElement(currFrame, it) && isIntersectUIElement(nextFrame, it)) {
              const children = [...currFrame.children, ...nextFrame.children, it];
              const childrenBox = computeBbox(children);
              frameElements.splice(i, 2, {
                ...currFrame,
                bbox: [0, childrenBox[1], width, childrenBox[3]],
                children,
              });
              subIds.push(it.id);
              break;
            }
          }
        }
      });
    }

    singleElements = singleElements.filter((it) => !subIds.includes(it.id));

    return [...frameElements, ...singleElements];
  }

  insertUIElementToDialog(frameElements: UIElement[], singleElements: UIElement[], subIds: string[]) {
    frameElements.forEach((item) => {
      if (item.dialogType) {
        singleElements.forEach((it) => {
          if (!subIds.includes(it.id) && this.containsUIElement(item, it)) {
            insertUIElement(item, it);
            subIds.push(it.id);
          }
        });
      } else if (item.children?.length > 0) {
        this.insertUIElementToDialog(item.children, singleElements, subIds);
      }
    });
  }

  /**
   * 处理容器元素的分组和布局
   * @param frameElements - 按从上到下、从左到右排序的容器元素数组
   * @param width - 图像宽度
   * @param height - 图像高度
   * @returns 处理后的UI元素数组，包含合并后的分组和调整后的布局
   *
   * @description
   * 该方法执行以下操作:
   * 1. 检测相交的容器元素并将它们合并到同一分组中
   * 2. 处理独立的容器元素
   * 3. 合并小分组以优化布局结构
   * 4. 调整元素垂直位置，确保无间隙布局
   * 5. 如果底部有足够空间(超过图像高度的10%)，添加一个额外的空分组
   *
   * 处理流程:
   * - 首先识别相交的容器元素并创建分组
   * - 然后通过prepareMergeElements合并小分组
   * - 最后调整垂直位置并处理底部空间
   *
   * @private
   */
  prepareFrameElements(frameElements: UIElement[], width: number, height: number) {
    const groupElements: UIElement[] = [];
    let minY = Infinity;
    let maxY = -Infinity;
    let groupIds: number[] = [];

    for (let i = 0; i < frameElements.length; i++) {
      const { bbox: currBox } = frameElements[i];

      minY = Math.min(minY, currBox[1]);
      maxY = Math.max(maxY, currBox[3]);

      const fullWidthBox = [0, minY, width, maxY] as BBox;

      // 判断当前容器元素是否与下一容器相交
      const hasNextElement = i !== frameElements.length - 1;
      const intersectsWithNext = hasNextElement && isIntersectBox(fullWidthBox, frameElements[i + 1].bbox);

      if (intersectsWithNext) {
        frameElements[i].isIntersect = true;
        frameElements[i + 1].isIntersect = true;
        groupIds.push(i);
      }

      // 将相交的容器元素放到一个分组中
      if (groupIds.length > 0 && (!hasNextElement || !isIntersectBox(fullWidthBox, frameElements[i + 1].bbox))) {
        const childElements = [...groupIds.map((it) => frameElements[it]), frameElements[i]];
        groupElements.push({
          id: 'groups_' + uuidv4(),
          type: '分组',
          bbox: computeBbox(childElements),
          children: childElements,
        });

        // 重置分组状态
        groupIds = [];
        minY = Infinity;
        maxY = -Infinity;
      } else if (groupIds.length === 0) {
        // 如果一个容器元素不与其它容器元素相交，它是单独的一个容器元素
        groupElements.push(frameElements[i]);
      }
    }

    const mergeElements = this.prepareMergeElements(groupElements, width, height);

    // 按垂直位置排序
    mergeElements.sort((a, b) => a.bbox[1] - b.bbox[1]);

    const result: UIElement[] = [mergeElements[0]];
    let lastBottom = mergeElements[0].bbox[3];

    // 处理元素之间的间隙
    for (let i = 1; i < mergeElements.length; i++) {
      const element = mergeElements[i];
      const gap = element.bbox[1] - lastBottom;
      const gapRatio = gap / height;

      // 如果间隙超过图像高度的10%，创建一个新的空分组
      if (gapRatio > 0.1) {
        result.push({
          id: 'groups_' + uuidv4(),
          type: '分组',
          bbox: [0, lastBottom, width, element.bbox[1]],
          children: [],
        });
        result.push(element);
      } else if (gapRatio > 0) {
        // 如果间隙小于10%，将空间分配给相邻的较小分组
        const prevElementHeight = result[result.length - 1].bbox[3] - result[result.length - 1].bbox[1];
        const currElementHeight = element.bbox[3] - element.bbox[1];

        if (prevElementHeight <= currElementHeight) {
          // 将间隙合并到上方较小的分组
          result[result.length - 1].bbox[3] = element.bbox[1];
        } else {
          // 将间隙合并到下方较小的分组
          element.bbox[1] = lastBottom;
        }
        result.push(element);
      } else {
        // 没有间隙，直接添加元素
        result.push(element);
      }

      lastBottom = element.bbox[3];
    }

    // 处理最后一个元素到图像底部的空间
    const lastElement = result[result.length - 1];
    const bottomGap = height - lastElement.bbox[3];
    const bottomGapRatio = bottomGap / height;

    if (bottomGapRatio > 0.1) {
      result.push({
        id: 'groups_' + uuidv4(),
        type: '分组',
        bbox: [0, lastElement.bbox[3], width, height],
        children: [],
      });
    } else if (bottomGapRatio > 0) {
      // 如果底部间隙小于10%，将其合并到最后一个元素
      lastElement.bbox[3] = height;
    }

    // 注：第一个分组的顶部坐标设置为0
    if (result.length > 0) {
      result[0].bbox[1] = 0;
    }

    return result;
  }

  prepareMergeElements(groupElements: UIElement[], width: number, height: number) {
    const HEIGHT_RATIO_THRESHOLD = 0.1;
    let mergeChildren: UIElement[] = [];
    const elements: UIElement[] = [];

    for (const element of groupElements) {
      const { type } = element;
      const bbox = computeBbox([element, ...mergeChildren]);
      const currentGroupHeight = bbox[3] - bbox[1];
      const heightRatio = currentGroupHeight / height;

      const group: UIElement = {
        id: 'groups_' + uuidv4(),
        type: '分组',
        bbox: [0, bbox[1], width, bbox[3]],
        children: type === '分组' ? [...element.children, ...mergeChildren] : [element, ...mergeChildren],
      };

      mergeChildren = [];

      const lastElement = elements[elements.length - 1];

      if (
        heightRatio > HEIGHT_RATIO_THRESHOLD ||
        (lastElement && (group.bbox[1] - lastElement.bbox[3]) / height > HEIGHT_RATIO_THRESHOLD)
      ) {
        elements.push(group);
      } else {
        if (elements.length > 0) {
          const childElements = [...lastElement.children, ...group.children].sort((a, b) => b.bbox[1] - a.bbox[1]);
          const newBbox = computeBbox(childElements);
          lastElement.children = childElements;
          lastElement.bbox = [0, newBbox[1], width, newBbox[3]];
        } else {
          mergeChildren = group.children;
        }
      }
    }

    // 处理剩余的 mergeChildren
    if (mergeChildren.length > 0) {
      const bbox = computeBbox(mergeChildren);
      elements.push({
        id: 'groups_' + uuidv4(),
        type: '分组',
        bbox: [0, bbox[1], width, bbox[3]],
        children: mergeChildren,
      });
    }

    return elements;
  }

  /**
   * 处理并组织UI元素的层级结构
   *
   * @param segments - 截图分段信息数组，包含元素ID、子元素和分组结构
   * @param elements - 待处理的UI元素数组
   * @returns 处理后的UI元素数组，按垂直位置从上到下排序
   *
   * @description
   * 该方法根据提供的分段信息重新组织UI元素的层级结构：
   * 1. 遍历每个分段，找到对应的元素
   * 2. 调用postLevelElements处理嵌套层级结构
   * 3. 更新原始元素数组中的对应元素
   * 4. 对处理后的元素进行排序
   *
   * 排序规则：
   * - 元素按照垂直位置（bbox[1]）从上到下排序
   * - 处理元素间的交叉关系，确保视觉上的正确层叠顺序
   *
   * 该方法是UI元素后处理流程的入口点，确保最终生成的UI结构符合设计要求。
   */
  postUIElements(ctx: Partial<ScreenshotContext>) {
    const { prepareElements, segments } = ctx;

    segments.forEach((it) => {
      const { id, children, groups } = it;

      const element = prepareElements.find((it) => it.id === id);

      this.postLevelElements(children, 0, element, groups, ctx);
    });

    const sortElements = sortElementTreeByY(prepareElements);

    const elements = this.postSortElements(sortElements);

    return elements;
  }

  /**
   * 处理UI元素的层级结构和分组逻辑
   *
   * @param elements - 待处理的UI元素数组
   * @param level - 当前处理的层级深度，从0开始
   * @param root - 根UI元素，用于插入和移除操作
   * @param groups - 嵌套分组结构，定义元素的分组关系
   * @param ctx - 截图上下文信息，包含用户ID、任务ID等
   * @returns UIElement[] - 返回处理后的UI元素数组
   *
   * @description
   * 该方法负责根据提供的分组信息重新组织UI元素的层级结构：
   * 1. 在第0层级时，验证分组ID的有效性并记录遗漏的元素
   * 2. 递归处理嵌套分组结构，将元素按层级组织
   * 3. 当层级元素数量大于1时，创建新的分组
   * 4. 处理编组过程中遗漏的非容器元素
   *
   * 处理流程：
   * - 首先收集当前层级的所有元素到levelElements集合中
   * - 对于数组类型的分组，递归调用自身处理下一层级
   * - 对于数字索引类型的分组，直接添加对应元素
   * - 最后根据处理结果决定是否创建新分组或返回单个元素
   */
  postLevelElements(
    elements: UIElement[],
    level: number,
    root: UIElement,
    groups: NestedGroup<number>[],
    ctx: Partial<ScreenshotContext>,
  ) {
    const { userId, requestId, fileId, taskId, sessionId } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    const levelElements = new Set<UIElement>();

    const missIds: string[] = [];
    if (level === 0) {
      const groupIds = flattenNestedGroup(groups);
      const invalidGroupIds = groupIds.filter((id) => id < 0 || id >= elements.length);

      for (let i = 0; i < elements.length; i++) {
        if (!groupIds.includes(i)) {
          missIds.push(elements[i].id);
        }
      }

      if (invalidGroupIds.length > 0) {
        this.logger.error(`后处理元素编组: ${'分组错误'}`, {
          ...commonLogRecord,
          invalidGroupIds,
          elements,
        });
      }

      if (missIds.length > 0) {
        this.logger.error(`后处理元素编组: ${'元素遗漏'}`, {
          ...commonLogRecord,
          missIds,
        });
      }
    }

    // 递归处理逻辑编组
    groups.forEach((it) => {
      if (Array.isArray(it)) {
        const subElements = this.postLevelElements(elements, level + 1, root, it, ctx);

        subElements.forEach((it) => {
          if (Array.from(levelElements).find((item) => item.id === it.id)) {
            return;
          }

          levelElements.add(it);
        });
      } else if (elements[it]) {
        levelElements.add(elements[it]);
      }
    });

    if (levelElements.size > 1) {
      const ids = Array.from(levelElements).map((it) => it.id);

      const newGroup = this.postGroupElementsByIds(root, ids);

      // 处理编组遗漏的非容器元素
      if (missIds.length > 0) {
        missIds.forEach((item) => {
          const element = elements.find((it) => it.id === item);
          if (element && !FRAME_BOX_TYPES.includes(element.type)) {
            removeUIElement(root, element.id);
            insertUIElement(root, element);
          }
        });
      }

      return newGroup ? [newGroup] : [];
    }

    return Array.from(levelElements);
  }

  /**
   * 根据一组子元素 ID，在给定根元素下合并或创建新的分组。
   *
   * 处理流程：
   * 1. 遍历根元素树，收集所有匹配 IDs 的节点及其父节点信息（node、parent、index）。
   * 2. 如果没有匹配节点，直接返回 null。
   * 3. 判断所有匹配节点是否属于同一个父节点：
   *    3.1. 若只有一个匹配节点，直接将其插回父节点原位置。
   *    3.2. 若所有子节点已覆盖父节点所有子项，无需改动，返回该父节点。
   *    3.3. 否则，移除这些匹配子节点，并创建一个新的“分组”节点，将这些子节点作为其 children，插回原位置，返回该分组。
   * 4. 若不在同一父节点下，尝试识别其中一个节点是否已自然成为包含其它节点的父节点，若是则直接返回该节点，否则返回 null。
   *
   * @param root 根 UI 元素节点（一棵树的根）。
   * @param ids 需要分组的子元素 ID 列表。
   * @returns 当成功分组时，返回新分组节点或对应的父节点；否则返回 null。
   */
  postGroupElementsByIds(root: UIElement, ids: string[]) {
    const matches: Array<{ node: UIElement; parent: UIElement; index: number }> = [];

    // 深度优先遍历函数：记录所有匹配的节点和其父节点信息
    const traverse = (node: UIElement, parent: UIElement | null, index: number) => {
      if (ids.includes(node.id) && parent !== null) {
        matches.push({ node, parent, index });
      }
      if (node.children) {
        node.children.forEach((child, i) => traverse(child, node, i));
      }
    };
    traverse(root, null, -1);

    // 若无匹配节点，直接返回
    if (matches.length === 0) return null;

    // 判断匹配节点是否都属于同一个父节点
    const firstParent = matches[0].parent;
    if (matches.every((m) => m.parent === firstParent)) {
      const matchNodes = matches.map((m) => m.node);
      const minIndex = Math.min(...matches.map((m) => m.index));

      if (matchNodes.length === 1) {
        firstParent.children!.splice(minIndex, 0, matchNodes[0]);
      } else if (matchNodes.length === firstParent.children.length) {
        // 如果所以匹配节点已经是父节点的所有子节点，则不处理
        return firstParent;
      } else {
        // 按降序移除节点以避免索引问题
        const indexes = matches.map((m) => m.index).sort((a, b) => b - a);
        indexes.forEach((index) => {
          firstParent.children.splice(index, 1);
        });

        // 如果是多个元素创建一个分组
        const groupedBox: UIElement = {
          id: 'groups_' + uuidv4(),
          type: '分组',
          bbox: computeBbox(matchNodes),
          children: matchNodes,
        };
        firstParent.children?.splice(minIndex, 0, groupedBox);
        return groupedBox;
      }
    }

    // 若不在同一父节点下，尝试匹配节点中包含的父节点，它是其它匹配节点的父节点
    let matchParent = null;
    for (const match of matches) {
      const parentId = match.node.id;

      if (matches.filter((it) => it.node.id !== match.node.id).every((it) => it.parent.id === parentId)) {
        matchParent = match.node;
        break;
      }
    }

    // 若找到隐含父节点，则返回该节点
    if (matchParent !== null) {
      return matchParent;
    }

    // 判断匹配的元素有一个共同的上级父节点
    const subIds = [];
    for (const match of matches) {
      const parentId = match.parent.id;

      if (matches.find((it) => it.node.id === parentId)) {
        subIds.push(match.node.id);
        const filterNodes = matches.filter((it) => !subIds.includes(it.node.id));

        if (filterNodes.length > 0) {
          const firstParent = filterNodes[0].parent;
          if (filterNodes.every((m) => m.parent === firstParent)) {
            const indexes = filterNodes.map((m) => m.index).sort((a, b) => b - a);
            indexes.forEach((index) => {
              firstParent.children.splice(index, 1);
            });

            // 如果是多个元素创建一个分组
            const groupedBox: UIElement = {
              id: 'groups_' + uuidv4(),
              type: '分组',
              bbox: computeBbox(filterNodes.map((it) => it.node)),
              children: filterNodes.map((it) => it.node),
            };
            firstParent.children?.splice(indexes[indexes.length - 1], 0, groupedBox);

            return groupedBox;
          }
        }
      }
    }

    return null;
  }

  postSortElements(elements: UIElement[]) {
    // 容器元素
    let containerElements = elements.filter((it) => [...FRAME_BOX_TYPES, '分组'].includes(it.type));

    // 递归处理子元素
    containerElements.forEach((it) => {
      if (it.children && it.children.length > 0) {
        const children = this.postSortElements(it.children);
        it.children = children;
      }
    });

    for (const container of containerElements) {
      // 删除空的分组元素
      if (container.type === '分组' && (!container.children || container.children.length === 0)) {
        const aIndex = elements.findIndex((it) => it.id === container.id);
        elements.splice(aIndex, 1);
      }

      // 判断当前容器元素是否被同级的其它容器元素包含
      for (const other of containerElements) {
        if (other.id !== container.id && this.containsUIElement(other, container)) {
          const aIndex = elements.findIndex((it) => it.id === container.id);
          const [removedElement] = elements.splice(aIndex, 1);
          insertUIElement(other, removedElement);
          break;
        }
      }
    }

    containerElements = elements.filter((it) => [...FRAME_BOX_TYPES, '分组'].includes(it.type));

    if (containerElements.length > 1) {
      // 如果两个容器元素相交，通过判断容器内的子元素是否与另一容器元素相交，相交的移动到上面
      for (let i = 0; i < containerElements.length - 1; i++) {
        const a = containerElements[i];
        const b = containerElements[i + 1];

        if (isIntersectBox(a.bbox, b.bbox) && (a.children || b.children)) {
          const isAUp = b.children?.find((it) => isIntersectBox(it.bbox, a.bbox));
          const isBUp = a.children?.find((it) => isIntersectBox(it.bbox, b.bbox));

          const aIndex = elements.findIndex((it) => it.id === a.id);
          const bIndex = elements.findIndex((it) => it.id === b.id);
          if (isBUp && !isAUp) {
            [elements[aIndex], elements[bIndex]] = [b, a];
          } else if (isAUp && isBUp) {
            // 如果各自都有子元素与其它容器元素相交，移动相交元素多的到上面
            console.log('存在交错的 UI 元素', a.id, b.id);
            const isAUps = b.children?.filter((it) => isIntersectBox(it.bbox, a.bbox));
            const isBUps = a.children?.filter((it) => isIntersectBox(it.bbox, b.bbox));

            if (isBUps.length > isAUps.length) {
              [elements[aIndex], elements[bIndex]] = [b, a];
            }
          }
        }
      }
    }

    // 处理单个元素与容器的关系
    const singleElements = elements.filter((it) => ![...FRAME_BOX_TYPES, '分组'].includes(it.type));

    for (const element of singleElements) {
      let isContainer = false;
      for (const container of containerElements) {
        // 如果单个元素在同级别的容器内，则将移入容器元素
        if (isContainedUIElement(container, element)) {
          const aIndex = elements.findIndex((it) => it.id === element.id);
          elements.splice(aIndex, 1);
          insertUIElement(container, element);
          isContainer = true;
          break;
        }
      }

      if (isContainer) {
        continue;
      }

      const intersectElements = containerElements.filter((container) => isIntersectBox(container.bbox, element.bbox));

      // 如果存在多个相交元素，则将元素插入到最外层容器中
      if (intersectElements.length > 1) {
        const targetIndex = elements.findIndex((el) => el === element);

        if (targetIndex !== -1) {
          // 从原位置移除元素
          const [movedElement] = elements.splice(targetIndex, 1);
          // 添加到数组末尾
          elements.push(movedElement);
        }
      }
    }

    // 如果分组元素内只包含一个分组元素，则将其移到最外层
    for (const container of containerElements) {
      if (container.type === '分组' && container.children?.length === 1 && container.children[0].type === '分组') {
        container.children = [...container.children[0].children];
      }
    }

    // 将弹窗元素移到最上方
    for (const element of elements) {
      if (element.dialogType) {
        const targetIndex = elements.findIndex((el) => el.id === element.id);

        if (targetIndex !== -1) {
          // 从原位置移除元素
          const [movedElement] = elements.splice(targetIndex, 1);
          // 添加到数组末尾
          elements.push(movedElement);
        }
      }
    }

    return elements;
  }

  /**
   * 判断元素是否被包含
   * @param outer
   * @param inner
   * @returns
   */
  private containsUIElement(outer: UIElement, inner: UIElement): boolean {
    return isContainedUIElement(outer, inner) || computeBBoxIntersectAreaScale(outer.bbox, inner.bbox) > 0.9;
  }

  computeBackground(base64Image: string, width: number, height: number, ctx: Partial<ScreenshotContext>) {
    const { elements } = ctx;
    let levelElements: UIElement[][] = [];
    this.groupByLevel(elements, 0, levelElements);

    for (let i = 0; i < levelElements.length; i++) {
      const children = levelElements[i];

      let isChange = false;
      for (let j = 0; j < children.length; j++) {
        const element = children[j];

        if (element.dialogType) {
          levelElements[i + 1].forEach((it) => {
            if (isIntersectUIElement(it, element) && !isNestedChild(element, it)) {
              this.groupByDialog(element, levelElements, i);
              isChange = true;
            }
          });
        }

        if (isChange) {
          break;
        }
      }
    }
    levelElements = levelElements.reverse();

    this.computeLevelBackgroundRx(base64Image, levelElements, 0, width, height, ctx).subscribe();
  }

  groupByLevel(elements: UIElement[] | UIElement, level: number, result: UIElement[][]) {
    // 如果当前节点是数组，遍历每个元素
    if (Array.isArray(elements)) {
      elements.forEach((item) => this.groupByLevel(item, level, result));
    } else {
      // 如果当前节点是对象，将其添加到对应层级的数组中
      if (!result[level]) {
        result[level] = []; // 如果当前层级的数组不存在，初始化它
      }
      result[level].push(elements);

      // 如果有子节点，递归处理子节点，层级加1
      if (elements.children && elements.children.length > 0) {
        this.groupByLevel(elements.children, level + 1, result);
      }
    }
  }

  groupByDialog(dialogElement: UIElement, elements: UIElement[][], level: number) {
    if (!elements[level]) {
      return;
    }
    dialogElement.children?.forEach((element) => {
      this.groupByDialog(element, elements, level + 1);
    });

    const targetIndex = elements[level].findIndex((it) => it?.id === dialogElement.id);

    if (targetIndex > -1) {
      const [removedElement] = elements[level].splice(targetIndex, 1);

      if (elements[level + 1]) {
        elements[level + 1].push(removedElement);
      } else {
        elements[level + 1] = [removedElement];
      }
    }
  }

  private computeLevelBackgroundRx(
    base64Image: string,
    levelBoxes: UIElement[][],
    level: number,
    width: number,
    height: number,
    ctx: Partial<ScreenshotContext>,
  ) {
    const { userId, requestId, fileId, taskId, sessionId } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    this.logger.log(`【图生UI】【图片资源处理开始】`, {
      ...commonLogRecord,
      level,
    });

    return of(null).pipe(
      concatMap(() => {
        // 处理当前层级
        if (!levelBoxes[level]) {
          this.handleFinalLevel(base64Image, width, height, ctx);
          return of(null);
        }

        const cropBoxes = levelBoxes[level].filter((it) => !['文本', '图标', '分组'].includes(it.type));

        return iif(
          () => cropBoxes.length === 0,
          // 如果当前层级没有需要处理的元素，直接处理下一层级
          defer(() => this.computeLevelBackgroundRx(base64Image, levelBoxes, level + 1, width, height, ctx)),
          // 处理当前层级的裁剪逻辑
          from(this.processCropOperations(base64Image, cropBoxes, ctx)).pipe(
            concatMap(() => {
              return this.getInpaintAsBase64(
                base64Image,
                cropBoxes.map((it) => it.bbox),
                [],
              ).pipe(
                map(({ base64, duration }) => {
                  return { base64: base64, cropBoxes: cropBoxes };
                }),
              );
            }),
            concatMap((result) =>
              iif(
                () => !!levelBoxes[level + 1],
                // 递归处理下一层级
                defer(() => this.computeLevelBackgroundRx(result.base64, levelBoxes, level + 1, width, height, ctx)),
                // 最终处理
                defer(() => {
                  this.handleFinalLevel(result.base64, width, height, ctx);
                  return of(null);
                }),
              ),
            ),
          ),
        );
      }),
      // 使用 expand 操作符处理递归
      expand((res) => (res === null ? of() : of(res))),
      catchError((error) => {
        this.logger.error(`背景图片处理失败: ${error.message}`, {
          userId: ctx.userId,
          taskId: ctx.taskId,
          stack: error.stack,
        });

        return of(null);
      }),
      finalize(() => {
        this.logger.log(`【图生UI】【图片资源处理完成】`, {
          ...commonLogRecord,
          level,
        });
      }),
    );
  }

  private handleFinalLevel(base64: string, width: number, height: number, ctx: Partial<ScreenshotContext>) {
    const { userId, requestId, fileId, taskId, sessionId } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    return this.uploadToCos(base64, ctx)
      .pipe(
        map(({ key }) => ({
          id: 'Root-Background',
          bbox: [0, 0, width, height],
          url: key,
        })),
        tap((item) => {
          this.logger.log(`【图生UI】【图片处理完成】`, {
            ...commonLogRecord,
            final: item,
          });
        }),
        switchMap((item) => {
          return this.prisma.ai_task_image_to_ui_resources.create({
            data: {
              id: createIdGenerator({ size: 36 })(),
              task_id: taskId,
              element_id: item.id,
              path: item.url,
              bbox: JSON.stringify({ bbox: item.bbox }),
              resource_type: 1,
              created_user: userId,
              updated_user: userId,
              status: 0,
            },
          });
        }),
        switchMap(() => {
          return this.updateTaskComplete(taskId);
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      )
      .subscribe();
  }

  private processCropOperations(base64Image: string, cropBoxes: UIElement[], ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { taskId, userId } = ctx;
    // 并发数
    const CONCURRENCY_LIMIT = 5;

    return from(cropBoxes).pipe(
      mergeMap((item) => {
        const base64 = helper.image_crop(
          base64Image,
          item.bbox[0],
          item.bbox[1],
          item.bbox[2] - item.bbox[0],
          item.bbox[3] - item.bbox[1],
        );
        return this.uploadToCos(base64, ctx).pipe(
          map(({ key }) => ({
            ...item,
            url: key,
          })),
          catchError((error) => {
            return throwError(() => error);
          }),
        );
      }, CONCURRENCY_LIMIT),
      toArray(),
      switchMap((items) => {
        // 提前过滤掉上传失败的项目
        if (items.length === 0) {
          console.warn('No background image processed successfully');
          return of({ count: 0, duration: Date.now() - startTime });
        }
        const dbRecords = items.map((item) => ({
          id: createIdGenerator({ size: 36 })(),
          task_id: taskId,
          element_id: item.id,
          path: item.url,
          bbox: JSON.stringify({ bbox: item.bbox }),
          resource_type: 1,
          created_user: userId,
          updated_user: userId,
          status: 0,
        }));

        return from(
          this.prisma.ai_task_image_to_ui_resources.createMany({
            data: dbRecords,
            skipDuplicates: true,
          }),
        ).pipe(
          map(({ count }) => {
            return {
              count,
              duration: Date.now() - startTime,
            };
          }),
        );
      }),
      catchError((error) => {
        return throwError(() => error);
      }),
    );
  }

  /**
   * 获取图生UI任务的状态
   * @param taskId - AI任务的唯一标识ID
   * @returns Observable<boolean> - 返回一个Observable,任务状态为1时返回true,否则返回false
   */
  getTaskStatus(taskId: string) {
    return from(
      this.prisma.ai_task_image_to_ui.findUnique({
        select: { id: true, created_at: true, status: true },
        where: { id: taskId },
      }),
    ).pipe(map((result) => (result.status === 1 ? true : false)));
  }

  /**
   * 获取指定任务的UI资源列表
   * @param taskId - 任务ID
   * @param skip - 分页跳过的记录数
   * @returns Observable<Array<UI资源>> - 返回包含UI资源信息的Observable流
   *
   * 查询条件:
   * - 匹配任务ID
   * - 状态为0的记录
   * - 按创建时间升序排序
   * - 支持分页
   *
   * 返回字段包括:id、创建时间、元素ID、资源类型、路径、边界框
   */
  getResources(taskId: string, skip: number) {
    return from(
      this.prisma.ai_task_image_to_ui_resources.findMany({
        select: { id: true, created_at: true, element_id: true, resource_type: true, path: true, bbox: true },
        where: { task_id: taskId, status: 0 },
        orderBy: { created_at: 'asc' },
        skip,
      }),
    );
  }
}
