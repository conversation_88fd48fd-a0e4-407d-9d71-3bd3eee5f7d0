import {
  BBox,
  OcrResponse,
  RleMatrix,
  ScreenshotSegment2,
  TextDetection,
  UIDialogType,
  UIElement,
  UITextStyle,
} from '@ai-assitant/ai-image-to-design';
import { AIModelId, AIPlatform } from '@modules/ai-base/define';

/**
 * 截图存储目录
 */
export const AI_SCREENSHOT_DIR = 'ai-screenshot';

/**
 * 图转UI任务处理上下文
 */
export type ScreenshotContext = {
  /**
   * 任务编号
   */
  taskId: string;
  /**
   * 对话编号
   */
  sessionId: string;
  /**
   * 截图URL
   */
  screenshotUrl: string;
  /**
   * 用户编号
   */
  userId: string;
  /**
   * 文件编号
   */
  fileId: string;
  /**
   * 请求 ID
   */
  requestId: string;
  /**
   * UI 元素
   */
  elements: UIElement[];
  /**
   * 原始 base64图像
   */
  base64Image: string;
  /**
   * 扣除弹窗或浮层的base64图像
   */
  base64Dialog: string;
  /**
   * 扣除图标的 base64图像
   */
  base64Crop: string;
  /**
   * 弹窗或浮层元素
   */
  dialogItems: DialogDetection[];
  /**
   * 扣除的图标矩阵数据
   */
  cropItems: RleMatrix[];
  /**
   * OCR识别结果
   */
  ocrResult: OcrResponse;
  /**
   * 文本检测结果
   */
  textDetections: TextDetection[];
  /**
   * 文本样式
   */
  textStyles: UITextStyle[];
  /**
   * 参与编组计算的元素
   */
  groupElements: UIElement[];
  /**
   * 预处理后的元素
   */
  prepareElements: UIElement[];
  /**
   * 分块编组图片
   */
  somItems: {
    url: string;
    id: string;
    children: UIElement[];
  }[];
  /**
   * 分块编组结果
   */
  segments: ScreenshotSegment2[];
};

/**
 * 图转UI编组处理参数
 */
export type ScreenshotGroupOptions = {
  originUrl: string;
  /**
   * 截图地址
   */
  url: string;
  /**
   * 模型ID
   */
  modelId: AIModelId;
  /**
   * 平台
   */
  platform: AIPlatform;
  /**
   * 用户ID
   */
  userId: string;
  /**
   * 中断控制器
   */
  abort: AbortController;
  /**
   * 请求 ID
   */
  requestId?: string;
};

/**
 * 文本颜色片段
 */
export type TextColorSegment = {
  /**
   * 颜色
   */
  color: string;
  /**
   * 分块起始索引
   */
  ids: [number, number];
};

export type DialogDetection = {
  /**
   * 边框范围
   */
  bbox: BBox;
  /**
   * 类型
   */
  type: UIDialogType;
  /**
   * 置信度
   */
  score: number;
  /***
   * 图片
   */
  base64: string;
};
