import {
  calculateTextBoundingBox,
  calculateTextBox,
  DetectionPosition,
  hasSignificantOverlap,
  isContainedUIElement,
  reverseMatrix,
  rleArrayToMatrix,
  RleMatrix,
  rleStringDecode,
  ScreenshotSegment2,
  TextDetection,
  UIElement,
  UIIcon,
  UITextStyle,
} from '@ai-assitant/ai-image-to-design';
import { createIdGenerator } from '@ai-sdk/provider-utils';
import { AiStreamService } from '@modules/ai-base/ai-stream.service';
import { AIChatService } from '@modules/ai-chat-session/ai-chat-session.service';
import { MessageStatus } from '@modules/ai-chat-session/define';
import { AiImageToDesignService } from '@modules/ai-image-to-design/ai-image-to-design.service';
import { PROMPT_TEMPLATE } from '@modules/ai-image-to-design/helper/prompt';
import { CosService } from '@modules/cos/cos.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import {
  Body,
  Controller,
  Get,
  Headers,
  InternalServerErrorException,
  Logger,
  Post,
  Query,
  Request,
} from '@nestjs/common';
import { ApiBody, ApiOperation } from '@nestjs/swagger';
import { getFileNameFromUrl, removeBase64Prefix } from '@utils/index';
import { extractLastMatchedByRegex } from '@utils/tmpl';
import { catchError, concatMap, from, map, mergeMap, of, reduce, switchMap, throwError, timer, toArray } from 'rxjs';

import helper from '../../native/index';
import { AIScreenshotService } from './ai-screenshot.service';
import { DialogDetection, ScreenshotContext, TextColorSegment } from './define';
import { ScreenshotTaskDto } from './dto/task-dto';
import { parseGroupsFromText } from './helper/extract';

@Controller('screenshot')
export class AIScreenshotController {
  constructor(
    private aiScreenshotService: AIScreenshotService,
    private readonly prisma: PrismaService,
    private readonly aiChatService: AIChatService,
    private readonly aiImageToDesignService: AiImageToDesignService,
    private readonly aiStreamService: AiStreamService,
    private readonly cosService: CosService,
  ) {}

  private readonly logger = new Logger(AIScreenshotController.name);

  @ApiOperation({
    tags: ['图生 UI'],
    description: '创建图生UI任务',
  })
  @ApiBody({
    description: '任务对象',
    required: true,
    isArray: false,
    type: ScreenshotTaskDto,
  })
  @Post('/task')
  createTask(@Body() dto: ScreenshotTaskDto, @Headers('X-User-Id') userId: string, @Request() req) {
    const { fileKey, url, width, height, fileId } = dto;

    let newTaskId = '';

    const requestId = req.requestId;

    const commonLogRecord = { userId, requestId, fileId, taskId: '', sessionId: '' };

    return this.createTaskAndSession(userId, fileKey).pipe(
      map(({ taskId, sessionId, duration }) => {
        commonLogRecord.taskId = taskId;
        commonLogRecord.sessionId = sessionId;

        this.logger.log(`【图生UI】【1.创建任务】`, {
          ...commonLogRecord,
          fileKey,
          screenshotUrl: url,
          duration,
        });

        return {
          ...commonLogRecord,
          duration,
          fileId,
          screenshotUrl: url,
        };
      }),
      switchMap(({ screenshotUrl, ...ctx }) => {
        return this.aiScreenshotService.getImageAsBase64(screenshotUrl).pipe(
          map(({ base64Image, duration }) => {
            return {
              base64Image,
              duration,
              screenshotUrl,
              ...ctx,
            };
          }),
        );
      }),
      switchMap(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【2.获取图片Base64数据】`, {
          ...commonLogRecord,
          duration,
        });

        newTaskId = ctx.taskId;

        return this.processDialogDetection(ctx);
      }),
      switchMap(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【3.获取弹窗与浮层元素】`, {
          ...commonLogRecord,
          dialogItems: ctx.dialogItems,
          duration,
        });

        newTaskId = ctx.taskId;

        return this.processLabelDetection(ctx);
      }),
      switchMap(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【4.获取元素标注信息】`, {
          ...commonLogRecord,
          elements: ctx.elements,
          duration,
        });
        return this.processIconCropping(width, height, ctx);
      }),
      switchMap(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【5.识别图片中的图标】`, {
          ...commonLogRecord,
          base64Crop: ctx.base64Crop,
          duration,
        });

        return this.processText(ctx, width);
      }),
      switchMap(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【6.OCR文字识别&文字样式识别】`, {
          ...commonLogRecord,
          elements: ctx.elements,
          duration,
        });

        return this.processGroupElements(width, height, ctx);
      }),
      switchMap(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【7.获取编组图片】`, {
          ...commonLogRecord,
          prepareElements: ctx.prepareElements,
          somItems: ctx.somItems,
          duration,
        });

        return this.processGroupAnalysis(ctx);
      }),
      switchMap(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【8.获取元素编组信息】`, {
          ...commonLogRecord,
          segments: ctx.segments,
          duration,
        });

        return this.processFinalInpainting(ctx);
      }),
      map(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【9.文字抠图填色】`, {
          ...commonLogRecord,
          base64Crop: ctx.base64Crop,
          duration,
        });

        return this.processBackgroundImage(width, height, ctx);
      }),
      map(({ duration, ...ctx }) => {
        this.logger.log(`【图生UI】【10.处理图标和背景图片】`, {
          ...commonLogRecord,
          duration,
        });

        return {
          url: ctx.screenshotUrl,
          taskId: ctx.taskId,
          elements: ctx.elements,
        };
      }),
      catchError(async (error) => {
        if (error instanceof Error) {
          this.logger.error(`图转UI任务失败: ${error.message}`, {
            ...commonLogRecord,
            taskId: newTaskId,
            stack: error.stack,
          });

          if (newTaskId) {
            await this.prisma.ai_task_image_to_ui.update({
              where: { id: newTaskId },
              data: {
                status: MessageStatus.ERROR,
                error_message: error.message,
                updated_at: new Date().toISOString(),
              },
            });
          }
        }
        throw new InternalServerErrorException('图转UI任务失败');
      }),
    );
  }

  @ApiOperation({
    tags: ['图生 UI'],
    description: '获取任务资源',
  })
  @Get('/resources')
  getResources(@Query('taskId') taskId: string, @Query('count') count: number, @Headers('X-User-Id') userId: string) {
    return this.aiScreenshotService.getTaskStatus(taskId).pipe(
      switchMap((isComplete) => {
        if (!isComplete) {
          // 如果任务未完成，直接返回空数组
          return of({
            resources: [],
            isComplete,
            count: count,
          });
        }

        return this.aiScreenshotService.getResources(taskId, count).pipe(
          map((resources) => {
            const paths = resources.map((resource) => resource.path);
            const urls = this.cosService.generateCdnSignUrl(paths);

            return resources.map((resource, index) => ({
              ...resource,
              url: urls[index],
            }));
          }),
          map((updatedResources) => ({
            resources: updatedResources,
            isComplete,
            count: count,
          })),
        );
      }),
      catchError(async (error) => {
        this.logger.error(`获取任务资源: ${error.message}`, {
          userId,
          taskId,
          stack: error.stack,
        });

        throw new InternalServerErrorException('获取任务资源失败');
      }),
    );
  }

  /**
   * 创建任务和对话
   * @param userId 用户ID
   * @param screenshotUrl 截图URL
   * @returns Observable 包含任务ID、对话ID、截图URL和处理时长的对象
   * @description 创建AI截图任务并为其创建对应的聊天对话,返回相关信息和处理耗时
   */
  private createTaskAndSession(userId: string, fileKey: string) {
    const startTime = Date.now();

    return this.aiScreenshotService.createTask(userId, fileKey).pipe(
      switchMap((task) =>
        this.aiChatService.createSession(userId, task.id, 'screenshot_to_ui').pipe(
          map((session) => ({
            taskId: task.id,
            userId,
            sessionId: session.id,
            fileKey,
            duration: Date.now() - startTime,
          })),
        ),
      ),
    );
  }

  private processDialogDetection(ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId, base64Image } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    return this.aiScreenshotService.getDialogElements(base64Image).pipe(
      switchMap(({ result }) => {
        const { boxes, labels, scores } = result;

        const dialogItems: DialogDetection[] = boxes.map((it, index) => {
          const x = it[0] < 0 ? 0 : it[0];
          const y = it[1] < 0 ? 0 : it[1];
          const w = it[2] - x;
          const h = it[3] - y;
          const base64 = helper.image_crop(base64Image, x, y, w, h) as string;

          return {
            bbox: it,
            type: labels[index],
            score: scores[index],
            base64,
          };
        });

        return of({
          dialogItems,
          ...ctx,
          duration: Date.now() - startTime,
        });

        // if (dialogItems.length === 0) {
        //   return of({
        //     dialogItems,
        //     ...ctx,
        //     duration: Date.now() - startTime,
        //   });
        // }

        // return this.aiScreenshotService
        //   .getInpaintAsBase64(
        //     base64Image,
        //     dialogItems.map((it) => it.box),
        //     [],
        //   )
        //   .pipe(
        //     map(({ base64, duration }) => {
        //       return { ...ctx, dialogItems, duration: Date.now() - startTime };
        //     }),
        //   );
      }),
      catchError((error) => {
        this.logger.error(`获取弹框与浮层失败: ${error.message}`, {
          ...commonLogRecord,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 处理图像标注检测
   * @param ctx - 截图上下文对象，包含任务ID、用户ID、会话ID等信息
   * @returns Observable 包含处理后的元素标注信息和处理耗时
   * - elements: UIElement[] - 检测到的UI元素数组
   * - duration: number - 处理耗时(毫秒)
   * - 以及ctx中的其他属性
   *
   * @description
   * 该方法执行以下操作:
   * 1. 调用AI图像设计服务获取图像中的UI元素标注
   * 2. 将标注信息更新到数据库中
   * 3. 记录处理日志
   * 4. 返回包含标注元素的上下文对象
   *
   * @throws 当标注处理失败时抛出错误
   * @private
   */
  private processLabelDetection(ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId, dialogItems } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    return this.aiImageToDesignService.getLabelAsJson(ctx.base64Image).pipe(
      switchMap(({ elements }) => {
        // 判断识别到的弹窗元素
        const items = dialogItems.filter(
          (it) =>
            (it.score > 0.8 && ['center_popup', 'tip_popup', 'popup_element'].includes(it.type)) ||
            (it.type === 'floating_icon' && it.score > 0.7),
        );
        for (const item of items) {
          let isMatch = false;
          for (const ele of elements) {
            const scales = hasSignificantOverlap(item.bbox, ele.bbox);
            if (scales[0] > 0.9 && scales[1] > 0.9) {
              ele.dialogType = item.type;
              isMatch = true;
              break;
            } else if (item.type === 'floating_icon' && scales[0] > 0.6 && scales[1] > 0.98) {
              ele.dialogType = item.type;
              ele.bbox = item.bbox;
              isMatch = true;
              break;
            }
          }

          // 如果匹配元素失败
          if (!isMatch && item.type === 'center_popup') {
            elements.push({
              id: 'box_' + elements.length,
              type: '外框形状',
              bbox: item.bbox,
              dialogType: item.type,
            });
          }
        }

        // 将标注信息更新到数据库
        return this.aiScreenshotService.updateTaskLabelJson(taskId, JSON.stringify(elements)).pipe(
          map(() => ({
            ...ctx,
            elements,
            duration: Date.now() - startTime,
          })),
        );
      }),
      catchError((error) => {
        this.logger.error(`获取元素标注处理失败: ${error.message}`, {
          ...commonLogRecord,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 识别图标
   * @param base64Image 输入的base64格式图片
   * @param width 图片宽度
   * @param height 图片高度
   * @param ctx 截图上下文对象
   * @returns Observable 返回裁剪结果，包含:
   *  - base64Crop: 裁剪后的base64图片
   *  - cropItems: 裁剪项数组，每项包含id、矩阵和RLE编码
   *  - duration: 处理耗时(毫秒)
   *  - 以及ctx中的其他属性
   * @private
   */
  private processIconCropping(width: number, height: number, ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    return this.aiImageToDesignService.getCropAsBase64(ctx.base64Image, ctx.elements).pipe(
      map(({ base64, cropResult }) => {
        const cropItems: RleMatrix[] = cropResult.map((item) => {
          const transformedMatrix = reverseMatrix(rleArrayToMatrix(rleStringDecode(item.rle.counts), height, width));

          return {
            id: item.id,
            matrix: transformedMatrix,
            rle: item.rle,
          };
        });

        return {
          base64Crop: base64,
          cropItems,
          ...ctx,
          duration: Date.now() - startTime,
        };
      }),
      catchError((error) => {
        this.logger.error(`图标识别处理失败: ${error.message}`, {
          ...commonLogRecord,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 处理图像中的文本元素
   * @param ctx - 截图上下文对象，包含base64图片、元素列表等信息
   * @returns Observable 包含处理后的文本元素信息和处理耗时
   * - ocrResult: OCR识别结果
   * - textDetections: 有效的文本检测项
   * - textStyles: 文本样式信息
   * - elements: 更新后的元素列表
   * - duration: 处理耗时(毫秒)
   *
   * @description
   * 该方法执行以下操作:
   * 1. 对裁剪后的图像进行OCR文本识别
   * 2. 过滤有效的文本检测项，排除与图标重叠的文本
   * 3. 分析文本样式(粗体、斜体等)
   * 4. 计算文本颜色
   * 5. 将文本元素添加到元素列表中
   *
   * @throws 当文本处理失败时抛出错误
   * @private
   */
  private processText(ctx: Partial<ScreenshotContext>, width: number) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    // 使用扣除图标后的截图进行 ocr 识别
    return this.aiScreenshotService.getTextByOcr(ctx.base64Crop).pipe(
      switchMap(({ ocrResult, duration }) => {
        this.logger.log(`【图生UI】【5.1 OCR文本识别】`, {
          ...commonLogRecord,
          ocrResult,
          duration,
        });

        // 提取有效文本检测项
        const { textDetections, updatedElements } = this.filterValidTextDetections(ocrResult.TextDetections, ctx);

        const textBoxes = textDetections.map((item) => calculateTextBoundingBox(item));

        return this.aiScreenshotService.getTextWeight(ctx.base64Image, textBoxes).pipe(
          map(({ result, duration }) => {
            this.logger.log(`【图生UI】【5.2 计算有效文本和文本粗体判断】`, {
              ...commonLogRecord,
              result,
              duration,
            });

            const textStyles: UITextStyle[] = [];
            const filterTextDections = [];
            const invalidTextDections = [];

            result.predictions.forEach((item, index) => {
              if (item.type === 'Editable') {
                textStyles.push({
                  weight: item.font,
                  italic: item.italic === 'Yes',
                });
                filterTextDections.push(textDetections[index]);
              } else {
                invalidTextDections.push(textDetections[index]);
              }
            });

            this.logger.log(`【图生UI】【5.2 无效的文字】`, {
              ...commonLogRecord,
              textDetection: invalidTextDections,
            });

            return {
              ...ctx,
              ocrResult,
              textDetections: filterTextDections,
              textStyles,
              elements: updatedElements,
              duration: Date.now() - startTime,
            };
          }),
        );
      }),
      switchMap(({ ocrResult, textDetections, textStyles, elements }) => {
        // 处理包含单个字符坐标的文本
        const textBoxes = textDetections.map((item) =>
          item.WordCoordPoint.map((it) => calculateTextBox(it.WordCoordinate)),
        );

        return this.aiImageToDesignService.getTextColor(ctx.base64Image, textBoxes).pipe(
          map(({ result, duration }) => {
            this.logger.log(`【图生UI】【5.3 计算文本颜色】`, {
              ...commonLogRecord,
              result,
              duration,
            });

            const processElements = this.processTextColorResult(result, textDetections, textStyles, elements);

            // 处理文本框有可能存在超出图片宽度的情况
            for (let i = 0; i < processElements.length; i++) {
              const [minX, minY, maxX, maxY] = processElements[i].bbox;
              if (maxX > width) {
                processElements[i].bbox = [minX, minY, width, maxY];
              }
            }

            return {
              ...ctx,
              ocrResult,
              elements: processElements,
              duration: Date.now() - startTime,
            };
          }),
        );
      }),
      catchError((error) => {
        this.logger.error(`文本OCR识别和样式处理失败: ${error.message}`, {
          ...commonLogRecord,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 过滤有效文本检测项并分类
   * @param textDetections - OCR识别的原始文本检测结果
   * @param elements - 当前的UI元素列表
   * @returns 包含以下属性的对象:
   * - textDetections: 有效的文本检测项数组
   * - updatedElements: 更新后的元素列表
   * - offsetIndex: 文本元素ID的索引偏移量
   *
   * @description
   * 该方法执行以下操作:
   * 1. 过滤掉空文本
   * 2. 排除与图标元素重叠的文本
   * 3. 将没有详细坐标点的文本直接添加到元素列表
   * 4. 收集需要进一步处理的有效文本检测项
   *
   * @private
   */
  private filterValidTextDetections(
    textDetections: TextDetection[],
    ctx: Partial<ScreenshotContext>,
  ): { textDetections: TextDetection[]; updatedElements: UIElement[] } {
    const { userId, requestId, fileId, taskId, sessionId, elements } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    let offsetIndex = 0;
    const validTextDetections: TextDetection[] = [];
    const updatedElements = [...elements]; // 避免直接修改原数组

    // 图标元素
    const iconElements = elements.filter((it) => it.type === '图标');

    textDetections.forEach((item) => {
      const detectedText = item.DetectedText.trim();
      if (!detectedText) return;

      // 创建基础文本元素
      const baseBox: Omit<UIElement, 'id'> = {
        type: '文本',
        text: detectedText,
        bbox: calculateTextBoundingBox(item),
      };

      // 检查是否与图标重叠
      if (iconElements.some((icon) => isContainedUIElement(icon, { ...baseBox, id: '' }))) {
        return;
      }

      // OCR 识别会出现无坐标点的情况
      if (item.WordCoordPoint.length === 0) {
        this.logger.log(`【图生UI】【5.1 OCR文本识别-无坐标点】`, {
          ...commonLogRecord,
          textDetection: item,
        });
        updatedElements.push({ ...baseBox, id: `text_${offsetIndex++}` });
      } else {
        validTextDetections.push(item);
      }
    });

    return {
      textDetections: validTextDetections,
      updatedElements,
    };
  }

  /**
   * 处理文本颜色计算结果并生成最终元素
   * @param colorResults 颜色计算结果
   * @param textDetections 待处理的文本检测项
   * @param elements 基础元素列表
   * @param startOffset 起始索引偏移量
   * @returns 更新后的元素列表
   */
  private processTextColorResult(
    segments: TextColorSegment[][],
    textDetections: TextDetection[],
    textStyles: UITextStyle[],
    elements: UIElement[],
  ): UIElement[] {
    const updatedElements = [...elements];

    segments.forEach((textItems, detectionIndex) => {
      const detectedText = textDetections[detectionIndex];
      const textStyle = textStyles[detectionIndex];
      const baseId = `text_${detectionIndex}`;

      // 预计算整个文本块的边界框
      const [minX, minY, maxX, maxY] = calculateTextBoundingBox(detectedText);
      const textWidth = maxX - minX;

      // 跟踪前一个分段的结束位置
      let prevSegmentEndX = minX;

      textItems.forEach(({ ids, color }, itemIndex) => {
        const textContent = detectedText.DetectedText.substring(ids[0], ids[1] + 1);
        const wordCoordPoints = detectedText.WordCoordPoint.slice(ids[0], ids[1] + 1);

        // 计算当前分段的边界框
        const [segMinX, segMinY, segMaxX, segMaxY] = this.calculateBoundingBox(wordCoordPoints);

        const subIndex = textItems.length === 1 ? '' : `_${itemIndex}`;

        // 处理竖行文本
        if (this.isVerticalText(minX, minY, maxX, maxY, textContent)) {
          wordCoordPoints.forEach((point, charIndex) => {
            updatedElements.push({
              id: `${baseId}${subIndex}_${charIndex}`,
              type: '文本',
              textStyle: {
                color,
                ...textStyle,
              },
              text: textContent.substring(charIndex, charIndex + 1),
              bbox: calculateTextBox(point.WordCoordinate),
            });
          });
        } else {
          // 横排文本处理
          const startX = itemIndex === 0 ? segMinX : prevSegmentEndX;
          const endX = itemIndex === textItems.length - 1 ? minX + textWidth : segMaxX;
          updatedElements.push({
            id: `${baseId}${subIndex}`,
            type: '文本',
            textStyle: {
              color,
              ...textStyle,
            },
            text: textContent,
            bbox: [startX, segMinY, endX, segMaxY],
          });
        }

        prevSegmentEndX = segMaxX;
      });
    });

    return updatedElements;
  }

  /**
   * 计算多个坐标点的边界框
   * @param wordCoordPoints 单词坐标点数组
   * @returns [minX, minY, maxX, maxY]
   */
  private calculateBoundingBox(
    wordCoordPoints: {
      WordCoordinate: DetectionPosition[];
    }[],
  ): [number, number, number, number] {
    return wordCoordPoints.reduce(
      ([minX, minY, maxX, maxY], word) => [
        Math.min(minX, ...word.WordCoordinate.map((coord) => coord.X)),
        Math.min(minY, ...word.WordCoordinate.map((coord) => coord.Y)),
        Math.max(maxX, ...word.WordCoordinate.map((coord) => coord.X)),
        Math.max(maxY, ...word.WordCoordinate.map((coord) => coord.Y)),
      ],
      [Infinity, Infinity, -Infinity, -Infinity],
    );
  }

  /**
   * 判断是否为竖排文本
   * @param minX 最小X坐标
   * @param minY 最小Y坐标
   * @param maxX 最大X坐标
   * @param maxY 最大Y坐标
   * @param textContent 文本内容
   * @returns 是否为竖排
   */
  private isVerticalText(minX: number, minY: number, maxX: number, maxY: number, textContent: string): boolean {
    const width = maxX - minX;
    const height = maxY - minY;
    return width < height && textContent.length > 1;
  }

  /**
   * 处理图像中的UI元素分组
   * @param base64Image - 原始图像的base64编码字符串
   * @param width - 图像宽度
   * @param height - 图像高度
   * @param ctx - 截图上下文对象（包含任务ID、元素列表等信息）
   * @returns Observable 包含处理后的元素分组信息（SOM项目、预处理元素等）及处理耗时
   */
  private processGroupElements(width: number, height: number, ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    const elements = ctx.elements || [];
    const cropItems = ctx.cropItems || [];

    // 注：对元素进行分类，singleElements 元素不参与编组运算
    const [singleElements, groupElements] = elements.reduce(
      ([single, group], box) => {
        if (box.type === 'ios安全区域小黑条') {
          single.push(box);
        } else {
          group.push(box);
        }
        return [single, group];
      },
      [[], []] as [UIElement[], UIElement[]],
    );

    // 注：根据元素的空间关系预处理UI元素
    const prepareElements = this.aiScreenshotService.prepareUIElements(groupElements, width, height, cropItems);

    return this.aiScreenshotService
      .getBatchSomAsBase64(ctx.base64Image, width, height, {
        ...ctx,
        prepareElements,
      })
      .pipe(
        switchMap(async ({ items }) => {
          return {
            somItems: items,
            groupElements,
            prepareElements,
            ...ctx,
            duration: Date.now() - startTime,
          };
        }),
        catchError((error) => {
          this.logger.error(`获取编组图片失败: ${error.message}`, {
            ...commonLogRecord,
            stack: error.stack,
          });
          return throwError(() => error);
        }),
      );
  }

  /**
   * 处理截图分组分析
   *
   * @description
   * 该方法处理截图的分组分析流程,包括:
   * 1. 创建中断控制器
   * 2. 调用AI服务进行截图分组分析
   * 3. 保存分析结果到数据库
   * 4. 解析AI返回的分组信息
   *
   * @param ctx - 截图上下文信息,包含somItems(截图项)、prepareElements(预处理元素)和sessionId
   * @returns Observable<{groups: ScreenshotSegment[], duration: number}> - 返回分组分析结果和处理时长
   *
   * @private
   */
  private processGroupAnalysis(ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId, somItems } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    // 中断控制器
    const abort = this.aiStreamService.createStream(sessionId);
    // 大模型ID
    const modelId = 'claude-3-7-sonnet-20250219';

    return from(somItems).pipe(
      concatMap(({ url, id, children }) => {
        return from(
          this.aiScreenshotService.getScreenShotGroup({
            originUrl: ctx.screenshotUrl,
            url,
            modelId,
            platform: 'venus',
            userId,
            abort,
            requestId,
          }),
        ).pipe(
          switchMap(async (result) => {
            const parentId = createIdGenerator({ size: 36 })();

            // 处理编组信息
            const text = extractLastMatchedByRegex(result.text);

            await this.prisma.ai_chat_message.create({
              data: {
                id: parentId,
                session_id: sessionId,
                content: PROMPT_TEMPLATE.ScreenShot_Element_Group,
                role: 'USER',
                ai_model_id: modelId,
                created_user: userId,
                updated_user: userId,
                file_count: 1,
                status: MessageStatus.COMPLETE,
              },
            });

            await this.prisma.ai_chat_message_file.create({
              data: {
                id: createIdGenerator({ size: 36 })(),
                message_id: parentId,
                name: getFileNameFromUrl(url),
                path: url,
                file_type: 'image/png',
                created_user: userId,
                updated_user: userId,
                status: 0,
              },
            });

            await this.prisma.ai_chat_message.create({
              data: {
                id: createIdGenerator({ size: 36 })(),
                session_id: sessionId,
                content: result.text,
                role: 'ASSISTANT',
                ai_model_id: modelId,
                parent_id: parentId,
                created_user: userId,
                updated_user: userId,
                status: MessageStatus.COMPLETE,
              },
            });

            const groups = parseGroupsFromText(text) as any;

            const segment: ScreenshotSegment2 = {
              groups,
              url,
              id,
              children,
            };

            return segment;
          }),
        );
      }),
      reduce((acc, cur) => [...acc, cur], [] as ScreenshotSegment2[]),
      map((segments) => {
        return {
          segments,
          ...ctx,
          duration: Date.now() - startTime,
        };
      }),
      catchError((error) => {
        this.logger.error(`获取元素编组信息失败: ${error.message}`, {
          ...commonLogRecord,
          stack: error.stack,
          responseBody: error.responseBody,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * 处理最终的图像修复操作
   *
   * @param base64Image - 输入的base64格式图片
   * @param ctx - 截图上下文对象
   * @returns Observable 包含修复后的base64图片和上下文信息
   *
   * @description
   * 该方法执行以下操作:
   * 1. 从上下文中提取文本元素和裁剪项
   * 2. 过滤出文本类型的元素并获取其边界框
   * 3. 获取所有裁剪项的RLE掩码
   * 4. 调用AI服务进行图像修复
   * 5. 返回修复后的图片和处理时长
   */
  private processFinalInpainting(ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId, base64Image, groupElements, cropItems } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    const textBoxes = groupElements.filter((it) => it.type === '文本').map((it) => it.bbox);
    const masks = cropItems.map((item) => item.rle);

    // 注：扣除图像中的图标和文字
    return this.aiImageToDesignService.getInpaintAsBase64(base64Image, textBoxes, masks).pipe(
      map(({ base64 }) => ({
        ...ctx,
        base64Crop: base64,
      })),
      switchMap((ctx) => {
        return this.aiScreenshotService.updateTaskResult(ctx.taskId, JSON.stringify(ctx.prepareElements)).pipe(
          map(() => ({
            ...ctx,
            duration: Date.now() - startTime,
          })),
        );
      }),
      catchError((error) => {
        this.logger.error(`文字抠图填色失败: ${error.message}`, {
          ...commonLogRecord,
          stack: error.stack,
        });
        return throwError(() => error);
      }),
    );
  }

  private processBackgroundImage(width: number, height: number, ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId, base64Image, base64Crop, cropItems } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    timer(1000)
      .pipe(
        switchMap(() => this.processIcon(base64Image, cropItems, ctx)),
        catchError((error) => {
          this.logger.error(`图转UI任务-处理图标失败: ${error.message}`, {
            ...commonLogRecord,
            stack: error.stack,
          });
          return of({ count: 0, duration: 0 });
        }),
      )
      .subscribe();

    // 元素后处理
    ctx.elements = this.aiScreenshotService.postUIElements(ctx);

    timer(1000)
      .pipe(
        map(() => this.aiScreenshotService.computeBackground(base64Crop, width, height, ctx)),
        catchError((error) => {
          this.logger.error(`图转UI任务-处理背景图失败: ${error.message}`, {
            ...commonLogRecord,
            stack: error.stack,
          });
          return of({ count: 0, duration: 0 });
        }),
      )
      .subscribe();

    return {
      ...ctx,
      duration: Date.now() - startTime,
    };
  }

  /**
   * 处理图标数据并上传到对象存储
   * @param base64Image - 图标的 base64 编码字符串
   * @param cropItems - 需要裁剪的图标矩阵数组
   * @param ctx - 截图上下文信息
   * @returns Observable 包含处理结果:
   *          - count: 成功上传的图标数量
   *          - duration: 处理耗时(毫秒)
   *
   * 该方法会:
   * 1. 将 base64 图片按矩阵信息裁剪成多个图标
   * 2. 并发上传图标到对象存储(最大并发数5)
   * 3. 将上传成功的图标信息保存到数据库
   */
  private processIcon(base64Image: string, cropItems: RleMatrix[], ctx: Partial<ScreenshotContext>) {
    const startTime = Date.now();

    const { userId, requestId, fileId, taskId, sessionId } = ctx;
    const commonLogRecord = { userId, requestId, fileId, taskId, sessionId };

    const base64Str = removeBase64Prefix(base64Image);
    const cropIcons: UIIcon[] = cropItems.map((it) => {
      return helper.image_new(base64Str, it.matrix, it.id);
    });

    // 并发数
    const CONCURRENCY_LIMIT = 5;

    return from(cropIcons).pipe(
      mergeMap(
        (item) =>
          this.aiScreenshotService.uploadToCos(item.base64, ctx).pipe(
            map(({ key }) => ({
              ...item,
              url: key,
            })),
            catchError(() => {
              return of(null);
            }),
          ),
        CONCURRENCY_LIMIT,
      ),
      toArray(),
      switchMap((items) => {
        // 提前过滤掉上传失败的项目
        if (items.length === 0) {
          console.warn('No icons processed successfully');
          return of({ count: 0, duration: Date.now() - startTime });
        }

        this.logger.log(`【图生UI】【图标数据处理完成】`, {
          ...commonLogRecord,
          items: items.map((it) => {
            return {
              id: it.id,
              url: it.url,
            };
          }),
        });

        const dbRecords = items.map((item) => ({
          id: createIdGenerator({ size: 36 })(),
          task_id: taskId,
          element_id: item.id,
          path: item.url,
          bbox: JSON.stringify({ bbox: item.bbox }),
          resource_type: 0,
          created_user: userId,
          updated_user: userId,
          status: 0,
        }));

        return from(
          this.prisma.ai_task_image_to_ui_resources.createMany({
            data: dbRecords,
            skipDuplicates: true,
          }),
        ).pipe(
          map(({ count }) => {
            return {
              count,
              duration: Date.now() - startTime,
            };
          }),
        );
      }),
      catchError((error) => {
        return throwError(() => error);
      }),
    );
  }
}
