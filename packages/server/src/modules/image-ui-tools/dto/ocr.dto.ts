import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class OcrDto {
  @ApiProperty({
    description: '图片的base64编码字符串',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: '图片base64编码不能为空' })
  @IsString({ message: '图片base64编码必须是字符串' })
  public imageBase64: string;

  @ApiProperty({
    description: 'API类型，支持high_quality(高质量)、advertise(广告)、basic(基础)',
    type: String,
    required: false,
    default: 'high_quality',
  })
  @IsOptional()
  @IsString({ message: 'API类型必须是字符串' })
  public apiType?: string = 'high_quality';

  @ApiProperty({
    description: '是否按词返回结果，true表示按词返回，false表示按行返回',
    type: Boolean,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'isWords必须是布尔值' })
  public isWords?: boolean = true;

  @ApiProperty({
    description: '是否开启单字分割功能，用于提高识别准确率',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'enableDetectSplit必须是布尔值' })
  public enableDetectSplit?: boolean = false;
}
