import { ApiProperty, ApiPropertyOptional, getSchemaPath } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class ExtractColorDto {
  @ApiProperty({
    description: '图片的base64编码字符串',
    type: String,
    required: true,
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...',
  })
  @IsNotEmpty({ message: '图片base64编码不能为空' })
  @IsString({ message: '图片base64编码必须是字符串' })
  public imageBase64: string;

  @ApiPropertyOptional({
    description: 'OCR识别的文字边界框数组，每个元素包含坐标 [x1, y1, x2, y2]',
    type: Array,
    required: false,
    example: [
      [
        [100, 50, 200, 80],
        [210, 50, 300, 80],
      ],
    ],
  })
  @IsOptional()
  @IsArray({ message: 'OCR边界框必须是数组格式' })
  public ocrBoxes?: number[][][];
}

// 定义基本的颜色组类型
export class ColorGroup {
  @ApiProperty({
    description: '颜色的十六进制代码',
    example: '#FF5733',
  })
  color: string;

  @ApiProperty({
    description: '颜色组的起始和结束索引',
    example: [0, 2],
  })
  ids: number[];
}

// 使用 SchemaFactory 创建嵌套数组模式
export class ExtractColorResponseDto {
  static schema() {
    return {
      type: 'array',
      items: {
        type: 'array',
        items: {
          $ref: getSchemaPath(ColorGroup),
        },
      },
    };
  }
}
