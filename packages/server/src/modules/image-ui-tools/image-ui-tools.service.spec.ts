import config from '@config/config';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import fs from 'fs/promises';
import path from 'path';

import testData from '../../../test/image-ui-tools/image_wise_tests.json';
import { ImageUIToolsService } from './image-ui-tools.service';

const data1 = testData[0];
const baseUrl = '../../../test/image-ui-tools';
const imgPath = path.resolve(__dirname, baseUrl, data1.image_path);
const outputDir = path.resolve(__dirname, baseUrl, 'output');

async function imgToBase64(path: string) {
  const buf = await fs.readFile(path);
  return buf.toString('base64');
}

async function writeJSON(data, filePath) {
  await fs.mkdir(outputDir, { recursive: true });
  await fs.writeFile(path.resolve(outputDir, filePath), data);
}

const ocrBoxes = [
  [
    [458, 478, 494, 515], // "搜"
    [495, 478, 531, 515], // "字"
    [533, 478, 569, 515], // "词"
  ],
  [
    [93, 773, 147, 831], // "作"
    [147, 773, 199, 830], // "业"
    [199, 774, 251, 831], // "批"
    [253, 775, 307, 831], // "改"
  ],
  [
    [374, 1862, 399, 1899], // "5"
    [402, 1862, 432, 1899], // "9"
    [439, 1855, 486, 1904], // "元"
  ],
];

describe('ImageUIToolsService', () => {
  let service: ImageUIToolsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule.forRoot({ isGlobal: true, load: [config] }), HttpModule],
      providers: [ImageUIToolsService],
    }).compile();

    service = module.get<ImageUIToolsService>(ImageUIToolsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('ocr', () => {
    const mockOcrDto = {
      isWords: true,
      enableDetectSplit: true,
    };
    it('should call OCR service and return response', async () => {
      const imageBase64 = await imgToBase64(imgPath);
      const result = await service.ocr({ ...mockOcrDto, imageBase64 });
      await writeJSON(JSON.stringify(result), 'ocr.json');
      expect(result).toBeDefined();
    });
  });
});

describe('ExtractColorService', () => {
  let service: ImageUIToolsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule.forRoot({ isGlobal: true, load: [config] }), HttpModule],
      providers: [ImageUIToolsService],
    }).compile();

    service = module.get<ImageUIToolsService>(ImageUIToolsService);
    // 手动调用 onModuleInit
    if (service.onModuleInit) {
      await service.onModuleInit();
    }
  });

  it('should extract colors from valid input', async () => {
    const mockDto = {
      imageBase64: await imgToBase64(imgPath),
      ocrBoxes: ocrBoxes,
    };

    const result = await service.extractColor(mockDto);
    console.dir(JSON.stringify(result));
    expect(result).toBeDefined();
    expect(Array.isArray(result)).toBe(true);
  });
});
