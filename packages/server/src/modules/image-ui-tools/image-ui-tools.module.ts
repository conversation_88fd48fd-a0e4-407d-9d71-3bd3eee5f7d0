import { HttpModule } from '@nestjs/axios';
import { Lo<PERSON>, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { ImageUIToolsController } from './image-ui-tools.controller';
import { ImageUIToolsService } from './image-ui-tools.service';

@Module({
  imports: [ConfigModule, HttpModule],
  providers: [Logger, ImageUIToolsService],
  controllers: [ImageUIToolsController],
})
export class ImageUIToolsModule {}
