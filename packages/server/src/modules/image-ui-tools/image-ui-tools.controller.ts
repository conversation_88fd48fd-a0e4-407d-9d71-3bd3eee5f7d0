import { Body, Controller, Logger, Post } from '@nestjs/common';
import { ApiBody, ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ColorGroup, ExtractColorDto, ExtractColorResponseDto } from './dto/extract-color.dto';
import { OcrDto } from './dto/ocr.dto';
import { ImageUIToolsService } from './image-ui-tools.service';

@ApiTags('图生成 UI 工具')
@Controller('image-ui-tools')
export class ImageUIToolsController {
  constructor(private readonly imageUIToolsService: ImageUIToolsService) {}
  private readonly logger = new Logger(ImageUIToolsController.name);

  @ApiOperation({
    description: 'ocr',
  })
  @ApiBody({
    description: 'ocr对象',
    required: true,
    isArray: false,
    type: OcrDto,
  })
  @Post('/ocr')
  create(@Body() dto: OcrDto) {
    return this.imageUIToolsService.ocr(dto);
  }

  @ApiExtraModels(ColorGroup)
  @ApiResponse({
    status: 200,
    schema: ExtractColorResponseDto.schema(),
  })
  @Post('/extract_color')
  async extractColor(@Body() extractColorDto: ExtractColorDto) {
    return this.imageUIToolsService.extractColor(extractColorDto);
  }
}
