import { ImageUIToolsConfig } from '@config/config.interface';
import { Injectable, InternalServerErrorException, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import cvReadyPromise, { CV, Mat } from '@techstark/opencv-js';
import sharp from 'sharp';
import { ClientConfig, CommonClient } from 'tencentcloud-sdk-nodejs-common';

import { ExtractColorDto } from './dto/extract-color.dto';
import { OcrDto } from './dto/ocr.dto';
import {
  analyzeColorDelta,
  base64ToImageBuffer,
  calculateMaskedMean,
  createMatFromSharpData,
  expandBoundingBox,
  extractSubImageData,
  generateColorResults,
  getMaxConnectedArea,
} from './utils';

@Injectable()
export class ImageUIToolsService implements OnModuleInit {
  private clientConfig: ClientConfig;
  private ocrClient: CommonClient;

  constructor(private readonly configService: ConfigService) {
    const config = this.configService.get<ImageUIToolsConfig>('imageUITools');
    if (!config?.OCR_SECRET_ID || !config?.OCR_SECRET_KEY || !config.OCR_REGION) {
      throw new InternalServerErrorException('图生成 UI 工具配置，请检查环境变量');
    }

    this.clientConfig = {
      credential: {
        secretId: config.OCR_SECRET_ID,
        secretKey: config.OCR_SECRET_KEY,
      },
      region: config.OCR_REGION,
    };

    this.initOCRClient();
  }

  async onModuleInit() {
    await this.initializeOpenCV();
  }

  private readonly logger = new Logger(ImageUIToolsService.name);

  private initOCRClient() {
    this.ocrClient = new CommonClient('ocr.tencentcloudapi.com', '2018-11-19', this.clientConfig);
  }

  async ocr(dto: OcrDto) {
    const { imageBase64, isWords, enableDetectSplit } = dto;
    const params = {
      ImageBase64: imageBase64,
      IsWords: isWords,
      EnableDetectSplit: enableDetectSplit,
    };
    return this.ocrClient.request('GeneralAccurateOCR', params);
  }

  private cv: CV;
  private async initializeOpenCV(): Promise<void> {
    this.cv = await cvReadyPromise;
    this.logger.log('OpenCV initialized successfully');
  }

  private readonly COLOR_DELTA_THRES = 12;

  async extractColor(dto: ExtractColorDto): Promise<Array<Array<{ color: string; ids: number[] }>>> {
    const { imageBase64, ocrBoxes } = dto;
    const results: any[] = [];

    // 使用 Sharp 替代 PIL.Image 处理
    const imageBuffer = base64ToImageBuffer(imageBase64);
    const { data, info } = await sharp(imageBuffer).removeAlpha().raw().toBuffer({ resolveWithObject: true });

    const imgData = new Uint8Array(data);
    const { width, height } = info;
    const cv = this.cv;

    for (const locations of ocrBoxes) {
      const foreColors: number[][] = [];

      for (const location of locations) {
        // 使用 utils 函数扩展边界框
        const [x1, y1, x2, y2] = expandBoundingBox(location[0], location[1], location[2], location[3], width, height);

        // 使用 utils 函数提取子图像数据
        const subImgData = extractSubImageData(imgData, width, x1, y1, x2, y2);
        const subWidth = x2 - x1;
        const subHeight = y2 - y1;

        // 使用 utils 函数创建 OpenCV Mat
        const subImg = createMatFromSharpData(cv, subImgData, subWidth, subHeight);

        const grayImg = new cv.Mat();
        cv.cvtColor(subImg, grayImg, cv.COLOR_RGB2GRAY);

        const th = new cv.Mat();
        cv.threshold(grayImg, th, 0, 255, cv.THRESH_BINARY + cv.THRESH_OTSU);

        const thInverted = new cv.Mat();
        cv.bitwise_not(th, thInverted);

        // 使用 utils 函数获取最大连通区域
        const maxAreaTh = getMaxConnectedArea(cv, th);
        const maxAreaThInverted = getMaxConnectedArea(cv, thInverted);

        let foreTh: Mat;
        if (maxAreaTh > maxAreaThInverted) {
          foreTh = thInverted.clone();
        } else {
          foreTh = th.clone();
        }

        const kernel = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(2, 2));
        const erodedForeTh = new cv.Mat();
        cv.erode(foreTh, erodedForeTh, kernel, new cv.Point(-1, -1), 1);

        // 使用 utils 函数计算掩码平均值
        const foreColor = calculateMaskedMean(subImg, erodedForeTh, 'median');
        foreColors.push(foreColor);

        // 清理内存
        subImg.delete();
        grayImg.delete();
        th.delete();
        thInverted.delete();
        foreTh.delete();
        kernel.delete();
        erodedForeTh.delete();
      }

      // 使用 utils 函数进行颜色差异分析
      const colorIndex = analyzeColorDelta(foreColors, this.COLOR_DELTA_THRES);

      // 使用 utils 函数生成最终结果
      const colorWithIds = generateColorResults(foreColors, colorIndex);

      results.push(colorWithIds);
    }

    return results;
  }
}
