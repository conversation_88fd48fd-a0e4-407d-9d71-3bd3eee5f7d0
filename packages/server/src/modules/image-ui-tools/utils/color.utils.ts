import * as colorDiff from 'color-diff';
import * as ss from 'simple-statistics';

/**
 * Convert base64 string to image buffer
 * 对应 Python: base64_to_image(image_base64)
 */
export function base64ToImageBuffer(base64String: string): Buffer {
  // Remove data URL prefix if present
  const base64Data = base64String.replace(/^data:image\/[a-z]+;base64,/, '');
  return Buffer.from(base64Data, 'base64');
}

/**
 * Calculate color difference using color-diff library
 * 对应 Python: deltaE_ciede2000(fore_colors[i-1], fore_colors[i])
 */
export function calculateColorDelta(color1: number[], color2: number[]): number {
  // 将 RGB 数组转换为 color-diff 库需要的格式
  const rgb1 = { R: color1[0], G: color1[1], B: color1[2] };
  const rgb2 = { R: color2[0], G: color2[1], B: color2[2] };

  // 使用 color-diff 库计算颜色差异
  return colorDiff.diff(rgb1, rgb2);
}

/**
 * Calculate trimmed mean using simple-statistics
 */
export function trimMean(values: number[], trimPercent: number): number {
  if (values.length === 0) return 0;

  const sorted = [...values].sort((a, b) => a - b);
  const trimCount = Math.floor((sorted.length * trimPercent) / 2);
  const trimmed = sorted.slice(trimCount, sorted.length - trimCount);

  return ss.mean(trimmed);
}

/**
 * Calculate median using simple-statistics
 */
export function median(values: number[]): number {
  if (values.length === 0) return 0;
  return ss.median(values);
}

/**
 * Calculate mode using simple-statistics
 */
export function mode(values: number[]): number {
  if (values.length === 0) return 0;
  return ss.mode(values);
}

/**
 * Calculate mean using simple-statistics
 */
export function mean(values: number[]): number {
  if (values.length === 0) return 0;
  return ss.mean(values);
}

/**
 * Convert RGB array to hex string
 * 对应 Python: hex_color = "#{:02X}{:02X}{:02X}".format(color[0], color[1], color[2])
 */
export function rgbToHex(rgb: number[]): string {
  // 确保值是整数并在 0-255 范围内
  const [r, g, b] = rgb.map((val) => Math.max(0, Math.min(255, Math.round(val))));

  // 使用与 Python 完全相同的格式化方式
  return `#${r.toString(16).padStart(2, '0').toUpperCase()}${g.toString(16).padStart(2, '0').toUpperCase()}${b.toString(16).padStart(2, '0').toUpperCase()}`;
}
