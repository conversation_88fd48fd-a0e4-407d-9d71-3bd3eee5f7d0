import { CV, Mat } from '@techstark/opencv-js';

import { mean, median, mode, trimMean } from './color.utils';

/**
 * OpenCV 相关的辅助函数
 */

/**
 * 将 Sharp 的 RGB 数据转换为 OpenCV Mat
 */
export function createMatFromSharpData(cv: CV, data: Uint8Array, width: number, height: number): Mat {
  const mat = new cv.Mat(height, width, cv.CV_8UC3);
  mat.data.set(data);
  return mat;
}

/**
 * 从 Sharp 数据中提取子图像数据
 */
export function extractSubImageData(
  imgData: Uint8Array,
  imgWidth: number,
  x1: number,
  y1: number,
  x2: number,
  y2: number,
): Uint8Array {
  const subWidth = x2 - x1;
  const subHeight = y2 - y1;
  const subData = new Uint8Array(subWidth * subHeight * 3);

  let subIndex = 0;
  for (let y = y1; y < y2; y++) {
    for (let x = x1; x < x2; x++) {
      const srcIndex = (y * imgWidth + x) * 3;
      subData[subIndex] = imgData[srcIndex]; // R
      subData[subIndex + 1] = imgData[srcIndex + 1]; // G
      subData[subIndex + 2] = imgData[srcIndex + 2]; // B
      subIndex += 3;
    }
  }

  return subData;
}

/**
 * 获取最大连通区域面积
 * 对应 Python: get_max_connected_area(mask)
 */
export function getMaxConnectedArea(cv: CV, mask: Mat): number {
  const labels = new cv.Mat();
  const stats = new cv.Mat();
  const centroids = new cv.Mat();

  const numLabels = cv.connectedComponentsWithStats(mask, labels, stats, centroids, 8);

  let maxArea = 0;
  for (let i = 1; i < numLabels; i++) {
    const width = stats.intAt(i, cv.CC_STAT_WIDTH);
    const height = stats.intAt(i, cv.CC_STAT_HEIGHT);
    const area = width * height;
    maxArea = Math.max(maxArea, area);
  }

  // 清理内存
  labels.delete();
  stats.delete();
  centroids.delete();

  return maxArea;
}

/**
 * 计算掩码区域的平均颜色
 * 对应 Python: calculate_masked_mean(image, mask, method='trim_mean')
 */
export function calculateMaskedMean(
  image: Mat,
  mask: Mat,
  method: 'mean' | 'trim_mean' | 'median' | 'mode' = 'median',
): number[] {
  const maskedPixels: number[][] = [];

  for (let y = 0; y < image.rows; y++) {
    for (let x = 0; x < image.cols; x++) {
      if (mask.ucharAt(y, x) === 255) {
        const pixel = image.ucharPtr(y, x);
        // Sharp 数据已经是 RGB 格式，直接使用
        maskedPixels.push([pixel[0], pixel[1], pixel[2]]);
      }
    }
  }

  if (maskedPixels.length === 0) {
    return [0, 0, 0];
  }

  const rValues = maskedPixels.map((p) => p[0]);
  const gValues = maskedPixels.map((p) => p[1]);
  const bValues = maskedPixels.map((p) => p[2]);

  let r: number, g: number, b: number;

  switch (method) {
    case 'mean':
      r = mean(rValues);
      g = mean(gValues);
      b = mean(bValues);
      break;
    case 'trim_mean':
      r = trimMean(rValues, 0.3);
      g = trimMean(gValues, 0.3);
      b = trimMean(bValues, 0.3);
      break;
    case 'median':
      r = median(rValues);
      g = median(gValues);
      b = median(bValues);
      break;
    case 'mode':
      r = mode(rValues);
      g = mode(gValues);
      b = mode(bValues);
      break;
    default:
      r = median(rValues);
      g = median(gValues);
      b = median(bValues);
  }

  return [r, g, b];
}
