import { calculateColorDelta, mean, median, rgbToHex } from './color.utils';

/**
 * 颜色分析相关的辅助函数
 */

/**
 * 颜色差异分析
 * 对应 Python: 颜色差异分析
 */
export function analyzeColorDelta(foreColors: number[][], colorDeltaThreshold: number = 12): number[][] {
  if (foreColors.length === 0) return [];

  const colorIndex: number[][] = [[0]];

  for (let i = 1; i < foreColors.length; i++) {
    const delta = calculateColorDelta(foreColors[i - 1], foreColors[i]);

    if (delta > colorDeltaThreshold) {
      colorIndex.push([i]);
    } else {
      colorIndex[colorIndex.length - 1].push(i);
    }
  }

  return colorIndex;
}

/**
 * 计算颜色平均值
 * 对应 Python: color = np.mean(colors, axis=0)
 */
export function calculateMeanColor(colors: number[][]): number[] {
  if (colors.length === 0) return [0, 0, 0];

  // 使用 simple-statistics 的 mean 函数分别计算每个通道的平均值
  const r = mean(colors.map((color) => color[0]));
  const g = mean(colors.map((color) => color[1]));
  const b = mean(colors.map((color) => color[2]));

  return [r, g, b];
}

/**
 * 计算颜色中位数
 * 对应 Python: color = np.median(colors, axis=0)
 */
export function calculateMedianColor(colors: number[][]): number[] {
  if (colors.length === 0) return [0, 0, 0];

  // 如果只有一个颜色，直接返回
  if (colors.length === 1) return colors[0];

  // 使用 simple-statistics 的 median 函数分别计算每个通道的中位数
  const r = median(colors.map((color) => color[0]));
  const g = median(colors.map((color) => color[1]));
  const b = median(colors.map((color) => color[2]));

  return [r, g, b];
}

/**
 * 生成颜色结果对象
 * 对应 Python:
 * colors = fore_colors[ids[0]:ids[-1]+1]
 * colors = np.array(colors)
 * color = np.mean(colors, axis=0)
 * color = np.round(color).astype(np.uint8)
 * color = np.clip(color, 0, 255)
 * hex_color = "#{:02X}{:02X}{:02X}".format(color[0], color[1], color[2])
 */
export function generateColorResults(foreColors: number[][], colorIndex: number[][]): any[] {
  return colorIndex.map((ids) => {
    // 提取颜色组 - 对应 Python: colors = fore_colors[ids[0]:ids[-1]+1]
    const startIdx = ids[0];
    const endIdx = ids[ids.length - 1];
    const colors = foreColors.slice(startIdx, endIdx + 1);

    // 计算平均值 (对应 Python: color = np.mean(colors, axis=0))
    const color = calculateMeanColor(colors);

    // 四舍五入到整数 (对应 np.round + astype(np.uint8))
    // 确保值在 0-255 范围内 (对应 np.clip)
    const clampedColor = color.map((c) => Math.max(0, Math.min(255, Math.round(c))));

    // 转换为十六进制 (对应 Python: "#{:02X}{:02X}{:02X}".format(color[0], color[1], color[2]))
    const hexColor = rgbToHex(clampedColor);

    return {
      color: hexColor,
      ids: [startIdx, endIdx],
    };
  });
}

/**
 * 扩展边界框
 */
export function expandBoundingBox(
  x1: number,
  y1: number,
  x2: number,
  y2: number,
  width: number,
  height: number,
  padding: number = 4,
): [number, number, number, number] {
  return [
    Math.max(0, x1 - padding),
    Math.max(0, y1 - padding),
    Math.min(width, x2 + padding),
    Math.min(height, y2 + padding),
  ];
}
