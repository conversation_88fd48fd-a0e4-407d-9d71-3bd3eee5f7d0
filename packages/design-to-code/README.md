# design-to-code

## 用法

### 获取截图
```ts
import { screenShot } from '@tencent/design-to-code';

const getScreenShotAsync = async (figmaNode: SceneNode) => {
  const screenshot = await screenShot.getScreenShotAsync(figmaNode);
  return screenshot;
}
```

### 将 figma 节点转换为 html 对象
```ts
import { DesignToCode } from '@tencent/design-to-code';
import type { DesignNodes } from '@tencent/design-to-code';

const nodeToHtml = new DesignToCode({
  screenShot: true, // 导出图片
});

async designToCode(nodes?: DesignNodes) {
  await figma.loadAllPagesAsync();
  const finalNodes = nodes || figma.currentPage.selection;
  if (finalNodes?.length === 0) {
    return {
      success: true,
      data: null,
    };
  }

  const htmlObject = await nodeToHtml.getHtmlObjectAsync(finalNodes as DesignNodes);
  const htmlString = await nodeToHtml.getHtmlString(htmlObject);

  return {
    success: true,
    data: {
      htmlObject,
      htmlString,
    },
  };
};

async htmlStringToObject(htmlString: string) {
  const htmlObject = nodeToHtml.htmlStringToObject(htmlString);

  return {
    success: true,
    data: htmlObject,
  };
}
```

### 将 figma 节点转换为 html 字符串
```ts
import nodeToHtml from '@tencent/design-to-code';
import type { DesignNodes } from '@tencent/design-to-code';

async getHtmlStringAsync(nodes?: DesignNodes) {
  try {
    const htmlString = await nodeToHtml.getHtmlAsync(nodes);
    return {
      success: true,
      data: htmlString,
    };
  } catch (error) {
    return {
      success: false,
      data: '',
      error,
    };
  }
}
```

### 自定义超时处理
```ts
import { DesignToCode } from '@tencent/design-to-code';
import type { DesignNodes } from '@tencent/design-to-code';

const nodeToHtml = new DesignToCode({
  screenShot: true, // 导出图片
  timeout: 5000, // 超时的时间，毫秒单位，默认为 0 ，表示不需要处理超时
  // 超时导出时的自定义占位图，非必须，没有传入时，以内部的自定义占位图方法来处理
  placeholder: (node: SceneNode) => {
    const box = node.absoluteBoundingBox;
    if (box?.width && box?.height) {
      return `https://placehold.co/${box.width}x${box.height}`;
    }
    return '';
  },
});

async designToCode(nodes?: DesignNodes) {
  await figma.loadAllPagesAsync();
  const finalNodes = nodes || figma.currentPage.selection;
  if (finalNodes?.length === 0) {
    return {
      success: true,
      data: null,
    };
  }

  const htmlObject = await nodeToHtml.getHtmlObjectAsync(finalNodes as DesignNodes);
  const htmlString = await nodeToHtml.getHtmlString(htmlObject);

  return {
    success: true,
    data: {
      htmlObject,
      htmlString,
    },
  };
};
```