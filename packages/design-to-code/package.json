{"name": "@tencent/design-to-code", "version": "0.3.0", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "description": "本项目是腾讯 AI 助手项目的一部分,专注于提供高质量的设计稿转换为代码的能力", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "scripts": {"dev": "tsup", "build": "tsup --env.NODE_ENV production", "build-publish": "pnpm run build && pnpm publish"}, "files": ["dist", "README.md"], "author": "", "license": "ISC", "devDependencies": {"@figma/plugin-typings": "1.114.0", "tsup": "^8.4.0", "typescript": "^5.8.3"}, "dependencies": {"htmlparser2": "^10.0.0"}}