import type { DesignToCodeSetting } from '../interface';
import TimeoutUtil from '../_utils/timeoutUtil';

class ScreenShot {
  public constructor() {}
  getScreenShotAsync = async (node: SceneNode) => {
    const imageData = await node.exportAsync({
      format: 'PNG',
      constraint: { type: 'SCALE', value: 1 },
      useAbsoluteBounds: true,
    });
    const base64 = figma.base64Encode(imageData);
    const screenshot = `data:image/png;base64,${base64}`;
    return screenshot;
  };
  getScreenShotAsyncTimeout = async (node: SceneNode, setting: DesignToCodeSetting): Promise<string> => {
    return await TimeoutUtil.main(async () => {
      try {
        const imgSrc = await this.getScreenShotAsync(node);
        return imgSrc;
      } catch (error) {
        return ''; 
      }
    }, () => {
      try {
        return setting.placeholder?.(node);
      } catch (error) {
        return '';
      }
    }, setting.timeout);
  }
}

const screenShot = new ScreenShot();
export default screenShot;
