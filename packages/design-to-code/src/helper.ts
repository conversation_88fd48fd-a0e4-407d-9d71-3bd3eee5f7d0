import fillsConverter from './_utils/figma-fills-converter';

type MixFields = keyof Omit<StyledTextSegment, 'characters' | 'start' | 'end'>;

export const TextNodeMixFields = [
  'fontSize',
  'fontName',
  'fontWeight',
  'lineHeight',
  'letterSpacing',
  'textCase',
  'textDecoration',
  'textDecoration',
  'textDecorationStyle',
  'textDecorationOffset',
  'textDecorationThickness',
  'textDecorationColor',
  'textDecorationSkipInk',
  'fills',
] as MixFields[];

export function getCssStyleFromStyledSegment(
  segment: Pick<StyledTextSegment, MixFields | 'characters' | 'start' | 'end'>,
) {
  const cssStyle: Record<string, string> = {};

  // 字体大小
  if (segment.fontSize !== undefined) {
    cssStyle['font-size'] = `${segment.fontSize}px`;
  }

  // 字体族和字重
  if (segment.fontName) {
    cssStyle['font-family'] = `"${segment.fontName.family}"`;

    // 从字体样式中提取字重和样式
    const style = segment.fontName.style.toLowerCase();
    const fontWeightMap: Record<string, string> = {
      bold: 'bold',
      light: '300',
      medium: '500',
      regular: 'normal',
      semibold: '600',
      black: '900',
    };
    if (fontWeightMap[style]) {
      cssStyle['font-weight'] = fontWeightMap[style];
    }

    if (style.includes('italic')) {
      cssStyle['font-style'] = 'italic';
    }
  }

  // 字重（如果单独设置）
  if (segment.fontWeight !== undefined) {
    cssStyle['font-weight'] = segment.fontWeight.toString();
  }

  // 行高
  if (segment.lineHeight) {
    if (segment.lineHeight.unit === 'PIXELS') {
      cssStyle['line-height'] = `${segment.lineHeight.value}px`;
    } else if (segment.lineHeight.unit === 'PERCENT') {
      cssStyle['line-height'] = (segment.lineHeight.value / 100).toString();
    } else if (segment.lineHeight.unit === 'AUTO') {
      cssStyle['line-height'] = 'normal';
    }
  }

  // 字间距
  if (segment.letterSpacing) {
    if (segment.letterSpacing.unit === 'PIXELS') {
      cssStyle['letter-spacing'] = `${segment.letterSpacing.value}px`;
    } else if (segment.letterSpacing.unit === 'PERCENT') {
      cssStyle['letter-spacing'] = `${segment.letterSpacing.value}%`;
    }
  }

  // 文本大小写
  if (segment.textCase) {
    const textCaseMap: Record<TextCase, string> = {
      UPPER: 'uppercase',
      LOWER: 'lowercase',
      TITLE: 'capitalize',
      SMALL_CAPS: 'small-caps',
      SMALL_CAPS_FORCED: 'small-caps',
      ORIGINAL: 'none',
    };
    cssStyle['text-transform'] = textCaseMap[segment.textCase] || 'none';
  }

  // 文本装饰
  if (segment.textDecoration) {
    if (segment.textDecoration === 'STRIKETHROUGH') {
      cssStyle['text-decoration-line'] = 'line-through';
    } else if (segment.textDecoration === 'UNDERLINE') {
      cssStyle['text-decoration-line'] = 'underline';
    } else {
      cssStyle['text-decoration-line'] = 'none';
    }
  }

  if (segment.textDecorationStyle) {
    cssStyle['text-decoration-style'] = segment.textDecorationStyle.toLowerCase();
  }

  if (segment.textDecorationOffset) {
    if (segment.textDecorationOffset.unit === 'PIXELS') {
      cssStyle['text-underline-offset'] = `${segment.textDecorationOffset.value}px`;
    } else if (segment.textDecorationOffset.unit === 'PERCENT') {
      cssStyle['text-underline-offset'] = (segment.textDecorationOffset.value / 100).toString();
    } else if (segment.textDecorationOffset.unit === 'AUTO') {
      cssStyle['text-underline-offset'] = 'auto';
    }
  }

  if (segment.textDecorationThickness) {
    if (segment.textDecorationThickness.unit === 'PIXELS') {
      cssStyle['text-decoration-thickness'] = `${segment.textDecorationThickness.value}px`;
    } else if (segment.textDecorationThickness.unit === 'PERCENT') {
      cssStyle['text-decoration-thickness'] = (segment.textDecorationThickness.value / 100).toString();
    } else if (segment.textDecorationThickness.unit === 'AUTO') {
      cssStyle['text-decoration-thickness'] = 'auto';
    }
  }

  if (segment.textDecorationColor) {
    const { value } = segment.textDecorationColor;
    let color = 'currentColor';
    if (value !== 'AUTO') {
      const [res] = fillsConverter.convertFills([value]);
      color = res?.type === 'SOLID' ? res.value : 'inherit';
    }
    cssStyle['text-decoration-color'] = color;
  }

  if (segment.textDecorationSkipInk !== undefined) {
    cssStyle['text-decoration-skip-ink'] = segment.textDecorationSkipInk ? 'auto' : 'none';
  }

  // 填充颜色
  if (segment.fills && segment.fills.length > 0) {
    const color = convertTextFillToCSS(segment.fills);
    cssStyle.color = color;
  }

  return cssStyle;
}

// 多个填充取最底下作为颜色
// @TODO 混合模式和渐变
export function convertTextFillToCSS(fills: readonly Paint[]) {
  const res = fillsConverter.convertFills(fills).filter((val) => val.type === 'SOLID');
  return res[res.length - 1]?.value || 'inherit';
}
