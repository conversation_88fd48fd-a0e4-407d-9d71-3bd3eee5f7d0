// 当前的 figma 打包机制中，遇到常规的 function 是 async 类型时会有报错，因此，采用 class 的方式来封装各类通用方法
import { DesignNodes } from '../detect-component-interface';
import { convertTextFillToCSS } from '../helper';
import type { DesignToCodeSetting, HtmlObject, LooseStyle, NodeProcessType } from '../interface';
import screenShot from '../screen-shot';

type ParamsType = {
  node: SceneNode;
  parentNode?: SceneNode;
  nodeProcess?: NodeProcessType;
  setting?: DesignToCodeSetting;
};

type SetFlexRelativeCssParams = {
  nodes: DesignNodes;
  parentNode?: SceneNode;
  htmlObject: HtmlObject;
};

class CSSUtils {
  static regs = {
    invalidCssVariable: /^var\(--,/,
  };

  static getTransformCss(node: SceneNode, isReverse = false) {
    if (!(node as any).rotation) {
      return '';
    }
    const rotation = isReverse ? -1 * (node as any).rotation : (node as any).rotation;

    return `rotate(${-1 * rotation}deg)`;
  }

  static fixCssBorder(params: { node: SceneNode; cssStyle: LooseStyle }) {
    const { node, cssStyle } = params;
    if ((node as FrameNode).strokeAlign === 'OUTSIDE') {
      cssStyle.outline = cssStyle.border;
      delete cssStyle.border;
    }
  }

  // 修复转换后的 css 属性中的值的一些问题
  static fixCssValue(cssValue: string) {
    if (!cssValue) {
      return cssValue;
    }
    let newCssValue = cssValue;
    if (this.regs.invalidCssVariable.test(cssValue)) {
      newCssValue = newCssValue.replace(this.regs.invalidCssVariable, 'var(--d2c-no-variable,');
    }
    return newCssValue;
  }

  static fixTextFillColor(params: { node: SceneNode; cssStyle: LooseStyle }) {
    const { node, cssStyle } = params;
    if (node.type !== 'TEXT' || node.fills === figma.mixed || node.fills.length <= 1) return;
    const color = convertTextFillToCSS(node.fills);
    cssStyle['color'] = color;
  }

  static setPosition(params: ParamsType & { cssStyle: LooseStyle; isSvg: boolean }) {
    const { cssStyle, node, parentNode, isSvg } = params;
    const figmaNode = node as FrameNode;

    if (!figmaNode) {
      return cssStyle;
    }

    if (figmaNode?.clipsContent) {
      cssStyle.overflow = 'hidden';
      cssStyle.position = 'relative';
    }

    // 无父级无子级的节点，不需要额外处理定位
    if (!parentNode && !figmaNode?.children?.length) {
      return cssStyle;
    }

    if ((figmaNode as unknown as TextNode).type === 'TEXT') {
      // 文本节点保留空白和换行
      cssStyle['white-space-collapse'] = 'preserve';
      cssStyle['overflow-wrap'] = 'break-word';

      const width = figmaNode?.absoluteBoundingBox?.width + 'px';
      const height = figmaNode?.absoluteBoundingBox?.height + 'px';
      switch ((figmaNode as unknown as TextNode).textAutoResize) {
        case 'NONE':
          cssStyle.width = width;
          cssStyle.height = height;
          break;
        case 'HEIGHT':
          cssStyle.width = width;
          break;
        case 'WIDTH_AND_HEIGHT': {
          cssStyle['text-wrap-mode'] = 'nowrap';
          if ((parentNode as any).layoutMode !== 'NONE') {
            cssStyle['flex-shrink'] = 0;
            cssStyle['flex-grow'] = 0;
          }
        }
      }
    } else if (
      // svg 图标本身自带有 width 和 height，这里的样式 width 和 height 有时会有一些冲突，因此，svg 图标不加这段样式处理
      // 非 svg 并且 自身并非 flex 布局时设置宽高
      !isSvg &&
      (!figmaNode?.layoutMode || figmaNode.layoutMode === 'NONE')
    ) {
      cssStyle.width = figmaNode?.absoluteBoundingBox?.width + 'px';
      cssStyle.height = figmaNode?.absoluteBoundingBox?.height + 'px';
    }

    // 父级是 flex 布局、且自身并非绝对定位时，作为定位基准
    if (
      // @ts-ignore
      parentNode?.layoutMode &&
      // @ts-ignore
      parentNode?.layoutMode !== 'NONE' &&
      // @ts-ignore
      node.layoutPositioning !== 'ABSOLUTE'
    ) {
      cssStyle.position = 'relative';
      return cssStyle;
    }

    // 其他场景为绝对定位，计算定位数据
    cssStyle.position = 'absolute';
    const nodeBox = isSvg ? (node as FrameNode)?.absoluteRenderBounds : node.absoluteBoundingBox;
    if (nodeBox && parentNode?.absoluteBoundingBox) {
      const parentBox = parentNode.absoluteBoundingBox;
      cssStyle.left = nodeBox.x - parentBox.x + 'px';
      cssStyle.top = nodeBox.y - parentBox.y + 'px';
    }
    return cssStyle;
  }

  private static getLineHeightValue(cssStyle: LooseStyle, fontSize: number) {
    const lineHeight = String(cssStyle['line-height']);
    if (/^[0-9]+px/.test(lineHeight)) {
      return parseFloat(lineHeight);
    }
    if (/^[0-9]+%/.test(lineHeight)) {
      return (parseFloat(lineHeight) * fontSize) / 100;
    }
    return fontSize * 1.2;
  }

  static setTextEllipsis(cssStyle: LooseStyle, node: TextNode) {
    if (node.type !== 'TEXT') {
      return cssStyle;
    }
    if (cssStyle['text-overflow'] === 'ellipsis') {
      const fontSize = parseFloat(String(cssStyle['font-size']));
      const lineHeight = this.getLineHeightValue(cssStyle, fontSize);
      if (node.height > lineHeight) {
        cssStyle.display = '-webkit-box';
        cssStyle['-webkit-line-clamp'] = Math.ceil(node.height / lineHeight);
        cssStyle['-webkit-box-orient'] = 'vertical';
        cssStyle['white-space'] = 'normal';
      }
    }
    return cssStyle;
  }

  static async getCssAsync(params: ParamsType) {
    try {
      const { node, parentNode, nodeProcess, setting } = params;
      let cssStyle: LooseStyle = {};
      const isSvg = nodeProcess?.htmlType === 'svg';

      cssStyle = await node.getCSSAsync();
      cssStyle['box-sizing'] = 'border-box';

      if ((node as any).opacity === 0) {
        cssStyle['opacity'] = 0;
      }

      // 非 svg 的正常获取 css 样式
      if (!isSvg) {
        // 修复带图标填充的场景
        const background = cssStyle.background as string;
        if (background && background.indexOf('path-to-image') > -1 && nodeProcess?.htmlType !== 'img') {
          const src = setting?.screenShot ? await screenShot.getScreenShotAsyncTimeout(node as SceneNode, setting) : '';
          cssStyle.background = `url(${src})`;
        } else if (background) {
          cssStyle.background = this.fixCssValue(background);
        }

        // 获取元素的旋转（svg 导出时已经带上旋转的信息，因此 svg 不再重复设置旋转）
        const transform = this.getTransformCss(node);
        if (transform) {
          cssStyle.transform = transform;
        }
      } else {
        delete cssStyle.width;
        delete cssStyle.height;
        delete cssStyle.transform;

        // 如果 svg 的父级设置了旋转的话，svg 需要反向旋转
        if (parentNode && (parentNode as FrameNode)?.rotation) {
          const transform = this.getTransformCss(parentNode, true);
          if (transform) {
            cssStyle.transform = transform;
          }
          const renderBounds = (node as FrameNode)?.absoluteRenderBounds;
          const boundingBox = (node as FrameNode)?.absoluteBoundingBox;
          if (renderBounds && boundingBox) {
            cssStyle['transform-origin'] =
              `${boundingBox.x + boundingBox.width / 2 - renderBounds.x}px ${boundingBox.y + boundingBox.height / 2 - renderBounds.y}px`;
          }
        }
      }

      if (cssStyle.color) {
        cssStyle.color = this.fixCssValue(cssStyle.color as string);
      }

      this.setPosition({
        ...params,
        cssStyle,
        isSvg,
      });

      this.setTextEllipsis(cssStyle, node as TextNode);

      this.fixCssBorder({
        node,
        cssStyle,
      });

      this.fixTextFillColor({
        node,
        cssStyle,
      });

      if (!node.visible) {
        cssStyle.display = 'none';
      }

      return cssStyle;
    } catch (error) {
      console.error('getCssAsync error', params.node, error);
      return {};
    }
  }

  static setFlexRelativeCss(params: SetFlexRelativeCssParams) {
    try {
      const { nodes, parentNode, htmlObject } = params;
      // 自动布局可以通过调整 canvas stacking 实现不同的层级
      const isCanvasStackingFirstTop =
        parentNode && (parentNode as any).layoutMode !== 'NONE' && (parentNode as any).itemReverseZIndex;

      // 自动布局 gap 为负数时 css 不支持
      const isGapNegativeValue =
        parentNode && (parentNode as any).layoutMode !== 'NONE' && (parentNode as any).itemSpacing < 0;

      for (let index = 0; index < nodes.length; index++) {
        const node = nodes[index];
        const htmlObjectItem = htmlObject[index];
        if (isCanvasStackingFirstTop) {
          htmlObjectItem.cssStyle['z-index'] = nodes.length - 1 - index;
        }
        // 自动布局 gap 为负数时 css 不支持, 需要单独处理
        if (isGapNegativeValue && (node as any).layoutPositioning !== 'ABSOLUTE' && index !== 0) {
          const marginDirection = (parentNode as any)?.layoutMode === 'HORIZONTAL' ? 'margin-left' : 'margin-top';
          htmlObjectItem.cssStyle[marginDirection] = (parentNode as any).itemSpacing + 'px';
        }
      }
      return htmlObject;
    } catch (error) {
      console.error('setFlexRelativeCss error', error);
      return params?.htmlObject;
    }
  }
}

export default CSSUtils;
