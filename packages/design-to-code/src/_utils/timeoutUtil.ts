class TimeoutUtil {
  /**
   * 执行竞速逻辑：以 handle 或 timeoutHandle 先返回的结果为准
   * @param handle 主业务逻辑（必须返回 Promise<string>）
   * @param timeoutHandle 超时回调逻辑（返回任意值）
   * @param timeout 超时时间（毫秒，默认 0 表示不启用超时）
   * @returns 先完成的结果（优先业务逻辑，超时逻辑仅兜底）
   * 注意： figma 的一些异步方法，比如 exportAsync 的做异步操作时，会直接让整个插件都卡住，相当于将整个 js 进程都暂停了，所以虽然通过计时判断时，发现导出的时间是比较久的，但因整个 Promise.race 都是暂停的状态，一直到 exportAsync 执行完毕。但这时导出已经完成，所以一定是比 timeout 先返回的。
   */
  static async main(
    handle: () => Promise<string>,
    timeoutHandle: () => unknown,
    timeout: number = 0
  ): Promise<string> {
    if (!timeout) {
      try {
        return await handle();
      } catch (error) {
        console.error('Handle error (no timeout):', error);
        throw error; // 重新抛出以保持调用方感知错误
      }
    }

    let timeoutId: ReturnType<typeof setTimeout>;

    // 构造超时 Promise（明确类型和错误处理）
    const timeoutPromise = new Promise<string>((resolve) => {
      timeoutId = setTimeout(() => {
        try {
          const result = timeoutHandle();
          resolve(String(result)); // 强制转为 string 保持类型一致
        } catch (error) {
          console.error('Timeout handle error:', error);
          resolve(''); // 兜底返回空字符串
        }
      }, timeout);
    });

    try {
      return await Promise.race([
        handle().catch((error) => {
          console.error('Handle error (race):', error);
          throw error; // 保持竞速失败
        }),
        timeoutPromise
      ]);
    } finally {
      clearTimeout(timeoutId!); // 确保清理定时器
    }
  }
}

export default TimeoutUtil;
