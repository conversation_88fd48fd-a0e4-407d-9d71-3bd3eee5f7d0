import { ConversionOptions } from './types';

export interface ColorResult {
  type: 'SOLID' | 'GRADIENT_LINEAR' | 'GRADIENT_RADIAL' | 'GRADIENT_ANGULAR' | 'GRADIENT_DIAMOND';
  value: string;
}

export class FigmaFillsConverter {
  private options: Required<Pick<ConversionOptions, 'precision'>>;

  constructor(options: ConversionOptions = {}) {
    this.options = {
      precision: options.precision || 3,
    };
  }

  /**
   * 转换 Figma fills 为颜色数组
   */
  convertFills(fills: readonly Paint[]): ColorResult[] {
    if (!fills || fills.length === 0) {
      return [];
    }

    const visibleFills = this.filterVisibleFills(fills);

    if (visibleFills.length === 0) {
      return [];
    }

    return visibleFills
      .map((fill) => this.convertSingleFill(fill))
      .filter((result): result is ColorResult => result !== null);
  }

  /**
   * 过滤可见的填充，只保留实色和渐变
   */
  private filterVisibleFills(fills: readonly Paint[]): Paint[] {
    return fills.filter((fill): fill is Paint => {
      return fill.visible !== false && (fill.opacity || 1) > 0 && this.isSupportedFillType(fill.type);
    });
  }

  /**
   * 检查是否为支持的填充类型
   */
  private isSupportedFillType(type: string): boolean {
    return ['SOLID', 'GRADIENT_LINEAR', 'GRADIENT_RADIAL', 'GRADIENT_ANGULAR', 'GRADIENT_DIAMOND'].includes(type);
  }

  /**
   * 转换单个填充
   */
  private convertSingleFill(fill: Paint): ColorResult | null {
    switch (fill.type) {
      case 'SOLID':
        return this.convertSolidFill(fill);
      case 'GRADIENT_LINEAR':
        return this.convertLinearGradient(fill);
      case 'GRADIENT_RADIAL':
        return this.convertRadialGradient(fill);
      case 'GRADIENT_ANGULAR':
        return this.convertAngularGradient(fill);
      case 'GRADIENT_DIAMOND':
        return this.convertDiamondGradient(fill);
      default:
        return null;
    }
  }

  /**
   * 转换实色填充
   */
  private convertSolidFill(fill: SolidPaint): ColorResult {
    const value = this.figmaColorToCSS(fill.color, fill.opacity || 1);
    return { type: 'SOLID', value };
  }

  /**
   * 转换线性渐变
   */
  private convertLinearGradient(fill: GradientPaint): ColorResult {
    const { gradientStops, gradientTransform } = fill;
    const opacity = fill.opacity || 1;

    const angle = this.calculateLinearGradientAngle(gradientTransform);

    const stops = gradientStops
      .map((stop) => {
        const color = this.figmaColorToCSS(stop.color, opacity);
        const position = this.roundToDecimal(stop.position * 100, this.options.precision);
        return `${color} ${position}%`;
      })
      .join(', ');

    return {
      type: 'GRADIENT_LINEAR',
      value: `linear-gradient(${angle}deg, ${stops})`,
    };
  }

  /**
   * 转换径向渐变
   */
  private convertRadialGradient(fill: GradientPaint): ColorResult {
    const { gradientStops, gradientTransform } = fill;
    const opacity = fill.opacity || 1;

    const params = this.calculateRadialGradientParams(gradientTransform);

    const stops = gradientStops
      .map((stop) => {
        const color = this.figmaColorToCSS(stop.color, opacity);
        const position = this.roundToDecimal(stop.position * 100, this.options.precision);
        return `${color} ${position}%`;
      })
      .join(', ');

    return {
      type: 'GRADIENT_RADIAL',
      value: `radial-gradient(${params.shape} ${params.size} at ${params.centerX}% ${params.centerY}%, ${stops})`,
    };
  }

  /**
   * 转换角度渐变
   */
  private convertAngularGradient(fill: GradientPaint): ColorResult {
    const { gradientStops, gradientTransform } = fill;
    const opacity = fill.opacity || 1;

    const stops = gradientStops
      .map((stop) => {
        const color = this.figmaColorToCSS(stop.color, opacity);
        const angle = this.roundToDecimal(stop.position * 360, this.options.precision);
        return `${color} ${angle}deg`;
      })
      .join(', ');

    const center = this.calculateGradientCenter(gradientTransform);

    return {
      type: 'GRADIENT_ANGULAR',
      value: `conic-gradient(from 0deg at ${center.x}% ${center.y}%, ${stops})`,
    };
  }

  /**
   * 转换钻石渐变（使用径向渐变近似）
   */
  private convertDiamondGradient(fill: GradientPaint): ColorResult {
    const radialResult = this.convertRadialGradient(fill);
    return {
      type: 'GRADIENT_DIAMOND',
      value: radialResult.value,
    };
  }

  /**
   * 将 Figma 颜色转换为 CSS 颜色
   */
  private figmaColorToCSS(color: RGB, opacity: number = 1): string {
    if (!color) return 'transparent';

    const r = Math.round((color.r || 0) * 255);
    const g = Math.round((color.g || 0) * 255);
    const b = Math.round((color.b || 0) * 255);

    if (opacity < 1) {
      const alpha = this.roundToDecimal(opacity, this.options.precision);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    const toHex = (num: number): string => num.toString(16).padStart(2, '0');
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }

  /**
   * 计算线性渐变角度
   */
  private calculateLinearGradientAngle(transform: Transform): number {
    if (!transform || !Array.isArray(transform)) {
      return 0;
    }

    const [[a], [b]] = transform;
    const radians = Math.atan2(b, a);
    let degrees = radians * (180 / Math.PI);
    degrees = (90 - degrees + 360) % 360;

    return this.roundToDecimal(degrees, this.options.precision);
  }

  /**
   * 计算径向渐变参数
   */
  private calculateRadialGradientParams(transform: Transform) {
    const params = {
      shape: 'ellipse' as 'circle' | 'ellipse',
      size: '50% 50%',
      centerX: 50,
      centerY: 50,
    };

    if (!transform || !Array.isArray(transform)) {
      return params;
    }

    try {
      const [[a, c, tx], [b, d, ty]] = transform;

      params.centerX = this.roundToDecimal((tx + 1) * 50, this.options.precision);
      params.centerY = this.roundToDecimal((ty + 1) * 50, this.options.precision);

      const radiusX = Math.sqrt(a * a + b * b);
      const radiusY = Math.sqrt(c * c + d * d);

      if (Math.abs(radiusX - radiusY) < 0.01) {
        params.shape = 'circle';
        params.size = `${this.roundToDecimal(radiusX * 100, this.options.precision)}%`;
      } else {
        params.shape = 'ellipse';
        params.size = `${this.roundToDecimal(radiusX * 100, this.options.precision)}% ${this.roundToDecimal(radiusY * 100, this.options.precision)}%`;
      }
    } catch (error) {
      // 使用默认参数
      console.error(error);
    }

    return params;
  }

  /**
   * 计算渐变中心点
   */
  private calculateGradientCenter(transform: Transform): { x: number; y: number } {
    if (!transform || !Array.isArray(transform)) {
      return { x: 50, y: 50 };
    }

    try {
      const [[, , tx], [, , ty]] = transform;
      return {
        x: this.roundToDecimal((tx + 1) * 50, this.options.precision),
        y: this.roundToDecimal((ty + 1) * 50, this.options.precision),
      };
    } catch (error) {
      console.error(error);
      return { x: 50, y: 50 };
    }
  }

  /**
   * 数字保留指定小数位数
   */
  private roundToDecimal(number: number, decimals: number): number {
    const factor = Math.pow(10, decimals);
    return Math.round(number * factor) / factor;
  }
}

export default new FigmaFillsConverter();
