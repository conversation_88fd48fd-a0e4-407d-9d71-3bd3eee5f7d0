import { ComponentNodesItem, AiComponentNode } from './detect-component-interface';

export type LooseStyle = {
  [key: string]: string | number | LooseStyle;
};

export type NodeType = ComponentNode | FrameNode | GroupNode | InstanceNode | RectangleNode | StarNode | VectorNode | EllipseNode | LineNode | PolygonNode | ShapeWithTextNode | TextNode;

export type HtmlTagType = 'div' | 'span' | 'img' | 'a' | 'svg';

export type ProcessKey = 'div' | 'img' | 'text' | 'svg';

export type HtmlObjectItem = {
  type: HtmlTagType;
  nodeId?: string;
  nodeType?: string;
  cssStyle: LooseStyle;
  src?: string;
  text?: string;
  href?: string;
  svg?: string;
  children?: HtmlObjectItem[];
};

export type HtmlObject = HtmlObjectItem[];

export type HtmlItem = {
  node: ComponentNodesItem['node'];
  componentNodes: Array<{
    class: AiComponentNode['class'];
    nodes: AiComponentNode['nodes'];
    html: string;
  }>;
};

export type Html = HtmlItem[];

export type NodeProcessType = {
  htmlType: ProcessKey;
  types: string[];
  process: 
    ((node: NodeType, parentNode?: SceneNode) => Promise<HtmlObjectItem>) |
    ((node: TextNode, parentNode?: SceneNode) => Promise<HtmlObjectItem>)
  ;
  domToHtmlObject: 
    ((dom: HTMLElement) => HtmlObjectItem) |
    ((dom: HTMLImageElement) => HtmlObjectItem) |
    ((dom: HTMLLinkElement | HTMLSpanElement) => HtmlObjectItem) |
    ((dom: SVGElement) => HtmlObjectItem)
  ;
};

export type NodeProcessesType = {
  [key in ProcessKey]: NodeProcessType;
};

export type DesignToCodeSetting = {
  /**
   * 图片资源是否导出，若为 false 则直接用占位图代替
   */
  screenShot?: boolean;
  /**
   * 占位图的生成方式，默认是 https://placehold.co/${width}x${height}
   */
  placeholder?: (node: SceneNode) => string;
  /**
   * 导出超时时间，单位毫秒，为 0 时代表不设置超时时间
   */
  timeout?: number;
  /**
   * 是否忽略隐藏的图层
   */
  ignoreHidden?: boolean;
};