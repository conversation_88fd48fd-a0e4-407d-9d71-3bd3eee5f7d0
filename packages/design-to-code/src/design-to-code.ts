import * as htmlparser2 from 'htmlparser2';

import CSSUtils from './_utils/CSSUtils';
import TimeoutUtil from './_utils/timeoutUtil';
import { ComponentNodesList, DesignNodes } from './detect-component-interface';
import { getCssStyleFromStyledSegment, TextNodeMixFields } from './helper';
import { NodeType } from './interface';
import { DesignToCodeSetting, Html, HtmlObject, HtmlObjectItem, NodeProcessesType, ProcessKey } from './interface';
import screenShot from './screen-shot';

export class DesignToCode {
  public constructor(setting: DesignToCodeSetting = {}) {
    this.setting = Object.assign(this.setting, setting);
  }
  private setting: DesignToCodeSetting = {
    screenShot: true,
    placeholder: (node: SceneNode) => {
      const box = node.absoluteBoundingBox;
      if (box?.width && box?.height) {
        return `https://placehold.co/${box.width}x${box.height}`;
      }
      return '';
    },
    timeout: 0,
    ignoreHidden: false,
  };

  private exportSvgAsync = async (node: SceneNode) => {
    try {
      const svg = await TimeoutUtil.main(
        async () => {
          try {
            const svg = await node.exportAsync({ format: 'SVG_STRING' });
            return svg;
          } catch (error) {
            console.error('node export error', node, error);
            return '';
          }
        },
        () => {
          try {
            const url = this.setting.placeholder?.(node);
            if (url) {
              return `<img src="${url}" />`;
            }
            return '';
          } catch (error) {
            console.log('timeoutHandle error', error);
            return '';
          }
        },
        this.setting.timeout,
      );
      return svg;
    } catch (error) {
      console.error('node export error', node, error);
      return '';
    }
  };

  private commonDomToHtmlObject = (dom: any) => {
    const htmlObject = {
      type: dom.name.toLowerCase(),
      nodeId: dom.attribs['data-node-id'],
      nodeType: dom.attribs['data-node-type'],
      cssStyle: this.getCssStyle(dom),
    } as HtmlObjectItem;
    return htmlObject;
  };

  private hasImageOrVideoFill(node: SceneNode): boolean {
    if ('fills' in node && node.fills instanceof Array) {
      return node.fills.some((fill) => 'IMAGE' === fill.type || 'VIDEO' === fill.type);
    }
    return 'MEDIA' === node.type;
  }

  private nodeProcesses: NodeProcessesType = {
    div: {
      htmlType: 'div',
      types: ['COMPONENT', 'FRAME', 'GROUP', 'INSTANCE', 'RECTANGLE'],
      process: async (node: NodeType, parentNode?: SceneNode) => {
        const cssStyle = await this.getFinalCssAsync(node as SceneNode, parentNode);
        const result = {
          type: 'div',
          nodeId: node.id,
          nodeType: node.type,
          cssStyle,
        } as any;

        return result;
      },
      domToHtmlObject: (dom: HTMLElement) => {
        const htmlObject = this.commonDomToHtmlObject(dom);
        return htmlObject;
      },
    },
    img: {
      htmlType: 'img',
      types: ['SHAPE_WITH_TEXT'],
      process: async (node: NodeType, parentNode?: SceneNode) => {
        const cssStyle = await this.getFinalCssAsync(node as SceneNode, parentNode);
        const src = this.setting.screenShot
          ? await screenShot.getScreenShotAsyncTimeout(node as SceneNode, this.setting)
          : this.setting.placeholder?.(node);

        const result = {
          type: 'img',
          nodeId: node.id,
          nodeType: node.type,
          cssStyle,
          src,
        } as any;

        return result;
      },
      domToHtmlObject: (dom: HTMLImageElement) => {
        const htmlObject = this.commonDomToHtmlObject(dom);
        if (dom.src) {
          htmlObject.src = dom.src;
        }
        return htmlObject;
      },
    },
    text: {
      htmlType: 'text',
      types: ['TEXT'],
      process: async (node: TextNode, parentNode?: SceneNode) => {
        const cssStyle = await this.getFinalCssAsync(node, parentNode);
        // @ts-ignore
        const isLink = node?.hyperlink?.type === 'URL';
        const isLinkMixed = node.hyperlink === figma.mixed;
        const isDecorationMixed = node.textDecoration === figma.mixed;
        const styledTextSegments = node.getStyledTextSegments(TextNodeMixFields.concat(isLinkMixed ? 'hyperlink' : []));

        const result = {
          nodeId: node.id,
          nodeType: node.type,
          type: isLink ? 'a' : 'span',
          // @ts-ignore
          href: isLink ? node.hyperlink.value : null,
          cssStyle,
        } as any;

        if (styledTextSegments.length === 1) {
          result.text = node.characters;
          return result;
        }

        if (isLinkMixed || isDecorationMixed) {
          result.cssStyle['text-decoration-line'] = 'none';
        }
        result.children = styledTextSegments.map((segment) => this.getTextNodeSegmentsChild(segment));
        return result;
      },
      domToHtmlObject: (dom: HTMLLinkElement | HTMLSpanElement) => {
        const htmlObject = this.commonDomToHtmlObject(dom);
        htmlObject.text = dom.innerText;
        if ((dom as HTMLLinkElement)?.href) {
          htmlObject.href = (dom as HTMLLinkElement).href;
        }
        return htmlObject;
      },
    },
    svg: {
      htmlType: 'svg',
      types: ['LINE', 'STAR', 'VECTOR', 'ELLIPSE', 'POLYGON', 'BOOLEAN_OPERATION'],
      process: async (node: SceneNode, parentNode?: SceneNode) => {
        const cssStyle = await this.getFinalCssAsync(node as SceneNode, parentNode);
        const result = {
          type: 'svg',
          nodeId: node.id,
          nodeType: node.type,
          cssStyle,
          svg: await this.exportSvgAsync(node),
        } as any;

        return result;
      },
      domToHtmlObject: (dom: SVGElement) => {
        const htmlObject = this.commonDomToHtmlObject(dom);
        htmlObject.svg = dom.outerHTML;
        return htmlObject;
      },
    },
  };

  private getTextNodeSegmentsChild = (segment: StyledTextSegment) => {
    const cssStyle = getCssStyleFromStyledSegment(segment);
    // @ts-ignore
    const isLink = segment?.hyperlink?.type === 'URL';
    const htmlObject = {
      type: isLink ? 'a' : 'span',
      cssStyle,
      text: segment.characters,
      href: isLink ? segment.hyperlink?.value : null,
    } as HtmlObjectItem;
    return htmlObject;
  };

  // 获取完整的 css
  getFinalCssAsync = async (node: SceneNode, parentNode?: SceneNode) => {
    try {
      const nodeProcess = this.getNodeProcess(node);
      const cssStyle = await CSSUtils.getCssAsync({
        node,
        parentNode,
        nodeProcess,
        setting: this.setting,
      });
      return cssStyle;
    } catch (error) {
      console.error('getFinalCssAsync error', node, error);
      return {};
    }
  };

  // 获取到处理的策略
  getNodeProcess = (node: SceneNode) => {
    if (!node) {
      return null;
    }
    const nodeType = node.type;
    const nodeProcesses = this.nodeProcesses;

    // 如果是图片素材，直接返回 svg 的处理策略
    if (node.isAsset && !this.hasImageOrVideoFill(node)) {
      return nodeProcesses.svg;
    }

    // 如果 node 子节点包含 mask 直接返回 svg 处理
    if ((node as FrameNode)?.children?.some((child: SceneNode & { isMask?: boolean }) => child.isMask)) {
      return nodeProcesses.svg;
    }

    for (const key in nodeProcesses) {
      if (Object.prototype.hasOwnProperty.call(nodeProcesses, key)) {
        // @ts-ignore
        const item = nodeProcesses[key];
        if (item.types.includes(nodeType)) {
          return item;
        }
      }
    }
    return null;
  };

  // 将节点转化为 html 对象
  getHtmlObjectAsync = async (nodes: DesignNodes, parentNode?: SceneNode): Promise<HtmlObject> => {
    try {
      const result = [] as HtmlObject;
      const firstLevelNodes = [] as any;
      let minX: any;
      let minY: any;
      let maxX: any;
      let maxY: any;

      for (let index = 0; index < nodes.length; index++) {
        const node = nodes[index];
        const figmaNode = (await figma.getNodeByIdAsync(node.id)) as FrameNode;
        const nodeProcess = this.getNodeProcess(figmaNode);
        if (!nodeProcess) {
          continue;
        }
        if (this.setting.ignoreHidden && figmaNode.visible === false) {
          continue;
        }
        const htmlObject = await nodeProcess.process(figmaNode, parentNode);

        result.push(htmlObject);

        if (nodeProcess.htmlType !== 'svg' && figmaNode?.children?.length) {
          htmlObject.children = await this.getHtmlObjectAsync(figmaNode.children as any[], figmaNode);
        }

        if (!parentNode) {
          firstLevelNodes.push(figmaNode);
          const nodeX = figmaNode?.absoluteBoundingBox?.x || 0;
          const nodeY = figmaNode?.absoluteBoundingBox?.y || 0;
          const width = figmaNode.absoluteBoundingBox?.width || 0;
          const height = figmaNode.absoluteBoundingBox?.height || 0;
          if (minX === undefined) {
            minX = nodeX;
            minY = nodeY;
            maxX = nodeX + width;
            maxY = nodeY + height;
          } else {
            if (nodeX < minX) {
              minX = nodeX;
            }
            if (nodeY < minY) {
              minY = nodeY;
            }
            if (nodeX + width > maxX) {
              maxX = nodeX + width;
            }
            if (nodeY + height > maxY) {
              maxY = nodeY + height;
            }
          }
        }
      }
      CSSUtils.setFlexRelativeCss({
        nodes,
        parentNode,
        htmlObject: result,
      });
      // 当第一层级的图层有多个，而且没有一个统一的父节点时，需要补充一个 div
      if (firstLevelNodes.length > 1) {
        return [
          {
            type: 'div',
            cssStyle: {
              position: 'relative',
              width: maxX - minX + 'px',
              height: maxY - minY + 'px',
            },
            children: result.map((item: any, index: number) => {
              const figmaNode = firstLevelNodes[index];
              return {
                ...item,
                cssStyle: {
                  ...item.cssStyle,
                  position: 'absolute',
                  left: figmaNode?.absoluteBoundingBox?.x - minX + 'px',
                  top: figmaNode?.absoluteBoundingBox?.y - minY + 'px',
                  width: figmaNode?.absoluteBoundingBox?.width + 'px',
                  height: figmaNode?.absoluteBoundingBox?.height + 'px',
                },
              };
            }),
          },
        ];
      }
      return result;
    } catch (error) {
      console.error('getHtmlObjectAsync error', nodes, error);
      return [];
    }
  };

  getStyleString = (item: HtmlObjectItem) => {
    const cssStyle = item.cssStyle;
    return Object.keys(cssStyle)
      .map((key: any) => `${key}: ${cssStyle[key]};`)
      .join('')
      .replace(/"/g, `'`);
  };

  // 将样式信息转化为 html 字符串
  getHtmlString = (htmlObject?: HtmlObject) => {
    if (!htmlObject) {
      return '';
    }
    const htmlStrings = [] as any;
    htmlObject.forEach((item: HtmlObjectItem) => {
      if (item.type === 'svg') {
        let htmlString: string | undefined = '';
        if (/^\<svg/.test(item.svg || '')) {
          htmlString = item.svg?.replace(
            /^\<svg/,
            `<svg data-node-id="${item.nodeId}" data-node-type="${item.nodeType}" style='${this.getStyleString(item)}'`,
          );
        } else {
          htmlString = item.svg?.replace(
            /^\<img/,
            `<img data-node-id="${item.nodeId}" data-node-type="${item.nodeType}" style='${this.getStyleString(item)}'`,
          );
        }
        htmlStrings.push(htmlString);
        return;
      }

      htmlStrings.push(`<${item.type} 
        style="${this.getStyleString(item)}"
        ${item.type === 'img' ? `src="${item.src}"` : ''}
        ${item.type === 'a' ? `href="${item.href}"` : ''}
        data-node-id="${item.nodeId ?? ''}"
        data-node-type="${item.nodeType ?? ''}">${item.text || ''}${this.getHtmlString(item.children)}</${item.type}>`);
    });

    return htmlStrings.join('');
  };

  // 将节点转换成 html 字符串
  getHtmlAsync = async (nodes: DesignNodes) => {
    const htmlObject = (await this.getHtmlObjectAsync(nodes)) as HtmlObject;
    const html = this.getHtmlString(htmlObject);
    return html;
  };

  // componentNodes = [{node: {id: string}, componentNodes: [{class: string; bbox: {}, targetNode: {}, nodes: [{id: string}]}]}]
  getHtmlFromComponentNodesAsync = async (componentNodes: ComponentNodesList) => {
    const result = [] as Html;
    for (const item of componentNodes) {
      const cNodes = await Promise.all(
        item.componentNodes.map(async (componentNode) => ({
          class: componentNode.class,
          nodes: componentNode.nodes,
          // @ts-ignore
          html: (await this.getHtmlAsync(componentNode.nodes)) as string,
        })),
      );
      result.push({
        node: item.node,
        componentNodes: cNodes,
      });
    }
    return result;
  };

  domsToObject = (doms: any[]) => {
    const htmlObject = [] as HtmlObject;
    for (const dom of doms) {
      let type = dom.name;
      if (['a', 'span'].includes(type)) {
        type = 'text';
      }
      const domToHtmlObject = this.nodeProcesses[type as ProcessKey]?.domToHtmlObject;
      // 跳过不需要处理的节点
      if (!domToHtmlObject) {
        continue;
      }
      // @ts-ignore
      const htmlObjectItem = this.nodeProcesses[type as ProcessKey]?.domToHtmlObject(dom);
      if (dom.childNodes.length) {
        // @ts-ignore
        const children = this.domsToObject(dom.childNodes);
        if (children.length) {
          htmlObjectItem.children = children;
        }
      }
      htmlObject.push(htmlObjectItem);
    }
    return htmlObject;
  };

  htmlStringToObject = (htmlString: string) => {
    const document = htmlparser2.parseDocument(htmlString);
    // document.html.body.div.childNodes
    const doms = document.children;
    // @ts-ignore
    const htmlObject = this.domsToObject(doms);
    return htmlObject;
  };

  // 从 dom 节点中获取到 style 属性上的值，并转换成对象
  getCssStyle = (node: any) => {
    const cssStyle = {} as any;
    const style = node?.attribs?.style ? node.attribs.style.split(';') : [];
    for (const item of style) {
      const [key, value] = item.split(':');
      if (!key) {
        continue;
      }
      cssStyle[key.trim()] = value.trim();
    }
    return cssStyle;
  };
}

const designToCode = new DesignToCode();
export default designToCode;
