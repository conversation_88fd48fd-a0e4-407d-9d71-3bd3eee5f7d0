
export type DesignNode = {
  id: string;
} | SceneNode;

export type DesignNodes = Array<DesignNode>;

export type ScreenShotType = {
  node: {
    id: string;
  };
  screenshot: string;
};

export type Prediction = {
  class: string;
  confidence: number;
  bbox: {
    height: number;
    width: number;
    x: number; // 注意：ai 返回的 x 和 y 并不是左上角坐标，而是中心点坐标
    y: number;
  };
  color: string;
};

export type PredictionsItem = {
  node: DesignNode;
  screenshot: string;
  predictions: Array<Prediction>;
};

export type PredictionsList = Array<PredictionsItem>;

export type ScreenShotsType = Array<ScreenShotType>;

export type TargetNode = {
  x: number; // 左上角的坐标
  y: number; // 左上角的坐标
  width: number;
  height: number;
}

export type AiComponentNode = {
  class: Prediction['class'];
  bbox: Prediction['bbox'];
  targetNode: TargetNode;
  nodes: Array<DesignNode>;
}

export type ComponentNodesItem = {
  node: DesignNode;
  componentNodes: Array<AiComponentNode>;
};

export type ComponentNodesList = Array<ComponentNodesItem>;