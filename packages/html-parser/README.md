# @tencent/h2d-html-parser

> HTML 转设计稿解析器 - 将 HTML 页面智能转换为设计稿的专业工具

## 📖 简介

@tencent/h2d-html-parser 是一个强大的 HTML 解析工具，能够将 HTML 页面解析并转换为设计稿格式。该工具支持从 URL、HTML 字符串或 H2D 文件多种输入方式，并能处理样式、字体、图片等各种资源，最终生成可用于设计工具的格式。

## ✨ 主要功能

- 🌐 **多种输入方式**：支持 URL 链接、HTML 字符串、H2D 文件等多种输入格式
- 🎨 **样式解析**：完整解析 CSS 样式，包括颜色、字体、布局等
- 🖼️ **资源处理**：自动处理图片、视频等媒体资源
- 📱 **响应式支持**：支持移动端和桌面端页面解析
- 🎯 **字体管理**：智能字体检测和缺失字体处理
- 📊 **进度跟踪**：提供详细的解析进度回调
- 🔧 **高度可配置**：丰富的配置选项满足不同需求

## 🚀 快速开始

### 安装

```bash
npm install @tencent/h2d-html-parser
```

### 基本使用

```typescript
import { HtmlParser } from '@tencent/h2d-html-parser';

// 初始化解析器
const parser = new HtmlParser({
  apiUrl: 'https://plugin.codesign.qq.com/api/plugin',
  source: 'figma',
  user: {
    userId: 'your-user-id',
    userName: 'your-username',
  },
  getImageHash: async (imageData) => {
    // 图片哈希处理逻辑
  },
  getVideoHash: async (videoData) => {
    // 视频哈希处理逻辑
  },
  createDesign: async (designData) => {
    // 设计稿创建逻辑
  },
});

// 从 URL 解析
await parser.create({
  url: 'https://example.com',
  name: '示例页面',
  pageParams: {
    viewport: 1920,
    theme: 'light',
  }
}, (progress) => {
  console.log(`解析进度: ${progress.progress}%`);
});
```

## 📚 API 文档

### HtmlParser 类

#### 构造函数

```typescript
new HtmlParser(options: IInitOptions)
```

**参数：**

- `apiUrl` (可选): API 服务地址
- `source` (可选): 请求来源标识
- `user`: 用户信息
- `getImageHash`: 图片哈希处理回调
- `getVideoHash`: 视频哈希处理回调
- `createDesign`: 设计稿创建回调
- `createDesignCollect`: 设计稿收集回调

#### 主要方法

##### create(params, onChange?)

解析 HTML 并生成设计稿的核心方法。

```typescript
async create(
  params: IUrlParams | IhtmlParams | Ih2dParams,
  onChange?: (params: CallbackParams) => void
): Promise<PageObject>
```

**支持的参数类型：**

1. **URL 参数** (`IUrlParams`):
```typescript
{
  url: string;           // 目标 URL
  id?: number | string;  // 页面 ID
  name?: string;         // 页面名称
  pageParams?: IPageParams;
}
```

2. **HTML 参数** (`IhtmlParams`):
```typescript
{
  html: string;          // HTML 内容
  css?: string;          // CSS 样式
  name: string;          // 页面名称
  pageParams?: IPageParams;
}
```

3. **H2D 文件参数** (`Ih2dParams`):
```typescript
{
  h2d: File;             // H2D 文件
  name: string;          // 页面名称
  pageParams?: IPageParams;
}
```

##### getDSL(params)

仅获取 DSL 对象，不创建设计稿。

```typescript
async getDSL(params: IhtmlParams): Promise<any>
```

### 页面参数 (IPageParams)

```typescript
interface IPageParams {
  viewport: number;                    // 视口宽度
  theme: 'light' | 'dark' | string;   // 主题模式
  isMobile?: boolean;                  // 是否移动端
  isCreateCollectAssets?: boolean;     // 是否创建资源收集
  isUseAutoLayout?: boolean;           // 是否使用自动布局
  isScreenShot?: boolean;              // 是否截图
  availableFamily?: FamilyMap;         // 可用字体
  missingFamily?: MissingFamily;       // 缺失字体
  withoutHtmlBody?: boolean;           // 是否不包含 body
  container?: {                        // 目标容器
    targetId: string;
    optType: 'replace' | 'append' | 'modify';
  };
}
```

### 进度回调 (CallbackParams)

```typescript
interface CallbackParams {
  stepName: string;    // 当前步骤名称
  status: Status;      // 执行状态
  progress: number;    // 进度百分比 (0-100)
  error?: any;         // 错误信息
}
```

## 🔧 高级用法

### 字体缺失处理

解析器提供字体缺失事件处理机制：

```typescript
// 监听字体缺失事件
parser.fontMissingEmitter.on((event) => {
  console.log('缺失字体:', event.missingFamily);
  console.log('可用字体:', event.availableFamily);
  
  // 处理字体映射
  const fontMapping = processFontMapping(event);
  
  // 设置字体映射
  parser.fontSetEmitter.emit(fontMapping);
});
```

### 自定义容器模式

支持将解析结果添加到指定容器：

```typescript
await parser.create({
  html: '<div>内容</div>',
  name: '组件',
  pageParams: {
    viewport: 1200,
    container: {
      targetId: 'container-id',
      optType: 'append'  // 'replace' | 'append' | 'modify'
    }
  }
});
```

### 主题和响应式支持

```typescript
await parser.create({
  url: 'https://example.com',
  name: '响应式页面',
  pageParams: {
    viewport: 768,        // 移动端视口
    isMobile: true,       // 明确指定为移动端
    theme: 'dark',        // 深色主题
  }
});
```

## 🧪 测试

运行测试：

```bash
npm test
```

项目包含完整的测试用例覆盖主要功能模块。

## 📦 依赖

- `colord`: 颜色处理
- `tinycolor2`: 颜色操作
- `opentype.js`: 字体处理
- `crypto-js`: 加密功能
- `postcss-value-parser`: CSS 值解析
- `transformation-matrix`: 矩阵变换

## 🛠️ 开发

### 项目结构

```
packages/html-parser/
├── lib/
│   ├── index.ts          # 主入口文件
│   ├── processor.ts      # 核心处理器
│   ├── assets.ts         # 资源处理
│   ├── helper.ts         # 辅助工具
│   ├── node/             # 节点处理
│   ├── design/           # 设计稿生成
│   └── utils/            # 工具函数
├── types/
│   ├── index.ts          # 类型导出
│   ├── parser.ts         # 解析器类型
│   ├── html.ts           # HTML 相关类型
│   ├── dsl.ts            # DSL 类型定义
│   └── import.ts         # 导入类型
├── tests/
│   └── htmlParser.spec.ts # 测试文件
└── package.json
```

### 构建配置

项目使用 TypeScript 开发，支持 ES 模块：

- **主入口**: `lib/index.ts`
- **类型定义**: `types/index.ts`
- **模块类型**: `module`
- **测试框架**: Vitest

## 📄 许可证

ISC

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

> 本项目是腾讯 AI 助手项目的一部分，专注于提供高质量的 HTML 到设计稿转换能力。 