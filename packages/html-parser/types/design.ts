/**
 * 字体集合
 */
export type FamilyMap = {
  [key: string]: {
    family: string;
    styles: string[];
  };
};

/**
 * 图片资源
 */
export type ImageAsset = {
  mimeType: 'image/svg+xml' | 'image/png' | 'image/jpeg' | 'image/gif' | 'image/webp' | 'image/jpg' | 'video/mp4';
  type: 'http' | 'base64' | 'dom';
  value: string;
  from?: 'background' | 'src';
};

/**
 * 图片hash信息
 */
export type ImageHashInfo = {
  hash: string;
  width: number;
  height: number;
};
