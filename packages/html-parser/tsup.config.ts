import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['index.ts'],
  format: ['esm', 'cjs'],
  dts: {
    resolve: true,
    compilerOptions: {
      composite: false,
      declaration: true,
      skipLibCheck: true,
    },
    banner: '/// <reference types="@figma/plugin-typings" />',
  },
  splitting: false,
  sourcemap: false,
  clean: false,
  treeshake: true,
  minify: false,
  outDir: 'dist',
});
