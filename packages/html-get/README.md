# html-get

一个轻量级的HTML元素解析库，专注于将HTML元素转换为结构化JSON数据。

## ✨ 功能特性

- 🚀 **简洁高效**：专注于HTML元素解析的核心功能
- 🛠️ **类型安全**：完整的TypeScript类型支持
- 📦 **零依赖**：不依赖任何第三方库，保持轻量级
- 🔍 **精准解析**：精确提取HTML元素的结构和属性

## 📦 安装

```bash
npm install html-get
# 或
yarn add html-get
```

## 🚀 快速使用

### 基础用法

```typescript
import { getHtml } from 'html-get';

const htmlElement = document.getElementById('my-element');
const result = await getHtml(htmlElement);
console.log(result);
```

### TypeScript支持

```typescript
import { getHtml, type HtmlObject } from 'html-get';

async function parseElement(element: HTMLElement): Promise<HtmlObject> {
  return getHtml(element);
}
```

## 📚 API文档

### `getHtml(elem: HTMLElement): Promise<HtmlObject>`

解析HTML元素并返回包含元素数据的结构化对象。

#### 参数
- `elem`: 需要解析的HTML元素 (必须是一个有效的HTMLElement)

#### 返回值
一个解析为`HtmlObject`的Promise，包含解析后的数据。

## 🏷️ 类型定义

```typescript
/**
 * HTML节点数据接口
 */
export interface NodeData extends Partial<PositionInfo> {
  type?: string;         // 节点类型
  tag?: string;          // 元素标签名
  attr?: Record<string, any>; // 元素属性
  classList?: string[];  // 类名列表
  styles?: Record<string, string>; // 样式属性
  children?: NodeData[] | null; // 子元素
  value?: string;        // 节点值
  isContent?: boolean;   // 是否为内容节点
}

/**
 * HTML对象结构
 */
export interface Document {
  name: string;          // 文档名称
  doc: {
    innerHeight: number; // 文档高度
    innerWidth: number; // 文档宽度
    fontSize: number; // 基础字体大小
    baseURI: string; // 基础URI
  };
  frame: NodeData;  // 根节点数据
  fonts: Font[]; // 使用的字体
  assets: Record<string, Asset | null>; // 资源文件
  screenShot?: string;
}

export interface PositionInfo {
  x: number;      // 元素左上角的X坐标(相对于视口)
  y: number;      // 元素左上角的Y坐标(相对于视口)
  width: number;  // 元素的宽度(像素)
  height: number; // 元素的高度(像素)
  quad?: number[]; // 可选的四角坐标数组，表示元素的四个角点坐标 格式为[x1,y1,x2,y2,x3,y3,x4,y4]
}
```

## 🤝 贡献指南

欢迎提交Pull Request。对于重大变更，请先创建Issue讨论您想要做的修改。

## 📄 许可证

ISC

> 本项目是腾讯 AI 助手项目的一部分，专注于提供高质量的HTML解析功能。
