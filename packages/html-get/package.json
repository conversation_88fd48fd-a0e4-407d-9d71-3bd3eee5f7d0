{"name": "@tencent/html-get", "version": "1.0.3", "description": "本项目是腾讯 AI 助手项目的一部分，专注于提供高质量的 HTML 获取能力。支持全面的图片资源收集、智能重试机制、CORS 处理和精确的位置计算。", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "scripts": {"build": "tsup --env.NODE_ENV=production", "build:dev": "tsup --env.NODE_ENV=development", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "files": ["dist", "README.md"], "keywords": ["html", "get", "browser", "image", "assets", "parser", "typescript", "position", "transform"], "author": "", "license": "ISC", "devDependencies": {"@ai-assitant/typescript-config": "workspace:*", "@types/jest": "^29.5.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "ts-jest": "^29.1.0", "tsup": "^8.4.0", "typescript": "5.8.2"}}