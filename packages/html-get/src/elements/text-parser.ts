import { NodeData } from '../types';
import { getTextRange } from '../core';

interface TextRange {
  start: number;
  end: number;
  y: number;
}

/**
 * 获取文本节点数据
 * @param textNode 文本节点
 * @param getElemPosition 获取元素位置的函数
 * @returns 文本节点数据数组
 */
export function getTextNodeData(textNode: HTMLElement, getElemPosition: Function): NodeData[] | null {
  if (textNode.textContent == null || textNode.textContent.length == 0) {
    return null;
  }

  const ranges: TextRange[] = [];
  for (let i = 0; i < textNode.textContent.length; i++) {
    const r = getTextRange(textNode, i, i + 1, getElemPosition);
    if (ranges.length === 0) {
      ranges.push({
        start: i,
        end: i + 1,
        y: r.y || 0,
      });
    } else if (ranges.length > 0 && ranges[ranges.length - 1]!.y !== r.y) {
      // 换行了
      // 首先检查上一行：前面的段如果以空格结束，则拆成两个
      const lastRange = ranges[ranges.length - 1]!;
      const lastChar = textNode.textContent[lastRange.end - 1];
      if (lastChar == ' ' && lastRange.end !== lastRange.start + 1) {
        ranges.pop();
        ranges.push({
          start: lastRange.start,
          end: lastRange.end - 1,
          y: r.y || 0,
        });
        ranges.push({
          start: lastRange.end - 1,
          end: lastRange.end,
          y: r.y || 0,
        });
      }
      // 然后加上新的行
      ranges.push({
        start: i,
        end: i + 1,
        y: r.y || 0,
      });
    } else if (ranges.length > 0) {
      ranges[ranges.length - 1]!.end = i + 1;
    }
  }

  return ranges.map((r) => {
    const rect = getTextRange(textNode, r.start, r.end, getElemPosition);
    return rect;
  });
}

/**
 * 处理文本节点
 * @param elem HTML元素
 * @param frame 节点数据
 * @param getElemPosition 获取元素位置的函数
 * @returns 处理后的节点数据数组或null
 */
export function processText(elem: HTMLElement, frame: NodeData, getElemPosition: Function): NodeData[] | null {
  if (frame.type === 'TEXT') {
    const textNodes = getTextNodeData(elem, getElemPosition);
    if (!textNodes) return null;

    const fullTextNodes = textNodes.map((e) => {
      return {
        ...frame,
        ...e,
      };
    });
    return fullTextNodes;
  }
  return null;
}
