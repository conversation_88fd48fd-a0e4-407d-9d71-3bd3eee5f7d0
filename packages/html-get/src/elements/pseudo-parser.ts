import { NodeData } from '../types';
import { hasPseudoElement } from '../core';

/**
 * 处理伪元素的类型
 */
export enum PseudoElementType {
  BEFORE = 'before',
  AFTER = 'after',
  BOTH = 'both'
}

/**
 * 处理伪元素
 * @param elem HTML元素
 * @param frame 节点数据
 * @param rootRect 根元素位置信息
 * @param getElemStyles 获取元素样式的函数
 * @param getPseudoElemPosition 获取伪元素位置的函数
 * @param type 指定要处理的伪元素类型，默认处理两种伪元素
 * @returns 处理后的节点数据
 */
export function processPseudoElements(
  elem: HTMLElement,
  frame: NodeData,
  rootRect: any,
  getElemStyles: Function,
  getPseudoElemPosition: Function,
  type: PseudoElementType = PseudoElementType.BOTH
): NodeData {
  // 确保children数组存在
  frame.children = frame.children || [];

  // 处理 ::before 伪元素
  if ((type === PseudoElementType.BEFORE || type === PseudoElementType.BOTH) && hasPseudoElement(elem, '::before')) {
    const { styles: beforeStyles, content } = getElemStyles(elem, '::before');
    const elemPositionObj = getPseudoElemPosition(frame, beforeStyles, rootRect);
    const beforeFrame: NodeData = {
      ...elemPositionObj,
      type: 'FRAME',
      tag: '::before',
      attr: {},
      classList: [],
      styles: beforeStyles,
    };
    if (content) {
      beforeFrame.children = [
        {
          type: 'TEXT',
          ...elemPositionObj,
          value: content,
          isContent: true,
          font: beforeStyles.fontFamily,
        },
      ];
    }
    frame.children.push(beforeFrame);
  }

  // 处理 ::after 伪元素
  if ((type === PseudoElementType.AFTER || type === PseudoElementType.BOTH) && hasPseudoElement(elem, '::after')) {
    const { styles: afterStyles, content } = getElemStyles(elem, '::after');
    const elemPositionObj = getPseudoElemPosition(frame, afterStyles, rootRect, true);
    const afterFrame: NodeData = {
      ...elemPositionObj,
      type: 'FRAME',
      tag: '::after',
      attr: {},
      classList: [],
      styles: afterStyles,
    };
    if (content) {
      afterFrame.children = [
        {
          type: 'TEXT',
          ...elemPositionObj,
          value: content,
          isContent: true,
          font: afterStyles.fontFamily,
        },
      ];
    }
    frame.children.push(afterFrame);
  }

  return frame;
}
