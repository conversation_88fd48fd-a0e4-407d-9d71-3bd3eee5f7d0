import type { NodeData } from '../types';
import { initBaseNodeData } from '../core';
import { processText } from './text-parser';
import { processPseudoElements, PseudoElementType } from './pseudo-parser';
import { getElemStyles, getElemPosition, getPseudoElemPosition } from '../core';
import {
  processBodyHtml,
  processAnchor,
  processImage,
  processVideo,
  processSVG,
  setInputBoxText,
  processComponent,
} from './element-parser';

/**
 * 解析节点数据
 * @param elem HTML元素
 * @param rootRect 根元素位置信息
 * @returns 节点数据或节点数据数组或null
 */
export function parseNodeData(elem: HTMLElement, rootRect: any): NodeData | NodeData[] | null {
  if (!elem) return null;

  // 初始化基础节点数据
  let frame = initBaseNodeData(elem);

  if (!frame) return null;

  // 处理body和html
  processBodyHtml(frame);

  // 处理组件
  const componentResult = processComponent(elem, frame);
  if (componentResult) return componentResult;

  // 处理文本
  const textResult = processText(elem, frame, getElemPosition);
  if (textResult) return textResult;

  // 处理SVG
  const svgResult = processSVG(elem, frame, getElemPosition);
  if (svgResult) {
    return svgResult;
  }

  // 处理超链接
  processAnchor(elem, frame);

  // 处理图片
  processImage(elem, frame);

  // 处理视频
  processVideo(elem, frame);

  // 递归处理子节点
  if (!elem.childNodes) {
    return frame;
  }

  frame.children = [];

  // 添加伪元素 before
  processPseudoElements(elem, frame, rootRect, getElemStyles, getPseudoElemPosition, PseudoElementType.BEFORE);

  // 处理子节点
  for (const c of elem.childNodes) {
    const n = parseNodeData(c as HTMLElement, rootRect);
    if (n) {
      if (Array.isArray(n)) {
        frame.children.push(...n);
      } else {
        frame.children.push(n);
      }
    }
  }

  // 添加伪元素 after
  processPseudoElements(elem, frame, rootRect, getElemStyles, getPseudoElemPosition, PseudoElementType.AFTER);

  // 处理输入框
  if (frame.tag && ['input', 'select'].includes(frame.tag)) {
    setInputBoxText(elem, frame);
  }

  return frame;
}
