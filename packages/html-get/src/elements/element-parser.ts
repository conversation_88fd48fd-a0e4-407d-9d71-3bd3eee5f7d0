import { ImageNode, NodeData, SvgNode, VideoNode } from '../types';
import { extractSVGFromDataURI } from '../core';

// SVG相关的CSS属性常量
const SVG_RELATED_STYLES = {
  MASK: 'mask',
  MASK_IMAGE: 'maskImage',
  CLIP_PATH: 'clipPath',
  FILTER: 'filter',
} as const;

// 需要转换为SVG的样式值常量
const SVG_CONVERSION_VALUES = {
  NONE: 'none',
} as const;

/**
 * 处理标准HTML和BODY元素
 * @param frame 节点数据
 * @returns 处理后的节点数据
 */
export function processBodyHtml(frame: NodeData): NodeData {
  if (frame.type === 'FRAME' && frame.tag && ['html', 'body'].includes(frame.tag.toLowerCase())) {
    frame.height = Math.max(frame.height || 0, document.documentElement.scrollHeight);
    if (['html', 'body'].includes(frame.tag.toLowerCase())) {
      if (frame.styles) {
        frame.styles.backgroundColor = '#fff';
      }
    }
  }
  return frame;
}

/**
 * 处理超链接元素
 * @param elem HTML元素
 * @param frame 节点数据
 * @returns 处理后的节点数据
 */
export function processAnchor(elem: HTMLElement, frame: NodeData): NodeData {
  if (frame.tag?.toLowerCase() === 'a') {
    frame.attr = {
      ...frame.attr,
      href: elem.getAttribute('href') || undefined,
    };
  }
  return frame;
}

function isImageNode(frame: NodeData): frame is ImageNode {
  return frame.tag?.toLowerCase() === 'img';
}

/**
 * 处理图片元素
 * @param elem HTML元素
 * @param frame 节点数据
 * @returns 处理后的节点数据
 */
export function processImage(elem: HTMLElement, frame: NodeData): NodeData | ImageNode {
  if (isImageNode(frame)) {
    frame.attr = {
      ...frame.attr,
      naturalHeight: elem.getAttribute('height') || undefined,
      naturalWidth: elem.getAttribute('width') || undefined,
      srcset: elem.getAttribute('srcset') || undefined,
    };
    frame.$url = elem.getAttribute('src') || undefined;
  }
  if (frame.tag?.toLowerCase() === 'figure') {
    //TODO(ryaanwang) 这里需要适配figure
  }
  return frame;
}

function isVideoNode(frame: NodeData): frame is VideoNode {
  return frame.tag?.toLowerCase() === 'video';
}

/**
 * 处理视频元素
 * @param elem HTML元素
 * @param frame 节点数据
 * @returns 处理后的节点数据
 */
export function processVideo(elem: HTMLElement, frame: NodeData): NodeData | VideoNode {
  if (isVideoNode(frame)) {
    frame.attr = {
      ...frame.attr,
      naturalHeight: elem.parentElement?.getAttribute('height') || undefined,
      naturalWidth: elem.parentElement?.getAttribute('width') || undefined,
      srcset: elem.getAttribute('srcset') || undefined,
    };
    frame.$url = elem.getAttribute('src') || undefined;
    if (!frame.$url && elem.childNodes && (elem.childNodes[0] as HTMLElement)?.tagName?.toLowerCase() === 'source') {
      frame.$url = (elem.childNodes[0] as HTMLElement).getAttribute('src') || undefined;
    }
  }
  return frame;
}

/**
 * 获取SVG节点数据
 * @param elem SVG元素
 * @param getElemPosition 获取元素位置的函数
 * @returns SVG节点数据
 */
export function getSVGNodeData(elem: HTMLElement, getElemPosition: Function): NodeData {
  return {
    children: [
      {
        type: 'SVG',
        svg: elem.outerHTML,
        ...getElemPosition(elem),
      },
    ],
  };
}

export function getMaskSVGNodeData(frame: NodeData, elem: HTMLElement, getElemPosition: Function): SvgNode | NodeData {
  const maskImage = frame?.styles?.maskImage || '';

  // 从 Data URI 中提取 SVG 内容
  const svgContent = maskImage ? extractSVGFromDataURI(maskImage) : '';
  // 设置透明背景
  if(frame.styles){
    // 设置透明背景
    frame.styles['color'] = frame.styles['backgroundColor'];
    frame.styles['backgroundColor'] = 'rgba(0, 0, 0, 0)';
  }
  return {
    children: [
      {
        type: 'SVG',
        svg: svgContent,
        ...getElemPosition(elem),
      },
    ],
  };
}

/**
 * 检查是否需要转换为SVG节点
 * @param frame 节点数据
 * @returns 是否需要转换为SVG
 */
function shouldConvertToSVG(frame: NodeData): boolean {
  if (!frame.styles) return false;

  // 检查mask相关样式
  const maskStyle = frame.styles[SVG_RELATED_STYLES.MASK];
  const maskImageStyle = frame.styles[SVG_RELATED_STYLES.MASK_IMAGE];

  if (maskStyle && maskStyle !== SVG_CONVERSION_VALUES.NONE) {
    return true;
  }

  if (maskImageStyle && maskImageStyle !== SVG_CONVERSION_VALUES.NONE) {
    return true;
  }

  // 检查clip-path样式
  const clipPathStyle = frame.styles[SVG_RELATED_STYLES.CLIP_PATH];
  if (clipPathStyle && clipPathStyle !== SVG_CONVERSION_VALUES.NONE) {
    return true;
  }

  // 检查filter样式
  const filterStyle = frame.styles[SVG_RELATED_STYLES.FILTER];
  if (filterStyle && filterStyle !== SVG_CONVERSION_VALUES.NONE) {
    return true;
  }

  return false;
}

/**
 * 处理SVG元素
 * @param elem HTML元素
 * @param frame 节点数据
 * @param getElemPosition 获取元素位置的函数
 * @returns 处理后的节点数据
 */
export function processSVG(elem: HTMLElement, frame: NodeData, getElemPosition: Function): NodeData | SvgNode | null {
  if (frame.tag?.toLowerCase() === 'svg') {
    return {
      ...frame,
      ...getSVGNodeData(elem, getElemPosition),
    } as SvgNode;
  }

  // 检查是否需要转换为SVG节点
  if (shouldConvertToSVG(frame)) {
    frame.tag = 'svg';
    return {
      ...frame,
      ...getMaskSVGNodeData(frame, elem, getElemPosition),
    } as SvgNode;
  }

  return null;
}

/**
 * 为输入框设置文本
 * @param elem 输入框元素
 * @param frame 节点数据
 */
export function setInputBoxText(elem: HTMLElement, frame: NodeData): void {
  if (!['input', 'select'].includes(frame.tag || '')) {
    return;
  }

  const value = (elem as HTMLInputElement).value || (elem as HTMLInputElement).placeholder;
  if (value && !['checkbox', 'radio'].includes((elem as HTMLInputElement).type)) {
    const x = (frame.x || 0) + parseFloat(frame.styles?.paddingLeft || '0');
    const y = (frame.y || 0) + parseFloat(frame.styles?.paddingTop || '0');
    const width = (frame.width || 0) - parseFloat(frame.styles?.paddingLeft || '0') * 2;
    const height = (frame.height || 0) - parseFloat(frame.styles?.paddingTop || '0') * 2;
    const rect = {
      left: x,
      top: y,
      width,
      height,
      right: x + width,
      bottom: y + height,
    };
    frame.children = frame.children || [];
    frame.children.push({
      type: 'TEXT',
      value: value,
      x: rect.left,
      y: rect.top,
      width: rect.width,
      height: rect.height,
      quad: [
        rect.left,
        rect.top, // 左上角
        rect.right,
        rect.top, // 右上角
        rect.right,
        rect.bottom, // 右下角
        rect.left,
        rect.bottom, // 左下角
      ],
    });
  }
}

/**
 * 处理组件元素
 * @param elem HTML元素
 * @param frame 节点数据
 * @returns 处理后的节点数据或null
 */
export function processComponent(elem: HTMLElement, frame: NodeData): NodeData | null {
  if ((elem.attributes as any)?.dc) {
    return {
      ...frame,
      type: 'COMPONENT',
      attr: {
        ...frame.attr,
        description: elem.innerText,
      },
    };
  }
  return null;
}
