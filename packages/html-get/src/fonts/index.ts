import { Font } from '../types';


/**
 * 解析文档字体大小
 * @returns 字体大小数值
 */
export function parseDocFontSize(): number {
  const match = window.getComputedStyle(document.body).fontSize?.match(/\d+/);
  if (match) {
    return Number(match[0]);
  }
  // 没解析出来，给一个默认值
  return 16;
}

/**
 * 获取文档中的所有字体
 * @returns 字体数组
 */
export function getFonts(): Font[] {
  const styleSheets = Array.from(document.styleSheets).filter((sheet): sheet is CSSStyleSheet => sheet !== null);
  const fonts: Font[] = [];
  styleSheets.forEach((sheet) => {
    try {
      if (sheet.cssRules) {
        const fontFaceRules = Array.from(sheet.cssRules).filter(
          (rule): rule is CSSFontFaceRule => rule instanceof CSSFontFaceRule,
        );
        fontFaceRules.forEach((rule) => {
          const font: Font = {
            fontFamily: rule.style.fontFamily.replace(/['"]/g, ''),
            fontStyle: rule.style.fontStyle,
            fontVariant: rule.style.fontVariant,
            fontWeight: rule.style.fontWeight,
            fontStretch: rule.style.fontStretch,
            fontDisplay: (rule as any).fontDisplay || 'auto',
            unicodeRange: (rule as any).unicodeRange || 'U+0-10FFFF',
            src: (rule as any).src || '',
          };
          fonts.push(font);
        });
      }
    } catch (e) {
      // Ignore errors accessing cross-origin stylesheets
    }
  });

  return fonts;
}
