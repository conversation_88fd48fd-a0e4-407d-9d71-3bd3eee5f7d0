import type { NodeData, PositionInfo } from '../types';

/**
 * 解析 CSS transform matrix 并提取偏移量
 * @param transform CSS transform 属性值
 * @returns 偏移量 {x, y}
 */
function parseTransformMatrix(transform: string): { x: number; y: number } {
  if (!transform || transform === 'none') {
    return { x: 0, y: 0 };
  }

  // 匹配 matrix(a, b, c, d, tx, ty) 格式
  const matrixMatch = transform.match(/matrix\(([^)]+)\)/);
  if (matrixMatch) {
    const values = matrixMatch[1].split(',').map(v => parseFloat(v.trim()));
    if (values.length >= 6) {
      return { x: values[4] || 0, y: values[5] || 0 };
    }
  }

  // 匹配 translate(x, y) 或 translateX(x) 或 translateY(y) 格式
  const translateMatch = transform.match(/translate(?:X|Y)?\(([^)]+)\)/);
  if (translateMatch) {
    const values = translateMatch[1].split(',').map(v => parseFloat(v.trim()));
    if (values.length >= 2) {
      return { x: values[0] || 0, y: values[1] || 0 };
    } else if (values.length === 1) {
      // translateX 或 translateY
      if (transform.includes('translateX')) {
        return { x: values[0] || 0, y: 0 };
      } else if (transform.includes('translateY')) {
        return { x: 0, y: values[0] || 0 };
      }
      // 单个值的 translate 默认是 X 方向
      return { x: values[0] || 0, y: 0 };
    }
  }

  return { x: 0, y: 0 };
}

/**
 * 获取元素的定位上下文位置
 * @param elem HTML元素
 * @returns 定位上下文的位置信息
 */
function getContainingBlockPosition(elem: HTMLElement): { x: number; y: number } {
  let parent = elem.parentElement;

  while (parent) {
    const computedStyle = window.getComputedStyle(parent);
    const position = computedStyle.position;

    // 如果父元素是定位元素（relative, absolute, fixed, sticky），则它是定位上下文
    if (position !== 'static') {
      const parentRect = parent.getBoundingClientRect();
      // 对于定位上下文，也需要加上滚动偏移量
      const scrollX = window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;
      const scrollY = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
      return { x: parentRect.left + scrollX, y: parentRect.top + scrollY };
    }

    parent = parent.parentElement;
  }

  // 如果没有找到定位上下文，则使用文档根元素
  return { x: 0, y: 0 };
}

/**
 * 获取元素的位置信息
 * @param elem HTML元素或Range对象
 * @returns 位置信息对象
 */
export function getElemPosition(elem: HTMLElement | Range): PositionInfo {
  try {
    let rect = elem?.getBoundingClientRect();
    const { left, top, width, height, right, bottom } = rect;

    // 不添加滚动偏移量，保持原始的视口坐标
    // 这样可以确保位置计算的一致性
    let adjustedLeft = left;
    let adjustedTop = top;
    let adjustedRight = right;
    let adjustedBottom = bottom;

    // 如果是 HTMLElement，进行更复杂的位置计算
    if (elem instanceof HTMLElement) {
      const computedStyle = window.getComputedStyle(elem);
      const position = computedStyle.position;
      const transform = computedStyle.transform;

      // 处理 CSS transform 变换
      if (transform && transform !== 'none') {
        const offset = parseTransformMatrix(transform);

        // 从变换后的位置反推原始布局位置
        adjustedLeft = left - offset.x;
        adjustedTop = top - offset.y;
        adjustedRight = right - offset.x;
        adjustedBottom = bottom - offset.y;
      }

      // 处理绝对定位元素
      if (position === 'absolute' || position === 'fixed') {
        // 对于绝对定位元素，我们需要考虑其定位属性
        const leftValue = computedStyle.left;
        const topValue = computedStyle.top;
        const rightValue = computedStyle.right;
        const bottomValue = computedStyle.bottom;

        // 如果有明确的定位值，尝试使用定位上下文计算更准确的位置
        if (leftValue !== 'auto' || topValue !== 'auto' || rightValue !== 'auto' || bottomValue !== 'auto') {
          const containingBlock = getContainingBlockPosition(elem);

          // 如果有 left 值，使用它
          if (leftValue !== 'auto') {
            const leftPx = parseFloat(leftValue);
            if (!isNaN(leftPx)) {
              adjustedLeft = containingBlock.x + leftPx;
              adjustedRight = adjustedLeft + width;
            }
          }
          // 如果没有 left 但有 right 值
          else if (rightValue !== 'auto') {
            const rightPx = parseFloat(rightValue);
            if (!isNaN(rightPx)) {
              // 这里需要知道容器的宽度，暂时使用当前计算结果
              // adjustedRight = containingBlock.x + containerWidth - rightPx;
              // adjustedLeft = adjustedRight - width;
            }
          }

          // 如果有 top 值，使用它
          if (topValue !== 'auto') {
            const topPx = parseFloat(topValue);
            if (!isNaN(topPx)) {
              adjustedTop = containingBlock.y + topPx;
              adjustedBottom = adjustedTop + height;
            }
          }
          // 如果没有 top 但有 bottom 值
          else if (bottomValue !== 'auto') {
            const bottomPx = parseFloat(bottomValue);
            if (!isNaN(bottomPx)) {
              // 这里需要知道容器的高度，暂时使用当前计算结果
              // adjustedBottom = containingBlock.y + containerHeight - bottomPx;
              // adjustedTop = adjustedBottom - height;
            }
          }
        }
      }
    }

    return {
      x: adjustedLeft,
      y: adjustedTop,
      width,
      height,
      quad: [
        adjustedLeft, adjustedTop,         // 左上角
        adjustedRight, adjustedTop,        // 右上角
        adjustedRight, adjustedBottom,     // 右下角
        adjustedLeft, adjustedBottom       // 左下角
      ],
    };
  } catch (e) {
    console.error('Error getting element position:', e);
    return {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    };
  }
}

/**
 * 获取伪元素的位置信息
 * @param parentFrame 父元素的NodeData
 * @param styles 伪元素的样式
 * @param isAfter 是否是::after伪元素
 * @returns 位置信息对象
 */
export function getPseudoElemPosition(
  parentFrame: NodeData,
  styles: Record<string, string>,
  isAfter: boolean = false
): {
  x: number;
  y: number;
  width: number;
  height: number;
  quad?: number[];
} {
  try {
    const { display } = styles;
    const isInline = display && display.includes('inline');

    // 设置默认值防止未定义
    const parentX = parentFrame.x || 0;
    const parentY = parentFrame.y || 0;
    const parentWidth = parentFrame.width || 0;
    const parentHeight = parentFrame.height || 0;

    let x = parentX;
    let y = parentY;

    // 对于块级元素，::before在元素内容的开始，::after在元素内容的结束
    if (isAfter && !isInline) {
      y = parentY + parentHeight - parseFloat(styles.height || '0');
    }

    // 对于行内元素，::before在元素内容的左侧，::after在元素内容的右侧
    if (isAfter && isInline) {
      x = parentX + parentWidth - parseFloat(styles.width || '0');
    }

    const width = parseFloat(styles.width || '0');
    const height = parseFloat(styles.height || '0');

    return {
      x,
      y,
      width,
      height,
      quad: [
        x, y,              // 左上角
        x + width, y,      // 右上角
        x + width, y + height, // 右下角
        x, y + height      // 左下角
      ],
    };
  } catch (e) {
    console.error('Error getting pseudo element position:', e);
    return {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    };
  }
}