import type { FetchedAsset, Document, ImageCollectionResult } from '../types';
import { extractSVGFromDataURI } from './utils';

// Chrome API类型声明
declare global {
  interface Window {
    chrome?: {
      runtime?: {
        sendMessage: (message: any, callback: (response: any) => void) => void;
        lastError?: { message: string };
      };
    };
  }
}

// 全局chrome对象类型声明
declare const chrome:
  | {
      runtime: {
        sendMessage: (message: any, callback: (response: any) => void) => void;
        lastError?: { message: string };
      };
    }
  | undefined;

// 收集所有图片类型资源（包括CSS背景图）
function collectAllImages(): ImageCollectionResult {
  const result: ImageCollectionResult = {
    imgTags: [] as string[], // <img> 标签
    inlineSvgs: [] as string[], // 内联SVG元素
    cssBackgrounds: [] as string[], // CSS背景图
    cssGradients: 0, // 统计渐变数量（非图片）
    mp4Videos: [] as string[], // MP4视频
  };

  // 1. 收集常规图片资源（更全面的选择器）
  const imageSelectors = [
    'img',
    'image', // SVG image
    'picture',
    'source',
    '[data-src]', // 懒加载图片
  ].join(', ');

  const imageElements = [...document.querySelectorAll(imageSelectors)];

  imageElements.forEach((element) => {
    // 获取各种可能的图片源
    const sources = [
      element.getAttribute('src'),
      element.getAttribute('data-src'),
      element.getAttribute('href'),
      element.getAttribute('xlink:href'),
    ].filter(Boolean);

    // 处理 srcset 属性
    const srcset = element.getAttribute('srcset');
    if (srcset) {
      const srcsetUrls = srcset
        .split(',')
        .map((item) => {
          const url = item.trim().split(' ')[0];
          return url;
        })
        .filter(Boolean);
      sources.push(...srcsetUrls);
    }

    result.imgTags.push(...(sources as string[]));
  });

  // 2. 收集内联SVG
  result.inlineSvgs = [...document.getElementsByTagName('svg')].map((svg) => svg.outerHTML);

  // 3. 收集CSS背景图片（遍历所有元素）
  const allElements = [...document.querySelectorAll('*')];
  allElements.forEach((el) => {
    const styles = getComputedStyle(el);

    // 提取 background-image 和 background 简写属性
    const backgroundProps = [styles.backgroundImage, styles.background];

    backgroundProps.forEach((bgProp) => {
      if (!bgProp || bgProp === 'none') return;

      // 改进的URL提取正则，支持嵌套函数
      const urlMatches = bgProp.match(/url\s*\(\s*(['"]?)([^'")]*)\1\s*\)/gi);
      if (urlMatches) {
        urlMatches.forEach((match) => {
          const urlMatch = match.match(/url\s*\(\s*(['"]?)([^'")]*)\1\s*\)/i);
          if (urlMatch && urlMatch[2]) {
            const url = urlMatch[2];
            // 过滤渐变
            if (
              !url.startsWith('linear-gradient') &&
              !url.startsWith('radial-gradient') &&
              !url.startsWith('conic-gradient')
            ) {
              result.cssBackgrounds.push(url);
            } else {
              result.cssGradients++;
            }
          }
        });
      }
    });

    // 提取mask
    if (styles.mask !== 'none' || styles.maskImage !== 'none') {
      const maskSvg = extractSVGFromDataURI(styles.maskImage);
      if (maskSvg) {
        result.inlineSvgs.push(maskSvg);
      }
    }
  });

  // 4. 收集MP4视频（去重并过滤）
  result.mp4Videos = [...document.querySelectorAll('video, source')]
    .map((el) => (el as HTMLVideoElement).src || el.getAttribute('src'))
    .filter((url) => url?.endsWith('.mp4')) as string[];

  // 去重处理
  return {
    imgTags: [...new Set(result.imgTags.filter(Boolean))],
    inlineSvgs: result.inlineSvgs, // 内联SVG不去重
    cssBackgrounds: [...new Set(result.cssBackgrounds.filter(Boolean))],
    cssGradients: result.cssGradients,
    mp4Videos: [...new Set(result.mp4Videos.filter(Boolean))],
  };
}

// 并发控制函数
async function processWithConcurrencyLimit<T>(
  items: T[],
  processor: (item: T) => Promise<void>,
  limit: number = 5,
): Promise<void> {
  const executing: Promise<void>[] = [];

  for (const item of items) {
    const promise = processor(item).finally(() => {
      executing.splice(executing.indexOf(promise), 1);
    });

    executing.push(promise);

    if (executing.length >= limit) {
      await Promise.race(executing);
    }
  }

  await Promise.all(executing);
}

// CORS 降级处理
async function handleCORSFallback(url: string): Promise<FetchedAsset | null> {
  try {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    await new Promise<void>((resolve, reject) => {
      img.onload = () => resolve();
      img.onerror = () => reject(new Error('Image load failed'));
      img.src = url;
    });

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Canvas context not available');

    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);

    const dataUrl = canvas.toDataURL('image/png');
    const base64Content = dataUrl.split(',')[1];

    return {
      base64Encoded: true,
      content: base64Content,
      mimeType: 'image/png',
      size: base64Content.length,
      fetchTime: 0, // Canvas 操作很快，设为0
    };
  } catch {
    return null;
  }
}

export async function getAssets(): Promise<Document['assets']> {
  const result: Document['assets'] = {};

  // 使用collectAllImages收集页面上的所有图片资源
  const allImages = collectAllImages();

  // 处理单个图片资源的函数
  const processImageUrl = async (url: string, type: 'img' | 'bg' | 'video') => {
    if (!url) return;

    try {
      // 处理 SVG 字符串（直接是 SVG 内容）
      if (url.startsWith('<svg')) {
        result[url] = {
          base64Encoded: false,
          content: url,
          mimeType: 'image/svg+xml',
        };
        return;
      }

      // 处理 data: URL（base64 或其他内联数据）
      if (url.startsWith('data:')) {
        const mimeMatch = url.match(/^data:([^;]+);/);
        const mimeType = mimeMatch ? mimeMatch[1] : 'application/octet-stream';

        result[url] = {
          base64Encoded: true,
          content: url,
          mimeType: mimeType,
        };
        return;
      }

      // 处理相对路径，转换为绝对路径
      const absoluteUrl = new URL(url, document.baseURI).href;

      // 处理外部资源URL
      const asset = await fetchAssetWithRetry(absoluteUrl);
      result[absoluteUrl] = asset;

      // 如果原URL和绝对URL不同，也存储原URL的引用
      if (url !== absoluteUrl) {
        result[url] = asset;
      }
    } catch (error) {
      console.error(`获取${type}资源失败: ${url}`, error);
      result[url] = null;
    }
  };

  // 1. 处理常规图片标签资源（并发控制）
  await processWithConcurrencyLimit(allImages.imgTags, (url) => processImageUrl(url, 'img'), 5);

  // 2. 处理CSS背景图片（并发控制）
  await processWithConcurrencyLimit(allImages.cssBackgrounds, (url) => processImageUrl(url, 'bg'), 5);

  // 3. 处理内联SVG（同步处理，无需网络请求）
  allImages.inlineSvgs.forEach((svgContent) => {
    // 为内联SVG生成唯一标识符
    const svgId = `inline-svg-${Math.random().toString(36).substr(2, 9)}`;
    result[svgId] = {
      base64Encoded: false,
      content: svgContent,
      mimeType: 'image/svg+xml',
    };
  });

  // 4. 处理MP4视频（并发控制）
  await processWithConcurrencyLimit(
    allImages.mp4Videos,
    (url) => processImageUrl(url, 'video'),
    3, // 视频文件较大，减少并发数
  );

  return result;
}

// 带重试机制的资源获取函数
export async function fetchAssetWithRetry(url: string, maxRetries: number = 3): Promise<FetchedAsset> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await fetchAsset(url);
    } catch (error) {
      lastError = error as Error;

      // 如果是CORS错误，尝试降级处理
      if (error instanceof TypeError && error.message.includes('CORS')) {
        const fallbackResult = await handleCORSFallback(url);
        if (fallbackResult) {
          return fallbackResult;
        }
      }

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries - 1) {
        const delay = Math.min(1000 * Math.pow(2, attempt), 5000); // 指数退避，最大5秒
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  // 所有重试都失败，抛出最后的错误
  throw lastError || new Error(`Failed to fetch asset after ${maxRetries} attempts`);
}

// 检测是否在Chrome插件环境中
function isChromeExtension(): boolean {
  return typeof chrome !== 'undefined' && !!chrome.runtime;
}

// Chrome插件环境下的图片获取函数
async function fetchAssetInChromeExtension(url: string): Promise<FetchedAsset> {
  const startTime = performance.now();
  if (!!chrome) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ type: 'GET_IMAGE', url: url }, (response) => {
        const fetchTime = performance.now() - startTime;

        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        if (response.base64) {
          // 从data URL中提取base64内容和MIME类型
          const dataUrlMatch = response.base64.match(/^data:([^;]+);base64,(.+)$/);
          if (dataUrlMatch) {
            const [, mimeType, base64Content] = dataUrlMatch;
            resolve({
              base64Encoded: true,
              content: base64Content,
              mimeType: mimeType,
              size: base64Content.length,
              fetchTime: Math.round(fetchTime),
            });
          } else {
            // 如果不是标准的data URL格式，直接使用返回的内容
            resolve({
              base64Encoded: true,
              content: response.base64,
              mimeType: 'application/octet-stream',
              size: response.base64.length,
              fetchTime: Math.round(fetchTime),
            });
          }
        } else if (response.error) {
          reject(new Error(`获取图片失败: ${response.error}`));
        } else {
          reject(new Error('未知的响应格式'));
        }
      });
    });
  }
  return {
    base64Encoded: false,
    content: '',
    mimeType: 'application/octet-stream',
    size: 0,
    fetchTime: 0,
  };
}

export async function fetchAsset(url: string): Promise<FetchedAsset> {
  // 如果在Chrome插件环境中，使用插件的获取方式
  if (isChromeExtension()) {
    return fetchAssetInChromeExtension(url);
  }

  // 原有的fetch方式
  const startTime = performance.now();
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

  try {
    const response = await fetch(url, {
      signal: controller.signal,
      mode: 'cors',
      credentials: 'omit',
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const fetchTime = performance.now() - startTime;

    // 优化 base64 编码性能
    const uint8Array = new Uint8Array(arrayBuffer);
    const base64String = btoa(uint8Array.reduce((data, byte) => data + String.fromCharCode(byte), ''));

    const contentType = response.headers.get('content-type') || 'application/octet-stream';

    return {
      base64Encoded: true,
      content: base64String,
      mimeType: contentType,
      size: arrayBuffer.byteLength,
      fetchTime: Math.round(fetchTime),
    };
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof Error) {
      const errorMessage = error.name === 'AbortError' ? `Request timeout for ${url}` : error.message;

      throw new Error(errorMessage);
    }

    throw new Error(`Unknown error fetching ${url}`);
  }
}
