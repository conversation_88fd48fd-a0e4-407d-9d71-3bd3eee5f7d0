
/**
 * 获取元素的计算样式
 * @param elem HTML元素
 * @param pseudoElem 伪元素（可选）
 * @returns 样式对象和内容
 */
export function getElemStyles(elem: HTMLElement, pseudoElem?: string): {
  styles: Record<string, string>;
  content?: string;
} {
  try {
    const attributes = [
      'width',
      'height',
      'left',
      'top',
      'right',
      'bottom',
      'marginTop',
      'marginLeft',
      'marginRight',
      'marginBottom',
      'accentColor',
      'alignItems',
      'alignSelf',
      'appearance',
      'backdropFilter',
      'background',
      'backgroundClip',
      'backgroundColor',
      'backgroundImage',
      'backgroundOrigin',
      'backgroundPosition',
      'backgroundRepeat',
      'backgroundSize',
      'border',
      'borderBottomLeftRadius',
      'borderBottomRightRadius',
      'borderBottomStyle',
      'borderColor',
      'borderLeftStyle',
      'borderRadius',
      'borderRightStyle',
      'borderStyle',
      'borderTopLeftRadius',
      'borderTopRightRadius',
      'borderTopStyle',
      'borderWidth',
      'borderTopWidth',
      'borderRightWidth',
      'borderBottomWidth',
      'borderLeftWidth',
      'boxShadow',
      'boxSizing',
      'clip',
      'clipPath',
      'color',
      'content',
      'display',
      'direction',
      'fill',
      'filter',
      'flexBasis',
      'flexDirection',
      'flexWrap',
      'float',
      'fontFamily',
      'fontSize',
      'fontStretch',
      'fontStyle',
      'fontWeight',
      'gridAutoFlow',
      'isolation',
      'justifyItems',
      'justifyContent',
      'letterSpacing',
      'lineHeight',
      'rowGap',
      'columnGap',
      'margin',
      'maxHeight',
      'maxWidth',
      'minHeight',
      'minWidth',
      'mask',
      'maskImage',
      'maskMode',
      'maskPosition',
      'maskRepeat',
      'maskSize',
      'objectFit',
      'objectPosition',
      'outlineColor',
      'outlineOffset',
      'outlineStyle',
      'outlineWidth',
      'overflow',
      'overflowX',
      'overflowY',
      'paddingBottom',
      'paddingLeft',
      'paddingRight',
      'paddingTop',
      'padding',
      'position',
      'rotate',
      'scale',
      'textAlign',
      'textDecorationLine',
      'textOverflow',
      'textShadow',
      'textTransform',
      'transform',
      'verticalAlign',
      'visibility',
      'webkitMaskClip',
      'webkitMaskImage',
      'webkitMaskOrigin',
      'webkitMaskPosition',
      'webkitMaskRepeat',
      'webkitMaskSize',
      'webkitTextFillColor',
      'webkitTextStrokeColor',
      'webkitTextStrokeWidth',
      'whiteSpace',
      'writingMode',
      'zIndex',
      'order',
      'flexGrow',
      'flexShrink',
      'opacity',
      'mask',
      'maskImage',
    ];
    const computedStyle = window.getComputedStyle(elem, pseudoElem) as any;
    const result: Record<string, string> = {};

    // 复制所有计算样式到结果对象
    for (const key of attributes) {
      result[key] = computedStyle[key];
    }

    // 特别处理常用样式
    const importantStyles = [
      'color', 'backgroundColor', 'fontSize', 'fontFamily', 'fontWeight',
      'display', 'position', 'width', 'height', 'margin', 'padding',
      'border', 'borderRadius', 'boxShadow', 'textAlign', 'lineHeight',
      'zIndex', 'opacity', 'transform', 'transition', 'animation',
      'flexDirection', 'justifyContent', 'alignItems', 'gap',
      'gridTemplateColumns', 'gridTemplateRows'
    ];

    for (const style of importantStyles) {
      const value = computedStyle.getPropertyValue(style);
      if (value) {
        result[style] = value;
      }
    }

    // 处理伪元素的content
    let content;
    if (pseudoElem) {
      content = computedStyle.getPropertyValue('content');
      if (content === 'none' || content === 'normal') {
        content = undefined;
      } else if (content.startsWith('"') && content.endsWith('"')) {
        // 移除外部引号
        content = content.substring(1, content.length - 1);
      }
      return {
        styles: result,
        content
      };
    }

    return {
      styles: result,
    };
  } catch (e) {
    console.error('Error getting element styles:', e);
    return { styles: {} };
  }
}
