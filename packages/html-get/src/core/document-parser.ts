import type { NodeData, Document } from '../types';
import { getElemPosition } from './position.js';
import { parseDocFontSize, getFonts } from '../fonts';
import { getAssets } from './collect-images.js';

/**
 * 获取HTML元素的解析数据
 * @param elem HTML元素
 * @param parseNodeData 节点解析函数
 * @returns 文档数据
 */
export async function createDocumentData(
  elem: HTMLElement,
  parseNodeData: (elem: HTMLElement, rootRect: any) => NodeData | NodeData[] | null
): Promise<Document> {
  const rootRect = { ...getElemPosition(elem) };

  // 解析HTML元素
  const frameData = parseNodeData(elem, rootRect) as NodeData;
  // 获取图片资源
  const assetsRes = await getAssets()

  // 获取文档信息
  const documentInfo: Document = {
    name: document.title || 'Untitled',
    doc: {
      innerHeight: window.innerHeight,
      innerWidth: window.innerWidth,
      contentHeight: document.documentElement.scrollHeight,
      fontSize: parseDocFontSize(),
      baseURI: document.baseURI
    },
    frame: frameData,
    fonts: getFonts(),
    assets: assetsRes
  };

  return documentInfo;
}