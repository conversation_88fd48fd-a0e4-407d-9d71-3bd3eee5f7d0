import type { NodeData } from '../types';
import { getIsVisible, getNodeType, getAttr, isEmptyText } from './utils';
import { getElemPosition } from './position';
import { getElemStyles } from './styles';

/**
 * 初始化基础节点数据
 * @param elem HTML元素
 * @returns 基础节点数据
 */
export function initBaseNodeData(elem: HTMLElement): NodeData | null {
  if (!elem) return null;

  const parentElement = elem.nodeType === Node.TEXT_NODE ? elem.parentElement : elem;
  const isVisibleElem = parentElement ? getIsVisible(parentElement) : false;
  if (!isVisibleElem) {
    return null;
  }

  let nodeType = elem?.dataset?.nodeType;
  if (
    !nodeType ||
    ['TEXT', 'GROUP', 'LINE', 'STAR', 'VECTOR', 'ELLIPSE', 'POLYGON'].includes(nodeType) ||
    (nodeType === 'RECTANGLE' && elem.childNodes)
  ) {
    const typeResult = getNodeType(elem.nodeType);
    nodeType = typeResult || undefined;
  }

  if (nodeType == null) {
    return null;
  }

  const frame: NodeData = elem.nodeType === Node.TEXT_NODE ? {
      tag: parentElement?.tagName?.toLowerCase(),
      type: nodeType,
    } : {
      tag: elem.tagName?.toLowerCase(),
      type: nodeType,
      attr: getAttr(elem),
      classList: elem.classList ? Array.from(elem.classList) : undefined,
      ...getElemPosition(elem),
      ...getElemStyles(elem),
    };

  // 去掉空白的 text 节点
  if (frame.type && isEmptyText(frame.type, elem.textContent || '')) {
    return null;
  }

  return frame;
}
