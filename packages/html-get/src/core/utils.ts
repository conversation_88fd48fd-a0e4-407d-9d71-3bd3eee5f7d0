import type { NodeData } from '../types';

/**
 * 检查元素是否可见
 * @param elem HTML元素
 * @returns 是否可见
 */
export function getIsVisible(elem: HTMLElement): boolean {
  try {
    const style = window.getComputedStyle(elem);
    const isVisible = style && style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    return isVisible;
  } catch (e) {
    return true; // 默认返回可见
  }
}

/**
 * 检查文本是否为空（只包含空白字符）
 * @param nodeType 节点类型
 * @param text 文本内容
 * @returns 是否为空文本
 */
export function isEmptyText(nodeType: string, text: string): boolean {
  if (nodeType != 'TEXT') {
    return false;
  }
  // 使用正则表达式匹配空白字符（包括空格、制表符、换行符等）
  const regex = /^\s*$/;
  // 如果字符串匹配正则表达式，则为空
  return regex.test(text);
}

/**
 * 检查元素是否有伪元素
 * @param element HTML元素
 * @param pseudoElement 伪元素选择器
 * @returns 是否有伪元素
 */
export function hasPseudoElement(element: HTMLElement, pseudoElement: string): boolean {
  if (element.nodeType !== Node.ELEMENT_NODE) {
    return false;
  }
  const pseudoElementStyle = window.getComputedStyle(element, pseudoElement);
  const content = pseudoElementStyle.getPropertyValue('content');
  // 检查content是否存在且非空（不为 'none' 或 'normal'）
  return Boolean(content && content !== 'none' && content !== 'normal');
}

/**
 * 获取元素属性
 * @param elem HTML元素
 * @returns 元素属性对象
 */
export function getAttr(elem: HTMLElement): Record<string, any> {
  try {
    return {
      id: elem.id,
      nodeId: elem?.dataset?.nodeId,
      ariaLabel: elem?.getAttribute('aria-label'),
    };
  } catch (e) {
    console.error(" ~ getAttr ~ error:", e)
    return {};
  }
}

/**
 * 获取节点类型
 * @param nodeType 节点类型数值
 * @returns 节点类型字符串
 */
export function getNodeType(nodeType: number): string | null {
  switch (nodeType) {
    case Node.ELEMENT_NODE:
      return 'FRAME';
    case Node.TEXT_NODE:
      return 'TEXT';
    case Node.COMMENT_NODE:
      return null;
    default:
      return `UNKNOWN:${nodeType}`;
  }
}

/**
 * 获取文本范围
 * @param textNode 文本节点
 * @param start 开始位置
 * @param end 结束位置
 * @returns 文本节点数据
 */
export function getTextRange(textNode: HTMLElement, start: number, end: number, getElemPosition: Function): NodeData {
  const range = document.createRange();
  range.setStart(textNode, start);
  range.setEnd(textNode, end);

  return {
    ...getElemPosition(range),
    value: range.toString().replaceAll(/^\s+|\s+$/g, ' '),
  };
}

/**
 * 从 url() 声明中提取内容
 * @param data 包含url()声明的字符串
 * @returns url()中的内容，没有匹配则返回undefined
 */
export function extractSVGFromDataURI(data: string): string | undefined {
  // 匹配 url("...") 或 url('...') 格式
  const regex = /url\((["'])(.*?)\1\)/;
  const match = data.match(regex);
  if (!match?.[2]) return undefined;

  const urlContent = match[2];
  // 检查是否是SVG数据URI
  if (urlContent.startsWith('data:image/svg+xml')) {
    // 检查utf8编码
    const utf8Index = urlContent.indexOf(';utf8,');
    if (utf8Index !== -1) {
      const encoded = urlContent.slice(utf8Index + 6);
      try {
        return decodeURIComponent(encoded);
      } catch (e) {
        console.error('Failed to decode UTF-8 SVG data URI:', e);
      }
    }
    // 检查base64编码
    const base64Index = urlContent.indexOf('base64,');
    if (base64Index !== -1) {
      const encoded = urlContent.slice(base64Index + 7);
      try {
        return decodeURIComponent(atob(encoded));
      } catch (e) {
        console.error('Failed to decode base64 SVG data URI:', e);
      }
    }
    // 其他情况直接解码整个内容
    return decodeURIComponent(urlContent);
  }
  return urlContent;
}
