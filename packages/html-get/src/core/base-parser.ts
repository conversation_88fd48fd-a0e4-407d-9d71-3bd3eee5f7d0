import type { NodeData } from '../types';
import { getIsVisible, isEmptyText, getNodeType, getAttr } from './utils';

/**
 * 初始化节点基础数据
 * @param elem HTML元素
 * @returns 基础节点数据
 */
export function initBaseNodeData(elem: HTMLElement): NodeData | null {
  // 获取工具函数
  const getElemStyles = (window as any).parseutil.getElemStyles;
  const getElemPosition = (window as any).parseutil.getElemPosition;
  
  const parentElement = elem.nodeType === Node.TEXT_NODE ? elem.parentElement : elem;
  if (parentElement === null) {
    return null;
  }
  
  const isVisibleElem = getIsVisible(parentElement);
  if (!isVisibleElem) {
    return null;
  }
  let nodeType = elem?.dataset?.nodeType;
  if (
    !nodeType || 
    ['TEXT', 'GROUP', 'LINE','STAR','VECTOR','ELLIPSE','POLYGON'].includes(nodeType) ||
    (nodeType === 'RECTANGLE' && elem.childNodes)
  ) {
    nodeType = getNodeType(elem.nodeType) || undefined;
  }
  
  if (nodeType == null) {
    return null;
  }
  
  const frame: NodeData = {
    tag: elem.tagName?.toLowerCase(),
    type: nodeType,
    attr: getAttr(elem),
    classList: elem.classList ? Array.from(elem.classList) : undefined,
    ...getElemPosition(elem),
    ...getElemStyles(elem),
  };
  
  // 去掉空白的 text 节点
  if (frame.type && isEmptyText(frame.type, elem.textContent || '')) {
    return null;
  }
  
  return frame;
}