import { Font } from './html';
import { NodeData } from './node';

export interface Document {
  name: string;
  doc: {
    innerHeight: number;
    innerWidth: number;
    contentHeight: number;
    fontSize: number;
    baseURI: string;
  };
  frame: NodeData;
  assets: Record<string, Asset | null>;
  fonts: Font[];
  screenShot?: string;
}

export interface Asset {
  base64Encoded: boolean;
  content: string;
  mimeType: string;
}
