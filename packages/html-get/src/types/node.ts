export interface NodeData extends Partial<PositionInfo> {
  type?: string;
  tag?: string;
  attr?: Record<string, any>;
  classList?: string[];
  styles?: Record<string, string>;
  children?: NodeData[] | null;
  value?: string;
  font?: string;
  isContent?: boolean;
}

export interface ImageNode extends NodeData {
  type: 'IMG';
  $url?: string;
  svg?: string;
}

export interface VideoNode extends NodeData {
  type: 'VIDEO';
  $url?: string;
}

export interface SvgNode extends NodeData {
  type: 'SVG';
  svg?: string;
}

/**
 * 元素位置信息
 * 描述元素在页面中的位置和尺寸信息
 */
export interface PositionInfo {
  x: number; // 元素左上角的X坐标(相对于视口)
  y: number; // 元素左上角的Y坐标(相对于视口)
  width: number; // 元素的宽度(像素)
  height: number; // 元素的高度(像素)
  quad?: number[]; // 可选的四角坐标数组，表示元素的四个角点坐标
  // 格式为[x1,y1,x2,y2,x3,y3,x4,y4]
}

export interface ImageCollectionResult {
  imgTags: string[];
  inlineSvgs: string[];
  cssBackgrounds: string[];
  cssGradients: number;
  mp4Videos: string[];
}

export interface FetchedAsset {
  base64Encoded: boolean;
  content: string;
  mimeType: string;
  size?: number; // 资源大小（字节）
  error?: string; // 错误信息
  fetchTime?: number; // 获取耗时（毫秒）
}
