import { createDocumentData } from './core';
import { parseNodeData } from './elements';
import type { Document } from './types';

/**
 * 获取HTML元素的解析数据
 * @param elem HTML元素
 * @returns 文档数据
 */
export const getHtml = async (elem: HTMLElement): Promise<Document> => {
  return createDocumentData(elem, parseNodeData);
};

// 导出图片收集相关函数
export {
  getAssets,
  fetchAsset,
  fetchAssetWithRetry,
} from './core/collect-images';

export type {
  // 公共类型
  NodeData,
  Document,
  ImageCollectionResult,
  FetchedAsset,
} from './types';
