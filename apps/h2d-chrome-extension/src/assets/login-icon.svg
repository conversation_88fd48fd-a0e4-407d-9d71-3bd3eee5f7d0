<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="none" viewBox="0 0 60 60">
  <rect width="60" height="60" fill="url(#a)" rx="10"/>
  <g clip-path="url(#b)">
    <path fill="#000" d="M16.886 24.698a14.332 14.332 0 0 0-.93 5.037c0 7.847 6.29 14.213 14.044 14.213 7.754 0 14.044-6.366 14.044-14.213 0-1.773-.331-3.47-.93-5.037h8.299c.373 1.622.587 3.297.587 5.037C52 42.035 42.154 52 30 52 17.847 52 8 42.035 8 29.735c0-1.73.214-3.415.588-5.037h8.298ZM42 9.099l-4.616 6.963 4.526 6.637h-8.23l-4.48-6.57 4.66-7.03H42Zm-24 3.566v10.034h8v-13.6a20.331 20.331 0 0 0-8 3.566Z"/>
  </g>
  <defs>
    <linearGradient id="a" x1="0" x2="49" y1="41" y2="25" gradientUnits="userSpaceOnUse">
      <stop offset=".031" stop-color="#52FF99"/>
      <stop offset=".375" stop-color="#4DF3A0"/>
      <stop offset=".625" stop-color="#3ED4B1"/>
      <stop offset=".98" stop-color="#0081F0"/>
    </linearGradient>
    <clipPath id="b">
      <rect width="44" height="44" fill="#fff" rx="10" transform="matrix(-1 0 0 1 52 8)"/>
    </clipPath>
  </defs>
</svg>
