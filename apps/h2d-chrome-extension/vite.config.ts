import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path, { resolve } from 'path';
import fs from 'fs';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { createRequire } from 'module';
const { version: currentVersion } = createRequire(import.meta.url)('./package.json');
console.log('📖 当前Chrome插件版本: v' + currentVersion);

function generateManifest() {
  try {
    const manifestPath = resolve(__dirname, 'public/manifest.json');
    const distPath = resolve(__dirname, 'dist');

    if (!fs.existsSync(distPath)) {
      fs.mkdirSync(distPath, { recursive: true });
    }

    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));
    manifest.version = currentVersion;

    const outputPath = resolve(distPath, 'manifest.json');
    fs.writeFileSync(outputPath, JSON.stringify(manifest, null, 2));
    console.log(`✅ Manifest generated at ${outputPath}`);
  } catch (error) {
    console.error('❌ Failed to generate manifest:', error);
    throw error;
  }
}
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    {
      name: 'copy-html-get',
      closeBundle() {
        generateManifest();
      }
    },
    createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'src/assets')], // svg地址
        symbolId: 'icon-[dir]-[name]',
      }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    copyPublicDir: true,
    commonjsOptions: {
      include: [/node_modules/, /packages\/html-get/]
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        background: resolve(__dirname, 'src/pages/background/index.ts'),
        content: resolve(__dirname, 'src/pages/content/index.ts'),
      },
      output: {
        entryFileNames: (chunk) => {
          return ['background', 'content'].includes(chunk.name)
            ? '[name].js'
            : 'assets/[name]-[hash].js';
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  },
  define: {
    __APP_VERSION__: JSON.stringify(currentVersion),
  },
});
