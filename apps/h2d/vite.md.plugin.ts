import { Mode, plugin as Mark<PERSON> } from 'vite-plugin-markdown';
import markdownit from 'markdown-it';
import markdownitLine from 'markdown-it-for-inline';

const mdOption = new markdownit({
  breaks: true,
  linkify: true,
});
mdOption.use(markdownitLine, 'url_new_win', 'link_open', function (tokens: any, idx: number) {
  tokens[idx].attrSet('target', '_blank');
});

export const mdPlugin = Markdown({
  mode: [Mode.HTML, Mode.TOC, Mode.REACT, Mode.MARKDOWN],
  markdownIt: mdOption,
});
