import { UploadFile } from 'tdesign-react';

/**
 * 聊天类型
 */
export type ChatType = 'Common' | 'Request' | 'Response' | 'Stop';

/**
 * 消息项
 */
export type MessageItem = {
  id: string;
  type: ChatType;
  content: string | MessageImageContent[];
  origin: string;
  status?: ChatStatus;
  files?: UploadFile[];
  model: AnalysisModelType;
  requestId?: string;
};

/**
 * 图片消息内容
 */
export type MessageImageContent = {
  type: 'text' | 'image';
  text?: string;
  image?: string;
};

/**
 * 图片地址
 */
export type ImageUrl = {
  detail: string;
  url: string;
};

/**
 * 消息状态
 */
export type ChatStatus = 'DEFAULT' | 'RUNNING' | 'STOPPED' | 'END' | 'FAILED';

/**
 * 分析模型类型
 */
export type AnalysisModelType =
  | 'NONE'
  | 'SWOT'
  | 'FiveForce'
  | 'AARRR'
  | 'UserPersona'
  | 'LeanCanvas'
  | 'CompetitiveAnalysis'
  | 'VisualMoodBoard'
  | 'BluePrint'
  | 'UserJourney'
  | 'KANO';

/**
 * 分析模型选项
 */
export type AnalysisModelOption = {
  type: AnalysisModelType;
  name: string;
  englishName: string;
  content: string;
  englishContent: string;
};

/**
 * Cos 上传密钥
 */
export type CosToken = {
  secret_id: string;
  secret_key: string;
  bucket: string;
  region: string;
  prefix: string;
  token: string;
  expired_at: number;
  enabled_at: number;
  expiration: string;
  domain: string;
};

/**
 * Cos 文件
 */
export type CosFile = {
  ETag: string;
  Location: string;
  ImageInfo?: any;
};

/**
 * 会话项
 */
export type ChatItem = {
  id: string;
  name: string;
  time: number;
  items: MessageItem[];
};

/**
 * 上传进度
 */
export type UploadProcess = {
  [key: string]: number;
};
