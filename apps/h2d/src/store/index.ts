import Typed from 'typed.js';
import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { DEFAULT_MSG_COUNT } from '@ui/utils/config';
import { MessageItem, AnalysisModelType, AnalysisModelOption, CosToken, ChatItem, ChatStatus } from './types';
import { DEFAULT_MODEL } from '../utils/constants';
import { Nullable } from '@ai-assitant/ai-core';
import { FamilyMap } from '@tencent/h2d-html-parser';
import { StageManager } from '@ui/components/stage/stageManager';
import { DEFAULT_AI_MODEL } from '@ui/components/model-select';

/**
 * 分析输入状态
 */
interface InputState {
  msgCount: number;
  decreaseMsgCount: () => void;
  engine: string;
  setEngine: (engine: string) => void;
  /**
   * 输入文本
   */
  content: string;
  /**
   * 设置输入文本
   * @param content
   * @returns
   */
  setContent: (content: string) => void;
  /**
   * 分析模型
   */
  modelType: AnalysisModelOption;
  /**
   * 设置分析模型
   * @param modelType
   * @returns
   */
  setModelType: (modelType: AnalysisModelOption) => void;
  /**
   * 显示模型选择面板
   */
  showModel: boolean;
  /**
   * 设置显示模型选择面板
   * @param show
   * @returns
   */
  setShowModel: (show: boolean) => void;
  uploadProcess: { [key: string]: number };
  removeUploadProcess: (fileName: string) => void;
  setUploadProgress: (fileName: string, progress: number) => void;
  clearUploadProcess: () => void;
}

const useInputStore = create<InputState>()((set) => ({
  msgCount: DEFAULT_MSG_COUNT,
  decreaseMsgCount: () => set((state) => ({ msgCount: state.msgCount - 1 })),
  engine: DEFAULT_AI_MODEL,
  setEngine: (engine) => set({ engine }),
  content: '',
  setContent: (content) => set({ content }),
  modelType: DEFAULT_MODEL,
  setModelType: (modelType) => set({ modelType }),
  showModel: false,
  setShowModel: (show) => set({ showModel: show }),
  uploadProcess: {},
  setUploadProgress: (fileName, progress) => {
    set((state) => {
      state.uploadProcess[fileName] = progress;
      return state;
    });
  },
  removeUploadProcess: (fileName: string) => {
    set((state) => {
      delete state.uploadProcess[fileName];
      return state;
    });
  },
  clearUploadProcess: () => {
    set((state) => {
      state.uploadProcess = {};
      return state;
    });
  },
}));

/**
 * 消息状态
 */
interface MessageState {
  /**
   * 会话编号
   */
  chatId: string;
  /**
   * 设置当前会话编号
   * @param chatId
   * @returns
   */
  setChatId: (chatId: string) => void;
  /**
   * 消息集合
   */
  items: MessageItem[];
  /**
   * 设置消息集合
   * @param items
   * @returns
   */
  setItems: (items: MessageItem[]) => void;
  /**
   * 新增消息项
   * @param item
   * @returns
   */
  addItem: (item: MessageItem) => void;
  /**
   * 清空消息项
   * @param itemId
   * @returns
   */
  clearItem: (itemId: string) => void;
  /**
   * 设置消息模型
   * @param itemId
   * @param model
   * @returns
   */
  setItemModel: (itemId: string, model: AnalysisModelType) => void;
  /**
   * 设置消息内容
   * @param itemId
   * @param content
   * @param requestId
   * @returns
   */
  setItemContent: (itemId: string, content: string, requestId: string) => void;
  /**
   * 设置原始消息内容
   * @param itemId
   * @param content
   * @param requestId
   * @returns
   */
  setItemOrigin: (itemId: string, content: string, requestId: string) => void;
  /**
   * 设置消息状态
   * @param itemId
   * @param status
   * @returns
   */
  setItemStatus: (itemId: string, status: ChatStatus) => void;
  /**
   * 设置消息请求编号
   * @param itemId
   * @param requestId
   * @returns
   */
  setItemRequestId: (itemId: string, requestId: string) => void;
  /**
   * 当前消息编号
   */
  messageId: string;
  /**
   * 设置当前消息编号
   * @param itemId
   * @returns
   */
  setMessageId: (itemId: string) => void;
  /**
   * 清除所有消息
   * @returns
   */
  clearAll: () => void;
  /**
   * 是否显示打字效果
   * 说明：从历史会话中读取消息列表时，不显示打字效果
   */
  showTypedObj: boolean;
  /**
   * 设置是否显示打字效果
   * @param show
   * @returns
   */
  setShowTypedObj: (show: boolean) => void;
  /**
   * 正在运行的打字效果对象
   */
  runTypedObj: Typed | null;
  /**
   * 设置正在运行的打字效果对象
   * @param typedObj
   * @returns
   */
  setRunTypedObj: (typedObj: Typed) => void;
  clearRunTypedObj: () => void;
  /**
   * 建议问题列表
   */
  questions: string[];
  /**
   * 设置建议问题列表
   * @param questions
   * @returns
   */
  setQuestions: (questions: string[]) => void;
  /**
   * 分析请求状态集合
   */
  requestMap: Map<string, 'NORMAL' | 'END'>;
  /**
   * 设置分析请求状态集合
   * @param requestId
   * @param value
   * @returns
   */
  setRequestMap: (requestId: string, value: 'NORMAL' | 'END') => void;
}

const useMessageStore = create<MessageState>()((set) => ({
  chatId: '',
  setChatId: (chatId) => set({ chatId }),
  items: [],
  setItems: (items) => set({ items }),
  addItem: (item) =>
    set((state) => {
      if (state.chatId === '') {
        state.chatId = uuidv4();
      }
      return { items: [...state.items, item] };
    }),
  messageId: 'suggestion',
  setMessageId: (itemId) => set({ messageId: itemId }),
  clearItem: (itemId: string) =>
    set((state) => ({
      items: state.items.map((it) => (it.id === itemId ? { ...it, origin: '', content: '' } : it)),
    })),
  setItemContent: (itemId, content, requestId) =>
    set((state) => {
      if (state.requestMap.get(requestId) === 'END') {
        return {
          items: state.items,
        };
      }

      if (state.messageId !== itemId) {
        return {
          items: state.items,
        };
      }

      const item = state.items.find((it) => it.id === itemId);

      if (item && item.status === 'STOPPED') {
        const newMap = new Map(state.requestMap);
        newMap.set(requestId, 'END');
        return {
          requestMap: newMap,
          items: state.items,
        };
      }

      return {
        items: state.items.map((it) =>
          it.id === itemId
            ? {
                ...it,
                content: it.content + content,
              }
            : it,
        ),
      };
    }),
  setItemModel: (itemId, model) =>
    set((state) => ({
      items: state.items.map((it) => (it.id === itemId ? { ...it, model } : it)),
    })),
  setItemOrigin: (itemId, content, requestId) =>
    set((state) => {
      if (state.requestMap.get(requestId) === 'END') {
        return {
          items: state.items,
        };
      }

      if (state.messageId !== itemId) {
        return {
          items: state.items,
        };
      }

      const item = state.items.find((it) => it.id === itemId);

      if (item && item.status === 'STOPPED') {
        const newMap = new Map(state.requestMap);
        newMap.set(requestId, 'END');
        return {
          requestMap: newMap,
          items: state.items,
        };
      }

      return {
        items: state.items.map((it) => (it.id === itemId ? { ...it, origin: it.origin + content } : it)),
      };
    }),
  setItemStatus: (itemId, status) =>
    set((state) => ({
      items: state.items.map((it) => (it.id === itemId ? { ...it, status } : it)),
    })),
  setItemRequestId: (itemId, requestId) =>
    set((state) => ({
      items: state.items.map((it) => (it.id === itemId ? { ...it, requestId } : it)),
    })),
  clearAll: () => set({ items: [] }),
  showTypedObj: false,
  setShowTypedObj: (showTypedObj) => set({ showTypedObj }),
  runTypedObj: null,
  setRunTypedObj: (runTypedObj) => set({ runTypedObj }),
  clearRunTypedObj: () => set({ runTypedObj: null }),
  questions: [],
  setQuestions: (questions) => set({ questions }),
  requestMap: new Map<string, 'NORMAL' | 'END'>(),
  setRequestMap: (requestId, value) => {
    set((state) => {
      const newMap = new Map(state.requestMap);
      newMap.set(requestId, value);
      return { requestMap: newMap };
    });
  },
}));

/**
 * 用户状态
 */
interface UserState {
  /**
   * 用户信息
   */
  userInfo: any;
  /**
   * 设置用户信息
   * @param userInfo
   * @returns
   */
  setUserInfo: (userInfo: any) => void;
  /**
   * 鉴权token
   */
  userToken: string;
  /**
   * 设置鉴权token
   * @param token
   * @returns
   */
  setUserToken: (token: string) => void;
  /**
   * 显示隐私政策页面
   */
  showPolicy: boolean;
  /**
   * 设置显示隐私政策页面
   * @param showPolicy
   * @returns
   */
  setShowPolicy: (showPolicy: boolean) => void;
  /**
   * 显示帮助页面
   */
  showHelp: boolean;
  /**
   * 设置显示帮助页面
   * @param showHelp
   * @returns
   */
  setShowHelp: (showHelp: boolean) => void;
  /**
   * 显示用户中心页面
   */
  showCenter: boolean;
  /**
   * 设置显示用户中心页面
   * @param showCenter
   * @returns
   */
  setShowCenter: (showCenter: boolean) => void;
}

const useUserStore = create<UserState>()((set) => ({
  userInfo: null,
  setUserInfo: (userInfo) => set({ userInfo }),
  userToken: '',
  setUserToken: (userToken) => set({ userToken }),
  showPolicy: false,
  setShowPolicy: (showPolicy) => set({ showPolicy }),
  showHelp: false,
  setShowHelp: (showHelp) => set({ showHelp }),
  showCenter: false,
  setShowCenter: (showCenter) => set({ showCenter }),
}));

/**
 * 会话状态
 */
interface ChatState {
  /**
   * 会话集合
   */
  items: ChatItem[];
  /**
   * 设置会话集合
   * @param items
   * @returns
   */
  setChatItems: (items: ChatItem[]) => void;
  /**
   * 新增会话
   * @param item
   * @returns
   */
  addChatItem: (item: ChatItem) => void;
  /**
   * 更新会话
   * @param itemId
   * @param messages
   * @returns
   */
  updateChatItem: (itemId: string, messages: MessageItem[]) => void;
  /**
   * 移除会话
   * @param itemId
   * @returns
   */
  removeChatItem: (itemId: string) => void;
  /**
   * 显示历史会话面板
   */
  showHistory: boolean;
  /**
   * 设置显示历史会话面板
   * @param showHistory
   * @returns
   */
  setShowHistory: (showHistory: boolean) => void;
}

const useChatStore = create<ChatState>()((set) => ({
  items: [],
  setChatItems: (items) => set({ items }),
  addChatItem: (item) => set((state) => ({ items: [...state.items, item] })),
  updateChatItem: (itemId, messages) =>
    set((state) => ({
      items: state.items.map((item) => (item.id === itemId ? { ...item, items: messages } : item)),
    })),
  removeChatItem: (itemId) => set((state) => ({ items: state.items.filter((item) => item.id !== itemId) })),
  showHistory: false,
  setShowHistory: (showHistory) => set({ showHistory }),
}));

/**
 * 字体资源状态
 */
interface FamilyState {
  /**
   * 可用字体
   */
  familyMap: Nullable<FamilyMap>;
  /**
   * 设置可用字体
   * @param familyMap
   * @returns
   */
  setFamilyMap: (familyMap: Nullable<FamilyMap>) => void;
}

const useFamilyStore = create<FamilyState>()((set) => ({
  familyMap: null,
  setFamilyMap: (familyMap) => set({ familyMap }),
}));

/**
 * 进度状态
 */
interface StageState {
  /**
   * 显示进度面板
   */
  showStage: boolean;
  /**
   * 设置显示进度面板
   * @param showStage
   * @returns
   */
  setShowStage: (showStage: boolean) => void;
  /**
   * 进度管理器
   */
  stageManager: Nullable<StageManager>;
  /**
   * 设置进度管理器
   * @param stageManager
   * @returns
   */
  setStageManager: (stageManager: StageManager) => void;
  /**
   * 清空进度管理器
   * @returns
   */
  clear: () => void;
}

const useStageStore = create<StageState>()((set, get) => ({
  showStage: false,
  setShowStage: (showStage: boolean) => set({ showStage }),
  stageManager: null,
  setStageManager: (stageManager) => set({ stageManager }),
  clear: () => {
    const stageManager = get().stageManager;
    if (stageManager) stageManager.stop();
    set({ stageManager: null, showStage: false });
  },
}));

export { useInputStore, useMessageStore, useUserStore, useChatStore, useFamilyStore, useStageStore };
