import { type RouteObject } from 'react-router-dom';
import MainLayout from '../layouts/main';

import IndexURL from '../pages/html-to-design/index-url';
import IndexHTML from '../pages/html-to-design/index-html';
import IndexText from '@ui/pages/html-to-design/index-text';
import IndexPlugin from '@ui/pages/html-to-design/index-plugin';

const routes: RouteObject[] = [
  {
    path: '/',
    element: <MainLayout />,
    children: [
      {
        path: 'url',
        element: <IndexURL />,
      },
      {
        path: 'plugin',
        element: <IndexPlugin />,
      },
      {
        path: 'html',
        element: <IndexHTML />,
      },
      {
        path: 'text',
        element: <IndexText />,
      },
    ],
  },
];

export default routes;
