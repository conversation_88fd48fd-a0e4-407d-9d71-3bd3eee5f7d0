@import './vars.less';

body {
  margin: 0;

  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans',
    'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 13px;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.loading {
  display: flex;
  align-content: center;
  justify-content: center;

  width: 100%;
  height: 100%;

  background-color: rgba(31, 32, 37, 1);
}
html,
body,
#app {
  height: 100%;
}

.t-layout__content {
  width: 100%;
  height: 100%;
}

#app,
.t-layout__content {
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  &::-webkit-scrollbar-track {
    background: @bg-color;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #141518;
  }

  &::-webkit-scrollbar-corner {
    background: @bg-color;
  }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
