.t-checkbox.t-is-checked .t-checkbox__input {
  transition: none;

  border-color: #00f697;
  background-color: #00f697;
}
.t-checkbox.t-is-checked .t-checkbox__input::after {
  border-color: #131318;
}
.t-checkbox.t-is-disabled .t-checkbox__input {
  border-color: transparent;
  background-color: #324d3e;
}
.t-checkbox.t-is-disabled:hover .t-checkbox__input {
  border-color: transparent;
}
.t-checkbox__input {
  transition: none;

  border-color: #61f29f;
  background-color: transparent;
}
.t-checkbox:hover .t-checkbox__input {
  transition: none;

  border-color: #61f29f;
}

.assitant-link-input .t-input {
  border: 1px solid #61f29f;
  background-color: #00d56e1a;
}
.t-input__wrap.assitant-link-input {
  border-radius: 4px;
  background: #2e3340;
}
.t-input__wrap.assitant-link-input .t-input--focused.t-input,
.t-input__wrap.assitant-link-input .t-input:focus {
  border-color: #00f697;
  background: rgba(0, 246, 151, 0.1);
  box-shadow: none;
}
.t-input__wrap.assitant-link-input .t-input__tips {
  color: #d54941;
}
.t-input__wrap.assitant-link-input .t-is-error {
  border-color: #d54941;
}
.t-input__wrap.assitant-link-input .t-input--focused.t-is-error {
  border-color: #d54941 !important;
}
.t-input__wrap.assitant-link-input .t-input__inner {
  color: #00f697 !important;

  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
}
.t-input__wrap.ass .t-input__inner::placeholder {
  color: #00f697 !important;

  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
}
