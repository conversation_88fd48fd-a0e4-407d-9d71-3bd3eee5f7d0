/**
 * 用户行为埋点
 *
 * 文档：https://iwiki.woa.com/p/564086851
 */

import { v4 as uuidv4 } from 'uuid';
import i18n from 'i18next';
import { VERSION } from '@ui/utils/config';

/**
 * 埋点值
 */
export type TrackValue = string | { [key: string]: string };

/**
 * 请求队列
 */
export class RequestQueue {
  queue: Array<string>;

  constructor() {
    this.queue = [];
  }

  async addRequest(url: string) {
    this.queue.push(url);
    await this.processQueue();
  }

  async processQueue() {
    if (this.queue.length === 0) {
      return;
    }

    const url = this.queue.shift() as string;

    try {
      await fetch(url, {
        method: 'GET',
      });
    } catch (error) {
      console.error('Error:', error);
    }
    await this.processQueue();
  }
}

const track = {
  script_id: 'horizon-analytics',
  // 版本号
  version: import.meta.env.VITE_APP_VERSION,
  // 平台
  platform: import.meta.env.VITE_APP_PLATFORM,
  // 环境
  env: import.meta.env.VITE_TRACK_ENV,
  // 所在域
  domain: import.meta.env.VITE_TRACK_DOMAIN,
  // 客户端 ID
  uid: '',
  // 上报路径
  report_uri: 'https://ap-guangzhou.cls.tencentcs.com/track?topic_id=ec98919a-f46a-40a6-b004-6d90fae54cce',
  //
  event: '',
  // 事件分类
  eventCategory: '',
  // 事件行为
  eventAction: null,
  // 补充值（字符串）
  eventLabel: '',
  // 补充值（数值）
  eventValue: '',
  // 用户编号
  userId: '',
  // 用户名称
  userName: '',
  // 用户角色
  userRole: '',
  // 请求队列
  requestQueue: new RequestQueue(),
  /**
   * 初始化用户信息
   * @param userId
   * @param userName
   */
  initTrackUser(userId: string, userName: string, userRole: string) {
    this.userId = userId;
    this.userName = userName;
    this.userRole = userRole;
    this.uid = uuidv4();
  },
  // 发送埋点信息
  send(name: string, value?: TrackValue) {
    this.event = 'event';
    this.eventAction = (name || null) as null;
    this.eventLabel = '';
    this.eventValue = '';
    this.eventCategory = '';

    let stringVal = '';
    if (typeof value === 'string') {
      stringVal = value;
      this.eventValue = stringVal;
    } else if (value) {
      for (const key in value) {
        switch (key) {
          case 'eventValue':
            stringVal = this.convertValueToString(value[key]);
            this.eventValue = stringVal;
            break;
          case 'eventLabel':
            this.eventLabel = this.convertValueToString(value[key]);
            break;
          case 'eventCategory':
            this.eventCategory = this.convertValueToString(value[key]);
            break;
          default:
            break;
        }
      }
    }

    const reportUrl = this.getFullReportUrl();
    if (reportUrl) {
      this.doSend(reportUrl);
    }
  },
  /**
   * 发送请求
   * @param url
   */
  doSend(url: string) {
    // 为了复用请求连接，保证同一时间只发出一个请求
    this.requestQueue.addRequest(url);
  },
  /**
   * 获取埋点完整上报地址
   */
  getFullReportUrl() {
    let url = this.report_uri;

    url += `&v=${VERSION}`;
    url += `&a=${this.env}`;
    url += `&uid=${this.uid}`; // 客户端的 uid，即插件只要未重启，则一直使用这个 uid
    url += `&ts=${this.timestamp()}`; // 上报时的毫秒数
    url += `&d=${this.domain}`; // 来源类型判断，即是 web 端还是各类插件端
    url += `&user_id=${this.userId}`; // 用户id
    url += `&et=${this.userRole}`; // 用户角色
    url += `&referer=${this.platform}`; // 操作系统
    url += `&l=${i18n.language === 'zh' ? 'zh' : 'en'}`; // 国际化语言版本 en | zh
    url += `&user_agent=${this.version}`; // 版本号

    if (this.eventCategory) {
      url += `&ec=${this.eventCategory}`;
    }
    if (this.eventAction) {
      url += `&ea=designgenie.${this.eventAction}`;
    }
    if (this.eventLabel) {
      url += `&el=${this.eventLabel}`;
    }
    if (this.eventValue) {
      url += `&ev=${this.eventValue}`;
    }
    return url;
  },
  /**
   * 将埋点值转换为字符串
   * @param value
   * @returns
   */
  convertValueToString(value: TrackValue | undefined) {
    if (!value) {
      return '';
    }
    if (typeof value === 'string') {
      return value;
    }
    return encodeURI(JSON.stringify(value));
  },
  /**
   * 时间戳
   */
  timestamp() {
    return new Date().getTime();
  },
};

export default track;
