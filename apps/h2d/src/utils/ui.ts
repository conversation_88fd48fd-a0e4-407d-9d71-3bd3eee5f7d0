import { messages } from '@worker/index';
import { MarkdownItem, MessageTypes } from '@ai-assitant/ai-core';
import { base64_high, base64_low, base64_middle, base64_user } from './base64';

/**
 * 插入 Swot 模型到画布
 * @param content
 */
export async function SwotToCanvas(name: string, content: string) {
  await messages[MessageTypes.CREATE_SWOT].request({
    name,
    width: 3200,
    height: 2000,
    x: 50,
    y: 50,
  });

  const swContent = extractByTags(content, '\\[START-SW\\]', '\\[END-SW\\]');
  const swItems = parseMarkdown(swContent);

  const otContent = extractByTags(content, '\\[START-OT\\]', '\\[END-OT\\]');
  const otItems = parseMarkdown(otContent);

  const soContent = extractByTags(content, '\\[START-SO\\]', '\\[END-SO\\]');
  const soItems = parseMarkdown(soContent);

  const stContent = extractByTags(content, '\\[START-ST\\]', '\\[END-ST\\]');
  const stItems = parseMarkdown(stContent);

  const woContent = extractByTags(content, '\\[START-WO\\]', '\\[END-WO\\]');
  const woItems = parseMarkdown(woContent);

  const wtContent = extractByTags(content, '\\[START-WT\\]', '\\[END-WT\\]');
  const wtItems = parseMarkdown(wtContent);

  messages[MessageTypes.SET_SWOT_CONTENT].send({
    name,
    swItems,
    otItems,
    soItems,
    stItems,
    woItems,
    wtItems,
  });
}

/**
 * 插入波特五力模型到画布
 * @param content
 */
export async function FiveForceToCanvas(name: string, content: string) {
  await messages[MessageTypes.CREATE_FIVE_FORCE].request({
    name,
    width: 4300,
    height: 2725,
    x: 50,
    y: 50,
  });

  const riContent = extractByTags(content, '\\[START-RIVALRY\\]', '\\[END-RIVALRY\\]');
  const riItems = parseMarkdown(riContent);

  const enContent = extractByTags(content, '\\[START-ENTRANTS\\]', '\\[END-ENTRANTS\\]');
  const enItems = parseMarkdown(enContent);

  const suContent = extractByTags(content, '\\[START-SUBSTITUTES\\]', '\\[END-SUBSTITUTES\\]');
  const suItems = parseMarkdown(suContent);

  const buContent = extractByTags(content, '\\[START-BUYERS\\]', '\\[END-BUYERS\\]');
  const buItems = parseMarkdown(buContent);

  const supContent = extractByTags(content, '\\[START-SUPPLIERS\\]', '\\[END-SUPPLIERS\\]');
  const supItems = parseMarkdown(supContent);

  messages[MessageTypes.SET_FIVE_FORCE_CONTENT].send({
    name,
    riItems,
    enItems,
    suItems,
    buItems,
    supItems,
  });
}

/**
 * 插入用户画像模型到画布
 * @param content
 */
export async function UserPersonaToCanvas(name: string, content: string) {
  const imageRes = base64ToUint8Array(base64_user);

  await messages[MessageTypes.CREATE_USER_PERSONA].request({
    name,
    width: 2400,
    height: 2400,
    x: 50,
    y: 50,
    data: imageRes,
  });

  const tagContent = extractByTags(content, '\\[START-USER-TAG\\]', '\\[END-USER-TAG\\]');
  const tagItems = parseMarkdown(tagContent);

  const proContent = extractByTags(content, '\\[START-USER-PRODUCT\\]', '\\[END-USER-PRODUCT\\]');
  const productItems = parseMarkdown(proContent);

  messages[MessageTypes.SET_USER_PERSONA_CONTENT].send({
    name,
    tagItems,
    productItems,
  });
}

/**
 * 插入AARRR模型到画布
 * @param content
 */
export async function AarrrToCanvas(name: string, content: string) {
  await messages[MessageTypes.CREATE_AARRR].request({
    name,
    width: 2700,
    height: 1700,
    x: 50,
    y: 50,
  });

  const a1Content = extractByTags(content, '\\[START-ACQUISITION\\]', '\\[END-ACQUISITION\\]');
  const a1Items = parseMarkdown(a1Content);

  const a2Content = extractByTags(content, '\\[START-ACTIVATION\\]', '\\[END-ACTIVATION\\]');
  const a2Items = parseMarkdown(a2Content);

  const r1Content = extractByTags(content, '\\[START-RETENTION\\]', '\\[END-RETENTION\\]');
  const r1Items = parseMarkdown(r1Content);

  const r2Content = extractByTags(content, '\\[START-REVENUE\\]', '\\[END-REVENUE\\]');
  const r2Items = parseMarkdown(r2Content);

  const r3Content = extractByTags(content, '\\[START-REFERRAL\\]', '\\[END-REFERRAL\\]');
  const r3Items = parseMarkdown(r3Content);

  messages[MessageTypes.SET_AARRR_CONTENT].send({
    name,
    a1Items,
    a2Items,
    r1Items,
    r2Items,
    r3Items,
  });
}

/**
 * 插入精益画布模型到画布
 * @param content
 */
export async function LeanCanvasToCanvas(name: string, content: string) {
  await messages[MessageTypes.CREATE_LEAN_CANVAS].request({
    name,
    width: 3702,
    height: 2200,
    x: 50,
    y: 50,
  });

  const cuContent = extractByTags(content, '\\[START-CUSTOMER\\]', '\\[END-CUSTOMER\\]');
  const cuItems = parseMarkdown(cuContent);

  const buContent = extractByTags(content, '\\[START-BUSINESS\\]', '\\[END-BUSINESS\\]');
  const buItems = parseMarkdown(buContent);

  messages[MessageTypes.SET_LEAN_CANVAS_CONTENT].send({
    name,
    cuItems,
    buItems,
  });
}

/**
 * 插入竞品分析模型到画布
 * @param content
 */
export async function CAToCanvas(name: string, content: string) {
  await messages[MessageTypes.CREATE_COMPETITIVE_ANALYSIS].request({
    name,
    width: 2030,
    height: 1500,
    x: 50,
    y: 50,
  });

  const productContent = extractByTags(content, '\\[START-PRODUCT\\]', '\\[END-PRODUCT\\]');
  const productItems = parseMarkdown(productContent);

  const alyContent = extractByTags(content, '\\[START-ANALYSIS\\]', '\\[END-ANALYSIS\\]');
  const alyItems = parseMarkdown(alyContent);

  messages[MessageTypes.SET_COMPETITIVE_ANALYSIS_CONTENT].send({
    name,
    productItems,
    alyItems,
  });
}

/**
 * 插入视觉情绪模型到画布
 * @param name
 * @param content
 */
export async function VisualAnalysisToCanvas(name: string, content: string, imageUrls: string[]) {
  messages[MessageTypes.CREATE_VISUAL_ANALYSIS].send({
    name,
    width: 2320,
    height: 1300,
    x: 50,
    y: 50,
    count: imageUrls.length,
  });

  const imageDatas: Uint8Array[] = [];

  for (let i = 0; i < imageUrls.length; i++) {
    const imageUrl = imageUrls[i];
    const imageData = await (await fetch(imageUrl)).arrayBuffer();
    imageDatas.push(new Uint8Array(imageData));
  }

  const vmbContent = extractByTags(content, '\\[START-VMB\\]', '\\[END-VMB\\]');
  const vmbItems = parseMarkdown(vmbContent);

  messages[MessageTypes.SET_VISUAL_ANALYSIS_CONTENT].send({
    name,
    imageDatas: imageDatas,
    type: 'Description',
    items: vmbItems,
  });
}

/**
 * 插入KANO模型到画布
 * @param name
 * @param content
 */
export async function KANOToCanvas(name: string, content: string) {
  await messages[MessageTypes.CREATE_KANO].request({
    name,
    width: 2300,
    height: 2300,
    x: 50,
    y: 50,
  });

  const basicContent = extractByTags(content, '\\[START-BASIC\\]', '\\[END-BASIC\\]');
  const basicItems = parseMarkdown(basicContent);

  const excitementContent = extractByTags(content, '\\[START-EXCITEMENT\\]', '\\[END-EXCITEMENT\\]');
  const excitementItems = parseMarkdown(excitementContent);

  const indifferentContent = extractByTags(content, '\\[START-PERFORMANCE\\]', '\\[END-PERFORMANCE\\]');
  const indifferentItems = parseMarkdown(indifferentContent);

  const performanceContent = extractByTags(content, '\\[START-INDIFFERENT\\]', '\\[END-INDIFFERENT\\]');
  const performanceItems = parseMarkdown(performanceContent);

  messages[MessageTypes.SET_KANO_CONTENT].send({
    name,
    basicItems,
    excitementItems,
    indifferentItems,
    performanceItems,
  });
}

/**
 * 插入蓝图模型到画布
 * @param name
 * @param content
 */
export async function BluePrintToCanvas(name: string, content: string) {
  const actionContent = extractByTags(content, '\\[START-ACTION\\]', '\\[END-ACTION\\]');
  const actionItems = parseMarkdown(actionContent);

  const frameId = await messages[MessageTypes.CREATE_BLUE_PRINT].request({
    name,
    width: 2300,
    height: 2300,
    x: 50,
    y: 50,
    count: actionItems.length,
  });

  messages[MessageTypes.SET_BLUE_PRINT_CONTENT].send({
    name: frameId,
    actionItems,
  });
}

/**
 * 插入用户旅行图模型到画布
 * @param name
 * @param content
 */
export async function JourneyToCanvas(name: string, content: string) {
  const tagContent = extractByTags(content, '\\[START-USER-TAG\\]', '\\[END-USER-TAG\\]');
  const tagItems = parseMarkdown(tagContent);

  const frameId = await messages[MessageTypes.CREATE_JOURNEY].request({
    name,
    width: 2300,
    height: 2300,
    x: 50,
    y: 50,
    count: tagItems.find((it) => it.content === '用户旅程阶段')?.children.length ?? 0,
  });

  const stageContent = extractByTags(content, '\\[START-STAGE\\]', '\\[END-STAGE\\]');
  const stageItems = parseMarkdown(stageContent);

  messages[MessageTypes.SET_JOURNEY_CONTENT].send({
    name: frameId,
    tagItems,
    stageItems,
    user: base64ToUint8Array(base64_user),
    high: base64ToUint8Array(base64_high),
    middle: base64ToUint8Array(base64_middle),
    low: base64ToUint8Array(base64_low),
  });
}

/**
 * 解析 markdown 字符串
 * @param content
 * @returns
 */
function parseMarkdown(content: string) {
  const lines = content.split('\n');
  const result: MarkdownItem[] = [];

  let parent: MarkdownItem | null = null;
  let subParent: MarkdownItem | null = null;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trimStart();
    if (line.startsWith('##')) {
      const item = {
        tag: '##',
        content: line.replace('##', '').trimStart(),
        children: [],
      };

      if (parent) {
        if (parent.tag === '#') {
          parent.children.push(item);
        } else if (parent.tag === '##') {
          result.at(-1)?.children.push(item);
        }
      }

      parent = item;
    } else if (line.startsWith('#')) {
      const item = {
        tag: '#',
        content: line.replace('#', '').trimStart(),
        children: [],
      };

      result.push(item);
      parent = item;
    } else if (line.startsWith('*')) {
      const item = {
        tag: '*',
        content: line.replace('*', '').trimStart(),
        children: [],
      };
      if (parent) {
        parent.children.push(item);
      }
      subParent = item;
    } else if (line.startsWith('-')) {
      const item = {
        tag: '-',
        content: line.replace('-', '').trimStart(),
        children: [],
      };
      if (subParent) {
        subParent.children.push(item);
      }
    }
  }

  return result;
}

/**
 * 将Base64字符串解码为二进制数据
 * @param base64String
 * @returns
 */
export function base64ToUint8Array(base64String: string) {
  // 移除数据URL方案（如果有）
  const base64WithoutPrefix = base64String.replace(/^data:image\/[a-z]+;base64,/, '');

  // 将Base64字符串解码为二进制字符串
  const binaryString = atob(base64WithoutPrefix);

  // 将二进制字符串转换为Uint8Array
  const uint8Array = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    uint8Array[i] = binaryString.charCodeAt(i);
  }

  return uint8Array;
}

/**
 * 根据标签提取内容
 * @param msg
 * @param startTag
 * @param endTag
 * @returns
 */
export function extractByTags(msg: string, startTag: string, endTag: string) {
  const regex = new RegExp(`${startTag}(.*?)${endTag}`, 's');
  const match = msg.match(regex);
  if (match) {
    return match[1];
  } else {
    return '';
  }
}
