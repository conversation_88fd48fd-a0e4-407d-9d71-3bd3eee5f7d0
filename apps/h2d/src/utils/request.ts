import CryptoJS from 'crypto-js';
import { ISecretParams } from '@tencent/h2d-html-parser';
import { PlatformUser } from '@ai-assitant/ai-core';

/**
 * 获取自定义 Header
 * @param userToken
 * @returns
 */
export function getCustomHeader(userToken: string) {
  const headers: any = {
    'Content-Type': 'application/json',
    // 标注是 ajax 请求，避免 IOA 网关为认证返回 302
    'X-Requested-With': 'XMLHttpRequest',
    'x-Source': 'figma',
    'authorization': `Bearer ${userToken}`,
    'x-backend-branch': import.meta.env.VITE_APP_BRANCH,
  };

  // 模拟登录态 /ghostlogin
  // const mockAuthorization = Cookies.get('_dcloud_app_token_mock_');

  // const { authorization, currentTeam } = store.state;

  // if (authorization) {
  //     headers['authorization'] = `Bearer ${authorization}`;
  // }

  // if (currentTeam) {
  //     headers['X-Team-Id'] = currentTeam.teamId;
  //     headers['X-Corp-Id'] = currentTeam.companyId;
  // }

  // if ((CONFIG.IS_DEV || CONFIG.ENV === 'test') && CONFIG.BRANCH) {
  //     // 开发和测试环境下，切换测试分支
  //     headers['x-backend-branch'] = CONFIG.BRANCH;
  // }

  return headers;
}

/**
 * 获取请求密钥
 * @param user 平台用户信息
 * @returns
 */
export function getSecretParams(user: PlatformUser): ISecretParams {
  const uid = user.userId;
  const ts = Math.floor(Date.now() / 1000);
  const name = user.userName;
  const membership = 'FREE';
  const source = import.meta.env.VITE_APP_PLATFORM;
  const key = import.meta.env.VITE_APP_KEY;
  const secretKey = import.meta.env.VITE_SECRET_KEY;

  const params = {
    ts,
    name,
    membership,
    source,
    key: Number(key),
    token: calculateHmacSha1(`${uid}${name}${ts}${membership}${source}`, secretKey),
  };
  return params;
}

/**
 * 计算 hash 密钥
 * @param message
 * @param secretKey
 * @returns
 */
export function calculateHmacSha1(message: string, secretKey: string) {
  const hash = CryptoJS.HmacSHA1(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
}
