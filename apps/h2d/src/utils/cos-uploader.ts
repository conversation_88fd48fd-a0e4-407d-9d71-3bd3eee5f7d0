import COS, { ProgressInfo, UploadBody } from 'cos-js-sdk-v5';
import { v4 as uuidv4 } from 'uuid';
import { CosToken } from '@ui/store/types';
import { getCustomHeader } from './request';

let promise: any = null;

export type ServerTime = {
  timestamp: number;
  timestampms: number;
  datetime: string;
  date: string;
  time: string;
  iso8601: string;
  timezone: string;
  timezone_offset: string;
};

export interface UploadOptions {
  // 待上传文件（File对象）
  file: File;
  body?: UploadBody;
  // 文件扩展名
  extension?: string;
  // 是否获取文件元信息
  getPicInfo?: boolean;
  //
  userToken: string;
  // 进度响应
  onProgress?: (percent: number) => void;
  onStart?: (arg: any) => void;
}

export interface UploadResult extends COS.PutObjectResult {
  url?: string;
  imageInfo?: object;
}

export class CosUploader {
  private cosToken: CosToken | null = null;

  public expiration: string = '';

  async initSrvTime(userToken: string) {
    const options = {
      method: 'Get',
      headers: getCustomHeader(userToken),
    };

    const response = await fetch(`${import.meta.env.VITE_APP_URL}/api/time`, options);
    const result = await response.json();
    return result;
  }

  async initToken(userToken: string): Promise<CosToken> {
    const options = {
      method: 'POST',
      headers: getCustomHeader(userToken),
    };

    const response = await fetch(`${import.meta.env.VITE_APP_URL}/api/federation-tokens`, options);
    const result = await response.json();

    return result;
  }

  async getCos(userToken: string) {
    const cosToken = await this.getCosToken(userToken);
    const { secret_id, secret_key, token, enabled_at, expired_at, domain } = cosToken;
    const cos = new COS({
      Domain: domain,
      getAuthorization(options, callback) {
        callback({
          TmpSecretId: secret_id,
          TmpSecretKey: secret_key,
          SecurityToken: token,
          StartTime: enabled_at, // 时间戳，单位秒，如：1580000000
          ExpiredTime: expired_at, // 时间戳，单位秒，如：1580000000
        });
      },
    });
    this.expiration = cosToken.expiration;

    return cos;
  }

  getCosToken(userToken: string): Promise<CosToken> {
    if (promise) {
      return promise;
    }

    promise = new Promise(async (resolve, reject) => {
      setTimeout(async () => {
        // 判断 token 是否过期， 减100秒避免时间误差
        let isTokenInvalid = false;
        if (this.cosToken === null) {
          isTokenInvalid = true;
        } else {
          const srvTime = await this.initSrvTime(userToken);

          if (srvTime.timestampms > (this.cosToken.expired_at - 100) * 1000) {
            // console.log('diffTime', srvTime.timestampms > (this.cosToken.expired_at - 100) * 1000);
            isTokenInvalid = true;
          }
        }
        // 过期了就重新获取 tokens
        if (isTokenInvalid) {
          try {
            this.cosToken = await this.initToken(userToken);
          } catch (error) {
            reject(error);
          }
        }
        promise = null;
        resolve(this.cosToken);
      }, 0);
    });
    return promise;
  }

  /**
   * 上传文件
   * @param opts
   * @returns
   */
  async upload(opts: UploadOptions): Promise<UploadResult> {
    const cos = await this.getCos(opts.userToken); // 每次都获取 cos，检测过期，防止 token 过期导致 cos 失效

    const { prefix, bucket, region } = this.cosToken!;

    let ext = opts.extension;
    return new Promise((resolve, reject) => {
      if (!ext) {
        ext = opts.file.name.split('.').pop();
      }

      const path = this.generatePath(prefix, ext!);
      // 图片类文件获取元信息
      const headers = opts.getPicInfo
        ? {
            'Pic-Operations': '{"is_pic_info": 1}',
          }
        : {};

      const fileSize = opts.file.size;

      try {
        // 超过 5Mb 进行分片上传
        if (fileSize > 5 * 1024 * 1024) {
          const chunkSize = 1 * 1024 * 1024;
          cos.sliceUploadFile(
            {
              Bucket: bucket,
              Region: region,
              Key: path,
              ChunkSize: chunkSize,
              AsyncLimit: 10,
              StorageClass: 'STANDARD',
              Body: opts.file,
              onTaskStart(info) {
                opts.onStart && opts.onStart(info);
              },
              onProgress(params: ProgressInfo) {
                opts.onProgress && opts.onProgress(params.percent);
              },
            },
            (error, data) => {
              if (error) {
                opts.onProgress && opts.onProgress(-1);
                reject(error);
              } else {
                const url = `https://${data.Location}`;
                const imageInfo = opts.getPicInfo ? (data as any).UploadResult?.OriginalInfo?.ImageInfo : null;

                resolve({ ...data, url, imageInfo });
              }
            },
          );
        } else {
          cos.putObject(
            {
              Bucket: bucket,
              Region: region,
              Key: path,
              StorageClass: 'STANDARD',
              Body: opts.body || opts.file,
              Headers: headers,
              onTaskStart(taskInfo: COS.Task) {
                // console.log(taskInfo);
                // opts.onStart && opts.onStart(info);
              },
              onProgress(params: ProgressInfo) {
                opts.onProgress && opts.onProgress(params.percent);
              },
            },
            (error, data) => {
              if (error) {
                // 记录tokens
                (error as any).tokens = this.cosToken;

                opts.onProgress && opts.onProgress(-1);

                reject(error);
              } else {
                const url = `https://${data.Location}`;
                const imageInfo = opts.getPicInfo ? (data as any).UploadResult?.OriginalInfo?.ImageInfo : null;

                resolve({ ...data, url, imageInfo });
              }
            },
          );
        }
      } catch (error) {
        opts.onProgress && opts.onProgress(-1);
      }
    });
  }

  // 生成 cos 存储路径
  generatePath(prefix: string, extension: string) {
    const now = new Date();
    const year = now.getFullYear();
    const month = `0${now.getMonth() + 1}`.slice(-2);
    const day = `0${now.getDate()}`.slice(-2);
    const path = `${'ai-materials'}/${year}/${month}/${day}/${prefix}/${uuidv4()}.${extension}`;
    return path;
  }
}

const cosUploader = new CosUploader();

export default cosUploader;
