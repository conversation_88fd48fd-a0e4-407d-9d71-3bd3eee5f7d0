import { fetchEventSource, EventSourceMessage } from '@microsoft/fetch-event-source';
import track from './track';

/**
 * 聊天消息错误对象
 */
export interface ChatMessageError {
  body?: any;
  message: string;
  type: ErrorType;
}

export interface FetchSSEOptions {
  fetcher?: typeof fetch;
  onOpen?: (res: Response) => void;
  onAbort?: (text: string) => Promise<void>;
  onErrorHandle?: (error: ChatMessageError) => void;
  onFinish?: OnFinishHandler;
  onMessageHandle?: (chunk: MessageTextChunk | MessageFlagChunk) => void;
  smoothing?: boolean;
}

/**
 * 返回文本消息
 */
export interface MessageTextChunk {
  type: 'text';
  text: string;
  origin?: string;
}

/**
 * 标记消息
 */
export interface MessageFlagChunk {
  content: string;
  type: 'flag';
}

type SSEFinishType = 'done' | 'error' | 'abort';

/**
 * 完成请求回调
 */
export type OnFinishHandler = (
  text: string,
  context: {
    observationId?: string | null;
    // toolCalls?: MessageToolCall[];
    traceId?: string | null;
    type?: SSEFinishType;
  },
) => Promise<void>;

/**
 * 聊天错误类型
 */
export const ChatErrorType = {
  // ******* 业务错误语义 ******* //

  InvalidAccessCode: 'InvalidAccessCode', // is in valid password
  InvalidClerkUser: 'InvalidClerkUser', // is not Clerk User
  /**
   * @deprecated
   */
  NoOpenAIAPIKey: 'NoOpenAIAPIKey',
  OllamaServiceUnavailable: 'OllamaServiceUnavailable', // 未启动/检测到 Ollama 服务
  PluginFailToTransformArguments: 'PluginFailToTransformArguments',

  // ******* 客户端错误 ******* //
  BadRequest: 400,
  Unauthorized: 401,
  Forbidden: 403,
  ContentNotFound: 404, // 没找到接口
  MethodNotAllowed: 405, // 不支持
  TooManyRequests: 429,

  // ******* 服务端错误 ******* //InvalidPluginArgumentsTransform
  InternalServerError: 500,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
} as const;

/**
 * 请求错误类型
 */
export type ErrorType = (typeof ChatErrorType)[keyof typeof ChatErrorType];

/**
 * 通过流式传输获取数据
 * @param url
 * @param options
 */
export const fetchSSE = async (url: string, options: RequestInit & FetchSSEOptions = {}) => {
  // 输出信息
  const output = '';
  // 完成类型
  let finishedType: SSEFinishType = 'done';
  let response!: Response;

  // 标签
  let flag = '';

  fetchEventSource(url, {
    method: 'POST',
    // mode: 'no-cors',
    body: options.body,
    headers: options.headers as Record<string, string>,
    // 当页面隐藏时保持连接
    openWhenHidden: true,
    // 终止信号
    signal: options.signal,
    // 请求连接处理
    onopen: async (res: any) => {
      response = res.clone();
      // 如果不 ok 说明有请求错误
      if (!response.ok) {
        throw await getMessageError(res);
      } else {
        track.send('analysis.request.open');
        options.onOpen?.(res);
        // console.log('请求连接成功');
      }
    },
    // 错误处理
    onerror: (error: any) => {
      // 此处抛出错误，否则会不停重试请求
      throw error;
    },
    // 消息处理
    onmessage: (ev: EventSourceMessage) => {
      // console.log(ev);

      // 空数据不处理
      if (ev.data === '') {
        return;
      }

      if (ev.event === 'error') {
        console.log(ev);
        return;
      }

      let data;
      try {
        data = JSON.parse(ev.data);
      } catch (e) {
        console.warn('parse error, fallback to stream', e);
        options.onMessageHandle?.({ text: data, type: 'text' });
        return;
      }

      switch (data.event) {
        case '[SWOT-START]':
          options.onMessageHandle?.({ content: 'SWOT', type: 'flag' });
          break;
        case '[FIVEFORCE-START]':
          options.onMessageHandle?.({ content: 'FiveForce', type: 'flag' });
          break;
        case '[USERPERSONA-START]':
          options.onMessageHandle?.({ content: 'UserPersona', type: 'flag' });
          break;
        case '[AARRR-START]':
          options.onMessageHandle?.({ content: 'AARRR', type: 'flag' });
          break;
        case '[LEANCANVAS-START]':
          options.onMessageHandle?.({ content: 'LeanCanvas', type: 'flag' });
          break;
        case '[CA-START]':
          options.onMessageHandle?.({ content: 'CompetitiveAnalysis', type: 'flag' });
          break;
        case '[KANO-START]':
          options.onMessageHandle?.({ content: 'KANO', type: 'flag' });
          break;
        case '[BLUEPRINT-START]':
          options.onMessageHandle?.({ content: 'BluePrint', type: 'flag' });
          break;
        case '[JOURNEY-START]':
          options.onMessageHandle?.({ content: 'UserJourney', type: 'flag' });
          break;
        case '[PURETEXT-START]':
          options.onMessageHandle?.({ content: 'PURETEXT', type: 'flag' });
          break;
        case '[DATA]':
          const info = data.data.trim();
          if (info === '[' && flag === '') {
            flag = info;
          } else if (info === ']' && flag !== '') {
            flag = flag + info;
            options.onMessageHandle?.({
              origin: flag,
              text: '',
              type: 'text',
            });
            flag = '';
          } else if (flag !== '') {
            flag = flag + info;
          } else if (data.data !== '') {
            options.onMessageHandle?.({
              text: data.data,
              origin: data.data,
              type: 'text',
            });
          }
          break;
        case '[FIN]':
          if (data.data === '[DONE]') {
            finishedType = 'done';
            options.onFinish?.(output, {
              type: finishedType,
            });
          }

          break;
        case '[ERROR]':
          finishedType = 'error';
          options.onErrorHandle?.(data.data);
          break;
      }
    },
    // 关闭连接处理
    onclose() {
      track.send('analysis.request.close');
    },
  }).catch((error) => {
    track.send('analysis.request.error', {
      eventValue: error.message || '未知错误',
      eventCategory: error.status || error.statusCode,
      eventLabel: error.code,
    });

    finishedType = 'error';
    options.onErrorHandle?.(error);
  });
};

export const getMessageError = async (response: Response) => {
  let chatMessageError: ChatMessageError;

  // 尝试取一波业务错误语义
  try {
    const data = (await response.json()) as ErrorResponse;
    chatMessageError = {
      body: data.body,
      message: data.errorType.toString(),
      type: data.errorType,
    };
  } catch {
    // 如果无法正常返回，说明是常规报错
    chatMessageError = {
      message: response.status.toString(),
      type: response.status as ErrorType,
    };
  }

  return chatMessageError;
};

export interface ErrorResponse {
  body: any;
  errorType: ErrorType;
}
