import { locales, Locales } from '@ui/locale';
import dayjs from 'dayjs';

/**
 * 获取随机字符串
 * @param len
 * @returns
 */
export const randomWord = (len: number) => {
  const arr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let hash = '';
  for (let i = 0; i < len; i++) {
    hash += arr.charAt(Math.floor(Math.random() * arr.length));
  }
  return hash;
};

/**
 * 获取文件扩展名
 * @param fileName
 * @param upperCase
 * @returns
 */
export function getExt(fileName = '', upperCase = false): string {
  const lastIndex = fileName.lastIndexOf('.');
  if (lastIndex < 0) {
    return '';
  }
  const fileType = fileName.slice(lastIndex + 1);
  return upperCase ? fileType.toUpperCase() : fileType.toLowerCase();
}

export function findExtraAtOrSlash(str0: string, str1: string) {
  // 确保str1至少和str0一样长
  if (str1.length < str0.length) {
    return null;
  }

  // 遍历str0的每个字符
  for (let i = 0; i < str0.length; i++) {
    // 检查str1中相应位置的字符是否是@或/且str0中不是
    if (str1[i] === '/' && str0[i] !== str1[i]) {
      // 返回找到的字符及其位置
      return { character: str1[i], position: i };
    }
  }

  // 如果str0遍历完都没有找到，则检查str1中超出str0长度的部分
  for (let i = str0.length; i < str1.length; i++) {
    if (str1[i] === '/') {
      return { character: str1[i], position: i };
    }
  }

  // 如果没有找到额外的@或/字符，则返回null
  return null;
}

export function findMissingAtOrSlash(str0: string, str1: string) {
  // 确保str1长度小于str0
  if (str1.length >= str0.length) {
    return null;
  }

  // 遍历str0的每个字符
  for (let i = 0; i < str0.length; i++) {
    // 如果str0中的字符是@或/，但str1中相应位置的字符不是
    if (str0[i] === '/' && str1[i] !== str0[i]) {
      // 返回缺少字符的位置
      return { character: str0[i], position: i };
    }
  }

  // 如果遍历完str0都没有找到缺少的字符，则返回null
  return null;
}

/**
 * 判断是否是有效的https链接
 * @param url
 * @returns
 */
export function isValidHttpsUrl(url: string) {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.protocol === 'https:' || parsedUrl.protocol === 'http:';
  } catch (error) {
    return false;
  }
}

/**
 * 格式化时间
 * @param time
 * @param language
 * @returns
 */
export const formatTime = (time: number, language: string) => {
  const timeStart = dayjs(time);
  const today = dayjs().startOf('day');
  // 相差秒
  const diffInSeconds = dayjs().diff(timeStart, 'second');

  const diffInMinutes = diffInSeconds / 60;
  const diffInHours = diffInMinutes / 60;
  const diffInDays = diffInHours / 24;
  const diffInMonths = diffInDays / 30;
  const diffInYears = diffInMonths / 12;

  if (today.isSame(timeStart.startOf('day'))) {
    return dayjs(time).format('HH:mm');
  } else if (diffInDays < 30) {
    return `${Math.floor(diffInDays < 1 ? 1 : diffInDays)}` + (language === locales[0] ? ' days ago' : '天前');
  } else if (diffInMonths < 12) {
    return `${Math.floor(diffInMonths)}` + (language === locales[0] ? ' months ago' : '个月前');
  } else {
    return `${Math.floor(diffInYears)}` + (language === locales[0] ? ' years ago' : '年前');
  }
};
