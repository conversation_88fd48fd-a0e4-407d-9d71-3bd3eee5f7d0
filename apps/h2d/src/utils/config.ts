/**
 * 配置信息
 */
// 菜单类
export type Menu = {
  key: string;
  rootRoute: string;
  routes: string[];
  name: string;
};

// 菜单集合
export const menus: Menu[] = [
  // {
  //   key: 'generate',
  //   rootRoute: '/main/generate',
  //   routes: ['/main/generate'],
  //   name: '生成',
  // },
  {
    key: 'analysis',
    rootRoute: '/main/analysis',
    routes: ['/main/analysis'],
    name: '分析',
  },
  {
    key: 'inspire',
    rootRoute: '/main/inspire',
    routes: ['/main/inspire', '/main/inspire/image-to-design', '/main/inspire/html-to-design'],
    name: '灵感',
  },
  // {
  //   key: 'edit',
  //   rootRoute: '/main/edit',
  //   routes: ['/main/edit'],
  //   name: '验证',
  // },
];

// 默认消息次数
export const DEFAULT_MSG_COUNT = 32;

export const VERSION = __APP_VERSION__;
