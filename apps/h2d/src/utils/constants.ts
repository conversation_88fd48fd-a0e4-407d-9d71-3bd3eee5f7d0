import { AnalysisModelOption } from '../store/types';

/**
 * 默认模型（空）
 */
export const DEFAULT_MODEL: AnalysisModelOption = {
  type: 'NONE',
  name: '',
  englishName: '',
  content: '',
  englishContent: '',
};

/**
 * 模型可选项
 */
export const modelOpts: AnalysisModelOption[] = [
  {
    name: 'SWOT分析',
    content: '识别和评估项目或业务的优势/劣势/机会和威胁',
    type: 'SWOT',
    englishName: '',
    englishContent: '',
  },
  {
    name: '用户画像',
    content: '通过数据分析帮助您构建精准的用户画像',
    type: 'UserPersona',
    englishName: '',
    englishContent: '',
  },
  {
    name: '精益画布',
    content: '专注于帮助您快速梳理和验证商业模式',
    type: 'LeanCanvas',
    englishName: '',
    englishContent: '',
  },
  {
    name: '波特五力模型',
    content: '帮助您评估行业竞争强度和盈利潜力',
    type: 'FiveForce',
    englishName: '',
    englishContent: '',
  },
  {
    name: 'AARRR模型',
    content: '帮助企业增长和提升用户价值',
    type: 'AARRR',
    englishName: '',
    englishContent: '',
  },
  {
    name: '竞品分析',
    content: '研究竞争对手产品助力决策',
    type: 'CompetitiveAnalysis',
    englishName: '',
    englishContent: '',
  },
  {
    name: 'KANO',
    content: '研究竞争对手产品助力决策',
    type: 'KANO',
    englishName: 'KANO Research',
    englishContent: 'Competitor Product Analysis to Aid Decision-Making',
  },
  {
    name: '服务蓝图',
    content: '帮助您快速梳理和验证商业模式',
    type: 'BluePrint',
    englishName: 'Service Blueprint',
    englishContent: 'Helping You Quickly Organize and Validate Your Business Model',
  },
  {
    type: 'UserJourney',
    name: '用户旅程地图',
    englishName: 'Customer Journey Map',
    content: '追踪用户与产品或服务互动的全过程',
    englishContent: 'Helping You Quickly Organize and Validate Your Business Model',
  },
];

/**
 * 支持链接
 */
export const SUPPORT_URL = 'https://support.qq.com/products/134728/';

/**
 * 官网地址
 */
export const OFFICAL_URL = 'https://codesign.qq.com/app/events/explore';

/**
 * 最大历史会话数
 */
export const MAX_HISTORY_COUNT = 10;

/**
 * HTML TO DESIGN 默认导入网址
 */
export const DEFAULT_IMPORT_URL = 'https://codesign.qq.com/download';

/**
 * HTML TO DESIGN 画框宽度
 */
export const DEFAULT_FRAME_WIDTH = {
  desktop: '1440',
  ipadpro: '1024',
  iphone: '375',
  android: '360',
  custom: '1920',
};
