import { HtmlParser, IInitOptions, ImageHashInfo } from '@tencent/h2d-html-parser';
import { messages as figmaMessages } from '@worker/index';

import { FigmaResult } from '@ai-assitant/ai-core';

const htmlParser = new HtmlParser({
  apiUrl: import.meta.env.VITE_PLUGIN_SERVER_URL + '/api/plugin',
  source: 'figma',
  getImageHash: (args) => {
    return figmaMessages['get-image-hash'].request(args) as Promise<FigmaResult<ImageHashInfo>>;
  },
  getVideoHash: (args) => {
    return figmaMessages['get-video-hash'].request(args) as Promise<FigmaResult<string>>;
  },
  createDesign: (args) => {
    return figmaMessages['create-design'].request(args) as Promise<string>;
  },
  createDesignCollect: (isCreateAssets) => {
    figmaMessages['create-design-collect'].send({ isCreateAssets });
  },
});

export default htmlParser;
