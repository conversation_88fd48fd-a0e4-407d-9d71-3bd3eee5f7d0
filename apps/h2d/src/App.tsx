import React, { Suspense } from 'react';
import { useEventEmitter } from 'ahooks';
import { Loading } from 'tdesign-react';
import { MemoryRouter, useRoutes } from 'react-router-dom';
import { AppContext } from '@ui/context';
import Policy from '@ui/components/policy';
import Help from '@ui/components/help';
import UserCenter from '@ui/components/user-center';
import routers from './router';
import 'tdesign-react/es/style/index.css';
import 'virtual:svg-icons-register';
import './locale';
import './styles/index.less';
import './styles/main.css';
import './styles/custom.css';
import useSocket from './hooks/useSocket';

const AppRouter = () => {
  return (
    <Suspense
      fallback={
        <div className="loading">
          <Loading />
        </div>
      }
    >
      {useRoutes(routers)}
    </Suspense>
  );
};

const App = () => {
  return (
    <MemoryRouter initialEntries={['/url']}>
      <AppContext.Provider
        value={{
          $focusInput: useEventEmitter(),
          $retry: useEventEmitter(),
          $upload: useEventEmitter(),
          $socket: useSocket(),
        }}
      >
        <AppRouter />
        <Policy />
        <Help />
        <UserCenter />
      </AppContext.Provider>
    </MemoryRouter>
  );
};

export default App;
