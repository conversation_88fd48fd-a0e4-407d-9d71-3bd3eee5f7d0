import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Checkbox, Input, MessagePlugin, Tooltip } from 'tdesign-react';
import { HelpCircleIcon } from 'tdesign-icons-react';

import { FigmaResult, MessageTypes, PlatformUser } from '@ai-assitant/ai-core';
import SvgIcon from '@ui/components/svgIcon';
import { useFamilyStore } from '@ui/store';
import track from '@ui/utils/track';

import PageContainer from '@ui/components/page-container';
import htmlParser from '@ui/utils/html-parser';
import { messages } from '@worker/index';
import SetMissingFamily from './components/set-missing-family';
import Stage from '@ui/components/stage';
import ScrollList from '@ui/components/scroll-list';
import StageInfo from './components/stage-info';
import StageTask from './components/stage-task';
import useUrlImport from './hooks/useUrlImport';
import useUrlStage from './hooks/useUrlStage';

import Style from './index.module.less';

import imgFlag from '@ui/assets/flag.gif';

const URLToDesign = () => {
  const { t } = useTranslation();

  const {
    url,
    inputStatus,
    inputTips,
    handleInputChange,
    themes,
    handleSetThemes,
    preferences,
    handleSetPreferences,
    frameObj,
    setCustomFrameObj,
    frameWidths,
    handleSetFrameWidths,
  } = useUrlImport();

  const { showStage, setShowStage, stageManager, initStageManager, clear } = useUrlStage();

  useEffect(() => {
    return () => {
      clear();
    };
  }, []);

  const { familyMap } = useFamilyStore();

  const handleImport = async () => {
    if (inputStatus === 'error') {
      return;
    }

    const tasks = initTasks();
    initStageManager(tasks);
    setShowStage(true);
  };

  /**
   * 生成解析任务
   * @returns
   */
  const initTasks = () => {
    const tasks = [];

    for (let i = 0; i < frameWidths.length; i++) {
      for (let j = 0; j < themes.length; j++) {
        const frameWidth = frameWidths[i];
        if (frameWidth && frameObj[frameWidth]) {
          const taskName = getTaskName(frameObj[frameWidth], themes[j] || '', url || '');
          tasks.push(taskName);
        }
      }
    }

    return tasks;
  };

  const [showTitle, setShowTitle] = useState(true);
  const [showDesc, setShowDesc] = useState(true);

  useEffect(() => {
    if (showStage) {
      runTask();
      setShowTitle(false);
      setShowDesc(false);
    } else {
      setShowTitle(true);
      setShowDesc(true);
    }
  }, [showStage]);

  function getTaskName(viewport: string, theme: string, url: string) {
    return `${Number(viewport)}x${theme}: ${url}`;
  }

  /**
   * 运行导入网站解析任务
   */
  const runTask = async () => {
    const result = (await messages[MessageTypes.GET_PLATFORM_USER].request({})) as FigmaResult<PlatformUser>;

    if (familyMap === null) {
      console.error('familyMap is null');
      return;
    }

    if (stageManager === null) {
      console.error('stageManager is null');
      return;
    }

    if (result.success && result.data) {
      const id = Date.now();
      const pageList = [];
      for (let i = 0; i < frameWidths.length; i++) {
        for (let j = 0; j < themes.length; j++) {
          const frameWidth = frameWidths[i];
          if (frameWidth && frameObj[frameWidth]) {
            const viewport = Number(frameObj[frameWidth]);
            const theme = themes[j] || '';
            const taskName = getTaskName(frameObj[frameWidth], theme, url || '');
            pageList.push({ viewport, theme, taskName });
          }
        }
      }

      for (const item of pageList) {
        const index = pageList.indexOf(item);
        const lastIndex = index === pageList.length - 1;
        track.send('get.content.url', url);
        track.send('get.content.theme', item.theme);
        track.send('get.content.viewport', item.viewport.toString());
        try {
          await htmlParser.create(
            {
              url: url,
              id: id,
              pageParams: {
                viewport: item.viewport,
                theme: item.theme,
                availableFamily: familyMap,
                isCreateCollectAssets: lastIndex && preferences.includes('assets'),
                isUseAutoLayout: preferences.includes('autoLayout'),
                isScreenShot: false, //需要对比生成效果时打开
              },
            },
            (data: any) => {
              if (data.status === 'Error') {
                stageManager?.emit('ERROR', item.taskName, data.error.message || data.error);
              } else {
                stageManager?.emit('STEP', item.taskName, data.stepName, data.progress);
              }
            },
          );
        } catch (e) {
          console.error('生成异常', e);
        }
      }
    }
  };

  return (
    <>
      <PageContainer page="inspire" activeTab="url" showTitle={showTitle} showDesc={showDesc}>
        <ScrollList flex={true} padding={'0px'}>
          {!showStage && (
            <div className={Style.inspireHtml}>
              <div className={Style.pageItemContent}>
                <img src={imgFlag} className={Style.inspireHtmlTag} />
                <div className={Style.pageItemBox}>
                  <div className={Style.inspireHtmlUrl}>
                    <span>{t('inspire.html.tip')}</span>
                    <Tooltip content={t('inspire.html.help')}>
                      <HelpCircleIcon style={{ color: '#61f29f' }} />
                    </Tooltip>
                  </div>
                  <div className={Style.inspireHtmlUrlRemark}>{t('inspire.html.remark')}</div>
                  <div className={Style.inspireHtmlInput}>
                    <Input
                      className="assitant-link-input"
                      value={url}
                      onChange={(value) => handleInputChange(value)}
                      status={inputStatus}
                      tips={inputTips}
                    />
                  </div>
                </div>
              </div>
              {/* 画框宽度 */}
              <div className={Style.pageItemTitle}>
                <span>{t('inspire.html.viewports.title')}</span>
              </div>
              <div className={Style.pageItemBox}>
                <div className={Style.inspireHtmlViewports}>
                  <Checkbox.Group style={{ display: 'block' }} value={frameWidths} onChange={handleSetFrameWidths}>
                    <div className={Style.settingLine}>
                      <Checkbox value="desktop" key="desktop">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-pc" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.Desktop')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <div className={Style.settingLineItemLabel}>{frameObj.desktop} px</div>
                    </div>
                    <div className={Style.settingLine}>
                      <Checkbox value="ipadpro" key="ipadpro">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-ipad" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.IpadPro')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <div className={Style.settingLineItemLabel}>{frameObj.ipadpro} px</div>
                    </div>
                    <div className={Style.settingLine}>
                      <Checkbox value="iphone" key="iphone">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-iphone" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.Iphone')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <div className={Style.settingLineItemLabel}>{frameObj.iphone} px</div>
                    </div>
                    <div className={Style.settingLine}>
                      <Checkbox value="android" key="android">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-android" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.Android')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <div className={Style.settingLineItemLabel}>{frameObj.android} px</div>
                    </div>

                    <div className={Style.settingLine}>
                      <Checkbox value="custom" key="custom">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-custom" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.Custom')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <Input
                        suffix="px"
                        autoWidth
                        value={frameObj.custom}
                        onChange={(value) => setCustomFrameObj(value)}
                      />
                    </div>
                  </Checkbox.Group>
                </div>
              </div>
              {/* 主题设置 */}
              <div className={Style.pageItemTitle}>
                <span>{t('inspire.html.themes.title')}</span>
              </div>
              <div className={Style.pageItemBox}>
                <Checkbox.Group value={themes} onChange={handleSetThemes} style={{ display: 'block' }}>
                  <div className={Style.settingLine}>
                    <Checkbox value="light" key="light">
                      <div className={Style.settingLineItem}>
                        <div className={Style.settingLineItemLabel}>
                          <SvgIcon name="icon-light" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                          <span>{t('inspire.html.themes.light')}</span>
                        </div>
                      </div>
                    </Checkbox>
                  </div>
                  <div className={Style.settingLine}>
                    <Checkbox value="dark" key="dark">
                      <div className={Style.settingLineItem}>
                        <div className={Style.settingLineItemLabel}>
                          <SvgIcon name="icon-dark" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                          <span>{t('inspire.html.themes.dark')}</span>
                        </div>
                      </div>
                    </Checkbox>
                  </div>
                </Checkbox.Group>
              </div>
              {/* 生成偏好设置 */}
              <div className={Style.pageItemTitle}>
                <span>{t('inspire.html.preferences.title')}</span>
              </div>
              <div className={Style.pageItemBox}>
                <Checkbox.Group value={preferences} onChange={handleSetPreferences} style={{ display: 'block' }}>
                  <div className={Style.settingLine}>
                    <Checkbox value="assets" key="assets">
                      <div className={Style.settingLineItem}>
                        <div className={Style.settingLineItemLabel}>
                          <span>{t('inspire.html.preferences.desc')}</span>
                        </div>
                      </div>
                    </Checkbox>
                  </div>
                  <div className={Style.settingLine}>
                    <Checkbox value="autoLayout" key="autoLayout">
                      <div className={Style.settingLineItem}>
                        <div className={Style.settingLineItemLabel}>
                          <span>{t('inspire.html.preferences.useAutoLayout')}</span>
                        </div>
                      </div>
                    </Checkbox>
                  </div>
                </Checkbox.Group>
              </div>
              <div className={Style.inspireHtmlFooter}>
                <div className={Style.inspireBlockBtn} onClick={handleImport}>
                  {t('inspire.html.import')}
                </div>
              </div>
            </div>
          )}
          <Stage
            info={
              <StageInfo
                type={'url'}
                loadingPage={url}
                viewports={frameWidths.join(' / ')}
                themes={themes.join(' / ')}
              />
            }
            task={<StageTask />}
          />
        </ScrollList>
      </PageContainer>
      <SetMissingFamily />
    </>
  );
};

export default memo(URLToDesign);
