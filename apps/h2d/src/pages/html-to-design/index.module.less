@import '@ui/styles/vars.less';

.contentContainer {
  height: 800px;
  margin-top: 10px;
}

.pageItemTitle {
  margin-top: 16px;
  margin-bottom: 12px;

  color: #ffffff;

  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
}

.pageItemTitleInline {
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ffffff;
  margin-top: 10px;

  .title {
    position: absolute;
    top: auto;
  }
}


.pageItemContent {
  position: relative;
  margin-top: 24px;
}

.pageItemBox {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  padding: 16px;
  border: solid 1px @border-color-1;
  border-radius: 8px;
  background-color: @bg-color-1;
  font-size: 14px;
  font-weight: 500;
  gap: 10px;
}

.inspireHtml {
  padding: 20px 0;

  &Tag {
    position: absolute;
    z-index: 1;
    top: -54px;
    right: 0px;

    height: 60px;
  }

  &Url {
    display: flex;
    align-items: center;
    flex-direction: row;

    color: #ffffff;

    font-size: 14px;
    line-height: 22px;

    gap: 12px;

    &Remark {
      color: #ffffff;

      font-size: 11px;
    }
  }

  &Input {
    width: 100%;

    input {
      box-sizing: border-box;
      width: 100%;
      height: 32px;

      resize: none;

      color: #ffffff;
    }

    :global(.t-input__inner::placeholder) {
      color: @primary-color;
    }
  }

  &Viewports {
    :global(.t-input__wrap .t-input) {
      max-width: 70px;
      height: 26px;
      padding: 2px 8px;

      border: 1px solid @primary-color;
      border-radius: 4px;
      background: transparent;
      box-shadow: none;
    }

    :global(.t-input__wrap .t-input__inner) {
      color: @primary-color;

      font-family: 'PingFang SC';
      font-size: 14px;
      font-weight: 600;
      line-height: 22px;
    }

    :global(.t-input__wrap .t-input__suffix) {
      margin-left: 3px;

      color: @primary-color;
    }
  }

  &Footer {
    display: flex;
    align-items: center;
    justify-content: center;

    margin-top: 28px;

    .inspireBlockBtn {
      display: flex;
      align-items: center;
      justify-content: center;

      width: 200px;
      height: 44px;

      cursor: pointer;

      color: #000000;
      border-radius: 8px;
      background-color: @primary-color;

      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
    }
  }
}

.settingLine {
  display: flex;
  align-items: center;
  justify-content: space-between;

  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  &ItemLabel {
    display: flex;
    align-items: center;

    padding-right: 8px;

    color: @text-color-1;

    font-family: 'PingFang SC';
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;

    gap: 8px;
  }
}


.inspireHtmlTitle {
  /* Frame 1000004891 */
  box-sizing: border-box;
  /* Auto layout */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 16px;
  gap: 10px;
  font-size: 12px;
  height: 30px;
  color: @text-color-1;
  left: calc(50% - 380px / 2);
  border-radius: 8.2px 8.2px 0px 0px;
  background: rgba(97, 242, 159, 0.12);
  border: 1px solid rgba(229, 231, 244, 0.1);

  img {
    display: inline-block;
    vertical-align: middle;
    margin-right: 8px;
  }
}

.pageItemBox2 {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  height: auto;
  padding: 0 0 16px 0;
  border: solid 1px @border-color-1;
  border-radius: 8px;
  background-color: @bg-color-1;
  font-size: 14px;
  font-weight: 500;
  gap: 10px;

  .htmlItem {
    height: 180px;
  }

  .cssItem {
    height: 140px;
  }
}

.textArea {
  background-color: rgba(0, 0, 0, 0);
  border: none;
  caret-color: white; /* 光标变为白色 */
  resize: none;
  color: #E5E7F4;
  overflow-y: auto; /* 确保滚动条可见 */
  padding-right: 4px; /* 向左移动滚动条 */

  &::-webkit-scrollbar {
    width: 4px; /* 滚动条宽度 */

  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3); /* 滚动条滑块颜色 */
    border-radius: 3px; /* 滑块圆角 */
  }

  &::-webkit-scrollbar-track {
    background-color: transparent; /* 滚动条轨道透明 */
  }

  .html {
    min-height: 110px;
  }

  .css {
    min-height: 90px;
  }
}

.textArea:focus {
  box-shadow: none; /* 保持与默认一致 */
  outline: none; /* 移除轮廓线 */
}

.notice {
  padding: 0 16px;
  white-space: pre-line;
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
  color: rgba(255, 255, 255, 0.8);
}

.upload {
  :global(.t-upload__trigger) {
    width: 100%;
    height: 100%;
    display: flex;
    text-align: center; /* 水平居中 */
    align-items: center;
    justify-content: center;
  }

  :global(.t-upload__single-name) {
    color: #FFF !important;
  }

  :global(.t-upload__dragger) {
    align-items: center;
    text-align: center;
    justify-content: center; /* 水平居中 */
    border: 1px dashed @primary-color;
    padding: 0;
    color: #FFF;
    margin-top: 20px;
    width: 100%;
  }

  :global(.t-upload--highlight) {
    color: @primary-color;
  }

  :global(.t-link--theme-primary) {
    color: @primary-color;
  }

  :global(.t-link--theme-primary:hover) {
    color: @primary-color;
  }

  :global(.t-upload__dragger-progress-cancel) {
    color: #FFF;
  }
}
