import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Textarea } from 'tdesign-react';
import { useFamilyStore } from '@ui/store';
import PageContainer from '@ui/components/page-container';
import SetMissingFamily from './components/set-missing-family';
import Stage from '@ui/components/stage';
import ScrollList from '@ui/components/scroll-list';
import StageInfo from './components/stage-info';
import StageTask from './components/stage-task';
import useUrlStage from './hooks/useUrlStage';
import Style from './index.module.less';
import imgFlag from '@ui/assets/flag.gif';
import useTextImport from '@ui/pages/html-to-design/hooks/useTextImport';

const HTMLToDesign = () => {
  const { t } = useTranslation();

  const {
    text,
    inputStatus,
    inputTips,
    handleTextInputChange,
    themes,
    handleSetThemes,
    preferences,
    handleSetPreferences,
    frameObj,
    setCustomFrameObj,
    frameWidths,
    handleSetFrameWidths,
  } = useTextImport();

  const { showStage, setShowStage, stageManager, initStageManager, clear } = useUrlStage();

  useEffect(() => {
    return () => {
      clear();
    };
  }, []);

  const { familyMap } = useFamilyStore();

  const [showTitle, setShowTitle] = useState(true);
  const [showDesc, setShowDesc] = useState(true);
  const handleJumpToPlugin = () => {};

  useEffect(() => {
    if (showStage) {
      setShowTitle(false);
      setShowDesc(false);
    } else {
      setShowTitle(true);
      setShowDesc(true);
    }
  }, [showStage]);

  return (
    <>
      <PageContainer page="inspire" activeTab="text" showTitle={showTitle} showDesc={showDesc}>
        <ScrollList flex={true} padding={'0px'}>
          {!showStage && (
            <div className={Style.contentContainer}>
              <div style={{ marginTop: '16px' }} className={Style.pageItemTitleInline}>
                <span className={Style.title}>{t('inspire.text.input.title')}</span>
              </div>
              <div className={Style.pageItemContent}>
                <img src={imgFlag} className={Style.inspireHtmlTag} />
                <div className={Style.pageItemBox2}>
                  <div className={Style.inspireHtmlTitle}>{t('inspire.text.input.help')}</div>
                  {/*提示词文本输入*/}
                  <Textarea className={Style.textArea} value={text} onChange={handleTextInputChange} />
                  <div className={Style.inspireHtmlFooter}>
                    <div className={Style.inspireBlockBtn} onClick={handleJumpToPlugin}>
                      {t('inspire.text.btn')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <Stage
            info={
              <StageInfo
                type={'text'}
                loadingPage={text}
                viewports={frameWidths.join(' / ')}
                themes={themes.join(' / ')}
              />
            }
            task={<StageTask />}
          />
        </ScrollList>
      </PageContainer>
      <SetMissingFamily />
    </>
  );
};

export default memo(HTMLToDesign);
