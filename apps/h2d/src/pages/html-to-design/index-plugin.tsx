import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useFamilyStore } from '@ui/store';
import { parse } from 'zipson';
import { Upload, UploadFile } from 'tdesign-react';
import PageContainer from '@ui/components/page-container';
import SetMissingFamily from './components/set-missing-family';
import Stage from '@ui/components/stage';
import ScrollList from '@ui/components/scroll-list';
import StageInfo from './components/stage-info';
import StageTask from './components/stage-task';
import Style from './index.module.less';
import imgFlag from '@ui/assets/flag.gif';
import browserExtension from '@ui/assets/browser-extension.svg';
import htmlParser from '@ui/utils/html-parser';
import usePluginImport from '@ui/pages/html-to-design/hooks/usePluginImport';
import usePluginStage from '@ui/pages/html-to-design/hooks/usePluginStage';
import clsx from 'clsx';
import { UploadConfig } from 'tdesign-react/es/config-provider/type';

const BrowserExtensionToDesign = () => {
  const { t } = useTranslation();
  const [devMod, setDevMod] = useState(true);
  const { url, setUrl, data, setData, themes, frameWidths } = usePluginImport();

  const { showStage, setShowStage, stageManager, initStageManager, clear } = usePluginStage();
  const [showTitle, setShowTitle] = useState(true);
  const [showDesc, setShowDesc] = useState(true);

  const uploadLocal: UploadConfig = {
    triggerUploadText: {
      normal: t('upload.normal'),
    },
    dragger: {
      clickAndDragText: t('upload.clickAndDragText'),
      dragDropText: t('upload.dragDropText'),
      draggingText: t('upload.draggingText'),
    },
  };
  const { familyMap } = useFamilyStore();

  useEffect(() => {
    if (showStage) {
      runTask();
      setShowTitle(false);
      setShowDesc(false);
    } else {
      setShowTitle(true);
      setShowDesc(true);
    }
  }, [showStage]);
  useEffect(() => {
    return () => {
      clear();
    };
  }, []);

  const handleInstallBrowserExtension = async () => {
    window.open('https://doc.weixin.qq.com/doc/w3_ATsAbwaeAPICNycM5S4zKR1Kwp6LI?scode=AJEAIQdfAAoFuLSHFJ');
  };

  /**
   * 运行导入网站解析任务
   */
  const runTask = async () => {
    if (familyMap === null) {
      console.error('familyMap is null');
      return;
    }

    if (stageManager === null) {
      console.error('stageManager is null');
      return;
    }
    for (let i = 0; i < data.length; i++) {
      const item = { ...data[i], availableFamily: familyMap };
      await htmlParser.createByDocJson(item, (data) => {
        if (data.status === 'Error') {
          stageManager?.emit(
            'ERROR',
            getTaskName(item.viewport, item.theme, item.doc.baseURI),
            data.error.message || data.error,
          );
        } else {
          stageManager?.emit(
            'STEP',
            getTaskName(item.viewport, item.theme, item.doc.baseURI),
            data.stepName,
            data.progress,
          );
        }
      });
    }
  };

  const handleUploadFiles = async (file: UploadFile) => {
    if (file.raw !== undefined) {
      const reader = new FileReader();
      reader.readAsText(file.raw);
      reader.onload = (e) => {
        const res = devMod
          ? (JSON.parse(e.target?.result as string) as Array<any>)
          : (parse(e.target?.result as string) as Array<any>);
        if (res) {
          if (res.length > 0) {
            setData(res);
            setUrl(res[0].doc.baseURI);
            const tasks = initTasks(res);
            initStageManager(tasks);
            setShowStage(true);
          }
        }
      };
      reader.onerror = (e) => {
        console.log(e);
      };
    }
    return false;
  };
  /**
   * 生成解析任务
   * @returns
   */
  const initTasks = (res: Array<any>) => {
    const tasks = [];

    for (let i = 0; i < res.length; i++) {
      const taskName = getTaskName(res[i].viewport, res[i].theme, res[i].doc.baseURI);
      tasks.push(taskName);
    }

    return tasks;
  };

  function getTaskName(viewport: string, theme: string, url: string) {
    return `${Number(viewport)}x${theme}: ${url}`;
  }

  return (
    <>
      <PageContainer page="inspire" activeTab="plugin" showTitle={showTitle} showDesc={showDesc}>
        <ScrollList flex={true} padding={'0px'}>
          {!showStage && (
            <div className={Style.contentContainer}>
              <div className={Style.pageItemTitleInline}>
                <span style={{ position: 'absolute', top: 'auto' }}>{t('inspire.browser.title')}</span>
              </div>
              <div className={Style.pageItemContent}>
                <img src={imgFlag} className={Style.inspireHtmlTag} />
                <div className={Style.pageItemBox2}>
                  <div className={Style.inspireHtmlTitle}>
                    <img style={{ position: 'absolute' }} src={browserExtension} />
                    <p style={{ marginLeft: '26px' }}>{t('inspire.browser.help')}</p>
                  </div>
                  <div className={Style.notice}>{t('inspire.browser.notice')}</div>
                  <div className={clsx(Style.inspireHtmlFooter, '!mt-2')}>
                    <div className={Style.inspireBlockBtn} onClick={handleInstallBrowserExtension}>
                      {t('inspire.browser.btn')}
                    </div>
                  </div>
                </div>
              </div>
              <Upload
                action={''}
                className={Style.upload}
                beforeUpload={handleUploadFiles}
                locale={uploadLocal}
                draggable
                autoUpload={false}
                showUploadProgress
                theme="file"
                useMockProgress
              />
            </div>
          )}
          <Stage
            info={
              <StageInfo
                type={'plugin'}
                loadingPage={url}
                viewports={frameWidths.join(' / ')}
                themes={themes.join(' / ')}
              />
            }
            task={<StageTask />}
          />
        </ScrollList>
      </PageContainer>
      <SetMissingFamily />
    </>
  );
};

export default memo(BrowserExtensionToDesign);
