import { useState } from 'react';
import { InputNumberValue, TdInputProps } from 'tdesign-react';
import { useTranslation } from 'react-i18next';

import { AnyObject } from '@ai-assitant/ai-core';
import { DEFAULT_FRAME_WIDTH } from '@ui/utils/constants';

export default function useHtmlImport() {
  const { t } = useTranslation();
  /**
   * 导入HTML
   */
  const [html, setHTML] = useState('');
  /**
   * 导入CSS
   */
  const [css, setCSS] = useState('');

  /**
   * 输入框状态
   */
  const [inputStatus, setInputStatus] = useState<TdInputProps['status']>('default');

  /**
   * 输入框提示信息
   */
  const [inputTips, setInputTips] = useState('');

  /**
   * 主题
   */
  const [themes, setThemes] = useState<string[]>(['light']);

  const handleSetThemes = (values: string[]) => {
    if (values.length > 0) {
      setThemes(values);
    } else {
      setThemes(themes);
    }
  };

  /**
   * 输入框值改变事件
   * @param value
   */
  const handleHtmlInputChange = (value: string) => {
    setHTML(value);
    //TODO HTML注入预防

    // if (isValidHttpsUrl(value) && regex.test(value)) {
    //   setInputTips(t('NotAllowTransition'));
    //   setInputStatus('error');
    // } else if (!isValidHttpsUrl(value)) {
    //   setInputTips(t('NotValidUrl'));
    //   setInputStatus('error');
    // } else {
    //   setInputTips('');
    //   setInputStatus('default');
    // }
  };

  /**
   * 输入框值改变事件
   * @param value
   */
  const handleCssInputChange = (value: string) => {
    setCSS(value);
    //TODO CSS注入预防

    // if (isValidHttpsUrl(value) && regex.test(value)) {
    //   setInputTips(t('NotAllowTransition'));
    //   setInputStatus('error');
    // } else if (!isValidHttpsUrl(value)) {
    //   setInputTips(t('NotValidUrl'));
    //   setInputStatus('error');
    // } else {
    //   setInputTips('');
    //   setInputStatus('default');
    // }
  };
  /**
   * 偏好设置
   */
  const [preferences, setPreferences] = useState<string[]>([]);

  const handleSetPreferences = (values: string[]) => {
    setPreferences(values);
  };

  /**
   * 画框宽度对象
   */
  const [frameObj, setFrameObj] = useState<AnyObject>(DEFAULT_FRAME_WIDTH);

  /**
   * 设置自定义画框宽度
   * @param key
   * @param value
   */
  function setCustomFrameObj(value: InputNumberValue) {
    setFrameObj({ ...frameObj, custom: String(value || 100) });
  }

  /**
   * 选中画框宽度
   */
  const [frameWidths, setFrameWidths] = useState<string[]>(['custom']);

  /**
   * 设置画框宽度
   * @param values
   */
  const handleSetFrameWidths = (values: string[]) => {
    if (values.length > 0) {
      setFrameWidths(values);
    } else {
      setFrameWidths(frameWidths);
    }
  };

  return {
    html,
    css,
    inputStatus,
    inputTips,
    handleHtmlInputChange,
    handleCssInputChange,
    themes,
    handleSetThemes,
    preferences,
    handleSetPreferences,
    frameObj,
    setCustomFrameObj,
    frameWidths,
    handleSetFrameWidths,
  };
}
