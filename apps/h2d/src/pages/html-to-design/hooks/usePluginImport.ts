import { useState } from 'react';
import { InputNumberValue, TdInputProps } from 'tdesign-react';
import { useTranslation } from 'react-i18next';

import { AnyObject } from '@ai-assitant/ai-core';
import { DEFAULT_FRAME_WIDTH } from '@ui/utils/constants';

export default function usePluginImport() {
  const { t } = useTranslation();
  /**
   * 导入网址
   */
  const [url, setUrl] = useState('');
  /**
   * 导入数据
   */
  const [data, setData] = useState<any[]>([]);
  /**
   * 输入框状态
   */
  const [inputStatus, setInputStatus] = useState<TdInputProps['status']>('default');

  /**
   * 输入框提示信息
   */
  const [inputTips, setInputTips] = useState('');

  /**
   * 主题
   */
  const [themes, setThemes] = useState<string[]>(['light']);

  const handleSetThemes = (values: string[]) => {
    if (values.length > 0) {
      setThemes(values);
    } else {
      setThemes(themes);
    }
  };

  /**
   * 偏好设置
   */
  const [preferences, setPreferences] = useState<string[]>([]);

  const handleSetPreferences = (values: string[]) => {
    setPreferences(values);
  };

  /**
   * 画框宽度对象
   */
  const [frameObj, setFrameObj] = useState<AnyObject>(DEFAULT_FRAME_WIDTH);

  /**
   * 设置自定义画框宽度
   * @param key
   * @param value
   */
  function setCustomFrameObj(value: InputNumberValue) {
    setFrameObj({ ...frameObj, custom: String(value || 100) });
  }

  /**
   * 选中画框宽度
   */
  const [frameWidths, setFrameWidths] = useState<string[]>(['custom']);

  /**
   * 设置画框宽度
   * @param values
   */
  const handleSetFrameWidths = (values: string[]) => {
    if (values.length > 0) {
      setFrameWidths(values);
    } else {
      setFrameWidths(frameWidths);
    }
  };

  return {
    url,
    setUrl,
    data,
    setData,
    inputStatus,
    inputTips,
    themes,
    handleSetThemes,
    preferences,
    handleSetPreferences,
    frameObj,
    setCustomFrameObj,
    frameWidths,
    handleSetFrameWidths,
  };
}
