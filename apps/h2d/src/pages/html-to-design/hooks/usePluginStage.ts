import { useStageStore } from '@ui/store';
import { StageManager } from '@ui/components/stage/stageManager';

// 解析步骤一
export const PARSER_STEP_0 = 'parse data';

// 解析步骤二
export const PARSER_STEP_1 = 'upack assets';

// 解析步骤三
export const PARSER_STEP_2 = 'html to dsl';

// 解析步骤四
export const PARSER_STEP_3 = 'dsl to design';

export default function usePluginStage() {
  const { showStage, setShowStage, stageManager, setStageManager, clear } = useStageStore();

  /**
   * 初始化进度管理
   * @returns
   */
  const initStageManager = (tasks: string[]): StageManager => {
    // 解析步骤
    const steps = [PARSER_STEP_0, PARSER_STEP_1, PARSER_STEP_2, PARSER_STEP_3];

    const manager = new StageManager({
      tasks,
      steps,
    });
    setStageManager(manager);
    return manager;
  };

  return {
    showStage,
    setShowStage,
    stageManager,
    initStageManager,
    clear,
  };
}
