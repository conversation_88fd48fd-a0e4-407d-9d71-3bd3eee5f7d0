import { FigmaResult, MessageTypes, PlatformUser } from '@ai-assitant/ai-core';
import { Checkbox, Input, Textarea } from 'tdesign-react';
import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useFamilyStore } from '@ui/store';
import { messages } from '@worker/index';
import PageContainer from '@ui/components/page-container';
import htmlParser from '@ui/utils/html-parser';
import SetMissingFamily from './components/set-missing-family';
import Stage from '@ui/components/stage';
import ScrollList from '@ui/components/scroll-list';
import StageInfo from './components/stage-info';
import StageTask from './components/stage-task';
import useHtmlImport from './hooks/useHtmlImport';
import useHtmlStage from './hooks/useHtmlStage';
import SvgIcon from '@ui/components/svgIcon';
import Style from './index.module.less';
import imgFlag from '@ui/assets/flag.gif';
import clsx from 'clsx';

const HTMLToDesign = () => {
  const { t } = useTranslation();

  const {
    html,
    css,
    inputStatus,
    handleHtmlInputChange,
    handleCssInputChange,
    themes,
    preferences,
    frameObj,
    setCustomFrameObj,
    frameWidths,
    handleSetFrameWidths,
  } = useHtmlImport();

  const { showStage, setShowStage, stageManager, initStageManager, clear } = useHtmlStage();

  useEffect(() => {
    return () => {
      clear();
    };
  }, []);

  const { familyMap } = useFamilyStore();

  const handleImport = async () => {
    if (inputStatus === 'error') {
      return;
    }
    if (html.trim() === '') {
      return;
    }
    const tasks = initTasks();
    initStageManager(tasks);
    setShowStage(true);
  };

  /**
   * 生成解析任务
   * @returns
   */
  const initTasks = () => {
    const tasks = [];
    for (let i = 0; i < frameWidths.length; i++) {
      for (let j = 0; j < themes.length; j++) {
        const frameWidth = frameWidths[i];
        if (frameWidth && frameObj[frameWidth]) {
          const taskName = getTaskName(frameObj[frameWidth], themes[j] || '', 'html' + i);
          tasks.push(taskName);
        }
      }
    }
    return tasks;
  };

  const [showTitle, setShowTitle] = useState(true);
  const [showDesc, setShowDesc] = useState(true);

  useEffect(() => {
    if (showStage) {
      runTask();
      setShowTitle(false);
      setShowDesc(false);
    } else {
      setShowTitle(true);
      setShowDesc(true);
    }
  }, [showStage]);

  function getTaskName(viewport: string, theme: string, url: string) {
    return `${Number(viewport)}x${theme}: ${url}`;
  }

  /**
   * 运行导入网站解析任务
   */
  const runTask = async () => {
    const result = (await messages[MessageTypes.GET_PLATFORM_USER].request({})) as FigmaResult<PlatformUser>;

    if (familyMap === null) {
      console.error('familyMap is null');
      return;
    }

    if (stageManager === null) {
      console.error('stageManager is null');
      return;
    }

    if (result.success && result.data) {
      const id = Date.now();
      const pageList = [];
      for (let i = 0; i < frameWidths.length; i++) {
        for (let j = 0; j < themes.length; j++) {
          const frameWidth = frameWidths[i];
          if (frameWidth && frameObj[frameWidth]) {
            const viewport = Number(frameObj[frameWidth]);
            const theme = themes[j] || '';
            const taskName = getTaskName(frameObj[frameWidth], theme, 'html' + i);
            pageList.push({ viewport, theme, taskName });
          }
        }
      }

      for (const item of pageList) {
        const index = pageList.indexOf(item);
        const lastIndex = index === pageList.length - 1;
        await htmlParser.create(
          {
            name: 'HTML',
            html: html,
            css: css,
            id: id,
            pageParams: {
              viewport: item.viewport,
              theme: item.theme,
              availableFamily: familyMap,
              isCreateCollectAssets: lastIndex && preferences.includes('assets'),
              isUseAutoLayout: preferences.includes('autoLayout'),
              isScreenShot: false, //需要对比生成效果时打开
            },
          },
          (data) => {
            if (data.status === 'Error') {
              stageManager?.emit('ERROR', item.taskName, data.error.message || data.error);
            } else {
              stageManager?.emit('STEP', item.taskName, data.stepName, data.progress);
            }
          },
        );
      }
    }
  };

  return (
    <>
      <PageContainer page="inspire" activeTab="html" showTitle={showTitle} showDesc={showDesc}>
        <ScrollList flex={true} padding={'0px'}>
          {!showStage && (
            <div className={Style.contentContainer}>
              {/* HTML */}
              <div className={Style.pageItemTitleInline}>
                <span style={{ position: 'absolute', top: 'auto' }}>HTML</span>
              </div>
              <div className={Style.pageItemContent}>
                <img src={imgFlag} className={Style.inspireHtmlTag}  alt=''/>
                <div className={clsx(Style.pageItemBox2, Style.htmlItem)}>
                  <div className={Style.inspireHtmlTitle}>{t('inspire.html.input.html')}</div>
                  {/*HTML文本输入*/}
                  <Textarea
                    spellCheck={false}
                    className={clsx(Style.textArea, Style.html)}
                    autosize={{ maxRows: 5, minRows: 5 }}
                    value={html}
                    onChange={handleHtmlInputChange}
                  />
                </div>
              </div>
              {/* CSS */}
              <div style={{ marginTop: '16px' }} className={clsx(Style.pageItemTitleInline)}>
                <span>CSS</span>
              </div>
              <div style={{ marginTop: '12px' }} className={Style.pageItemContent}>
                <div className={clsx(Style.pageItemBox2, Style.cssItem)}>
                  <div className={Style.inspireHtmlTitle}>{t('inspire.html.input.css')}</div>
                  {/*CSS文本输入*/}
                  <Textarea
                    spellCheck={false}
                    className={clsx(Style.textArea, Style.css)}
                    value={css}
                    autosize={{ maxRows: 3, minRows: 3 }}
                    onChange={handleCssInputChange}
                  />
                </div>
              </div>
              {/* 画框宽度 */}
              <div className={Style.pageItemTitle}>
                <span>{t('inspire.html.viewports.title')}</span>
              </div>
              <div className={Style.pageItemBox}>
                <div className={Style.inspireHtmlViewports}>
                  <Checkbox.Group style={{ display: 'block' }} value={frameWidths} onChange={handleSetFrameWidths}>
                    <div className={Style.settingLine}>
                      <Checkbox value="desktop" key="desktop">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-pc" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.Desktop')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <div className={Style.settingLineItemLabel}>{frameObj.desktop} px</div>
                    </div>
                    <div className={Style.settingLine}>
                      <Checkbox value="ipadpro" key="ipadpro">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-ipad" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.IpadPro')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <div className={Style.settingLineItemLabel}>{frameObj.ipadpro} px</div>
                    </div>
                    <div className={Style.settingLine}>
                      <Checkbox value="iphone" key="iphone">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-iphone" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.Iphone')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <div className={Style.settingLineItemLabel}>{frameObj.iphone} px</div>
                    </div>
                    <div className={Style.settingLine}>
                      <Checkbox value="android" key="android">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-android" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.Android')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <div className={Style.settingLineItemLabel}>{frameObj.android} px</div>
                    </div>

                    <div className={Style.settingLine}>
                      <Checkbox value="custom" key="custom">
                        <div className={Style.settingLineItem}>
                          <div className={Style.settingLineItemLabel}>
                            <SvgIcon name="icon-custom" iconStyle={{ width: '16px', height: '16px' }} hover={false} />
                            <span>{t('inspire.html.viewports.Custom')}</span>
                          </div>
                        </div>
                      </Checkbox>
                      <Input
                        suffix="px"
                        autoWidth
                        value={frameObj.custom}
                        onChange={(value) => setCustomFrameObj(value)}
                      />
                    </div>
                  </Checkbox.Group>
                </div>
              </div>
              <div className={Style.inspireHtmlFooter}>
                <div className={Style.inspireBlockBtn} onClick={handleImport}>
                  {t('inspire.html.import')}
                </div>
              </div>
            </div>
          )}
          <Stage
            info={
              <StageInfo
                type={'html'}
                loadingPage={html}
                viewports={frameWidths.join(' / ')}
                themes={themes.join(' / ')}
              />
            }
            task={<StageTask />}
          />
        </ScrollList>
      </PageContainer>
      <SetMissingFamily />
    </>
  );
};

export default memo(HTMLToDesign);
