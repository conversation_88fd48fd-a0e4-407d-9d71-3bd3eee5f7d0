import react from '@vitejs/plugin-react';
import path, { resolve } from 'path';
import { defineConfig, type UserConfigFnObject } from 'vite';
import { viteSingleFile } from 'vite-plugin-singlefile';
import { writeHtmlToDistPlugin } from '@ai-assitant/vite-plugin';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { mdPlugin } from './vite.md.plugin';
import { createRequire } from 'module';

const { version: currentVersion } = createRequire(import.meta.url)('./package.json');
console.log('📖 当前版本: v' + currentVersion);
export default defineConfig((() => {
  const buildConfig = {
    target: 'esnext',
    assetsInlineLimit: Infinity,
    chunkSizeWarningLimit: Infinity,
    cssCodeSplit: false,
    brotliSize: false,
    rollupOptions: {
      output: {
        input: path.resolve('./index.html'),
      },
    },
  };

  return {
    plugins: [
      react(),
      viteSingleFile(),
      writeHtmlToDistPlugin(),
      mdPlugin,
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'src/assets')], // svg地址
        symbolId: 'icon-[dir]-[name]',
      }),
    ],
    build: {
      ...buildConfig,
      emptyOutDir: false,
    },
    resolve: {
      alias: {
        '@worker': resolve(__dirname, './worker'),
        '@src': resolve(__dirname, './src'),
        '@ui': resolve(__dirname, './src'),
      },
    },
    define: {
      __APP_VERSION__: JSON.stringify(currentVersion),
    },
  };
}) as UserConfigFnObject);
