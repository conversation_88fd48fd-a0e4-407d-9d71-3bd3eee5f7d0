{"name": "design-genie", "version": "2.6.3", "type": "module", "scripts": {"build:code": "pnpm --filter @ai-assitant/ai-figma build:h2d-plugin", "dev": "pnpm build:code && vite dev && pnpm copy-manifest", "build": "pnpm build:code && vite build && pnpm copy-manifest", "copy-manifest": "cp manifest.json dist/"}, "dependencies": {"@ai-assitant/ai-bridge": "workspace:*", "@ai-assitant/ai-core": "workspace:*", "@ai-assitant/ai-figma": "workspace:*", "@tencent/h2d-html-parser": "workspace:*", "@figma/plugin-typings": "*", "@microsoft/fetch-event-source": "^2.0.1", "@react-spring/web": "^9.7.5", "autoprefixer": "^10.4.20", "better-scroll": "^2.5.1", "clipboard-polyfill": "^4.1.0", "clsx": "^2.1.1", "cos-js-sdk-v5": "^1.8.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "i18next": "^23.15.2", "jimp": "^1.6.0", "markdown-it": "^14.1.0", "postcss": "^8.4.49", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.1", "react-router-dom": "^6.26.0", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.15", "tdesign-icons-react": "^0.4.2", "tdesign-react": "^1.10.0", "textarea-caret": "^3.1.0", "typed.js": "^2.1.0", "zipson": "^0.2.12", "zustand": "^4.5.4"}, "devDependencies": {"@ai-assitant/typescript-config": "workspace:*", "@ai-assitant/vite-plugin": "workspace:*", "@babel/core": "^7.17.5", "@babel/preset-react": "^7.24.7", "@types/babel__core": "^7.1.18", "@types/crypto-js": "^4.2.2", "@types/markdown-it": "^14.1.2", "@types/node": "^22.13.14", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/textarea-caret": "^3.0.3", "@types/typed.js": "^2.0.9", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.1.1", "autoprefixer": "^10.4.20", "markdown-it-for-inline": "^2.0.1", "standard-version": "^9.5.0", "vite": "^6.2.4", "vite-plugin-dts": "^4.5.3", "vite-plugin-generate-file": "^0.2.0", "vite-plugin-html": "^3.0.6", "vite-plugin-markdown": "^2.2.0", "vite-plugin-singlefile": "^2.2.0", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^3.1.1"}, "engines": {"node": ">=14.14.0"}}